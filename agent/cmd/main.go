package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	gosync "sync"
	"syscall"

	c "github.com/tigergraph/cloud-universe/agent/internal"
	"github.com/tigergraph/cloud-universe/agent/internal/activeness"
	config "github.com/tigergraph/cloud-universe/agent/internal/config"
	kms "github.com/tigergraph/cloud-universe/agent/internal/kms"
	"github.com/tigergraph/cloud-universe/agent/internal/license"
	sync "github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user"
	"github.com/tigergraph/cloud-universe/controller/observability"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"golang.org/x/sync/errgroup"
)

func main() {
	log := logger.L()
	config.LoadConfig()
	go sync.New()
	go activeness.UpdateTopologyCron()
	go activeness.GetCatalogLastActiveTimeCron()
	go activeness.GetQueryLastActiveTimeCron()
	go license.UpdateLicenseExpirationCron()
	go c.Report()

	mainCtx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	if config.Config.EnableEncryption {
		go kms.InitKMSServer(mainCtx, fmt.Sprintf(":%v", config.Config.EncryptionPort), false)
	}

	g, gCtx := errgroup.WithContext(mainCtx)
	wg := &gosync.WaitGroup{}
	wg.Add(2)
	obs := observability.NewObserver(":24240")
	g.Go(func() error {
		defer wg.Done()
		if err := obs.Serve(); err != nil {
			logger.L().Fatalf("error serving observability:%v", err)
			return err
		}

		return nil
	})
	g.Go(func() error {
		defer wg.Done()
		<-gCtx.Done()
		log.Info("Received exit signal, sending an exit request to observability server...")
		return obs.Close()
	})

	wg.Wait()
}
