package activeness

import (
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

func MockShowProcessListAll(resp string) *httptest.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		cookie1 := &http.Cookie{Name: "TigerGraphApp", Value: "sample", HttpOnly: false}
		http.SetCookie(w, cookie1)
		io.WriteString(w, "")
	})
	mux.HandleFunc("/api/restpp/showprocesslistall", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp)
	})
	return httptest.NewServer(mux)
}

func TestGetQueriesTimeStamp(t *testing.T) {
	resp := `{
		"error": false,
		"message": "",
		"results": [
		  {
			"requestid": "15.RESTPP_1_1.1660165648633.N", 
			"startTime": "2022-08-10 21:07:28.633",
			"expirationTime": "2022-08-10 21:07:44.633", 
			"url": "/query/Social/rngExample",
			"elapsedTime": 3385
		  },
		  {
			"requestid": "65551.RESTPP_1_1.1660165648638.N",
			"startTime": "2022-08-10 21:07:28.638",
			"expirationTime": "2022-08-10 21:07:44.638",
			"url": "/query/Work_Net/rngExample",
			"elapsedTime": 3380
		  },
		  {
			"requestid": "131087.RESTPP_1_1.1660165648723.N",
			"startTime": "2022-08-10 21:07:28.723",
			"expirationTime": "2022-08-10 21:07:44.723",
			"url": "/query/Entity_Resolution/rngExample",
			"elapsedTime": 3295
		  }
		]
	  }`
	server := MockShowProcessListAll(resp)
	ts, err := GetQueriesTimeStamp(server.URL)
	require.NoError(t, err)
	require.NotNil(t, ts)
	server.Close()

	resp1 := `{
  "error": false,
  "message": "",
  "results": [
    {
      "elapsedTime": 6,
      "expirationTime": "2024-08-27 00:01:01.762",
      "requestid": "16974197.RESTPP_1_1.1724716801762.N",
      "startTime": "2024-08-27 00:00:01.762",
      "status": "running",
      "url": "/interpreted_query/ddd",
      "user": ""
    }
  ]
}`
	server1 := MockShowProcessListAll(resp1)
	ts1, err := GetQueriesTimeStamp(server1.URL)
	require.NoError(t, err)
	require.NotNil(t, ts1)
	server1.Close()
}

func TestGetQueriesTimeStampEmpty(t *testing.T) {
	resp := `{"error":false,"message":"","results":[]}`
	server := MockShowProcessListAll(resp)
	ts, err := GetQueriesTimeStamp(server.URL)
	require.NoError(t, err)
	require.Nil(t, ts)
	server.Close()
}

func TestGetQueriesTimeStampNoGraph(t *testing.T) {
	resp := `{"version":{"edition":"enterprise","api":"v2","schema":0},"error":true,"message":"Graph Schema is empty, engine could not run the request '/showprocesslistall', please create schema first.","code":"REST-1003"}`
	server := MockShowProcessListAll(resp)
	ts, err := GetQueriesTimeStamp(server.URL)
	require.NoError(t, err)
	require.Nil(t, ts)
	server.Close()
}
