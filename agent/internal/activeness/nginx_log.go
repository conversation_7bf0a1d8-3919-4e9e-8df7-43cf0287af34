package activeness

import (
	"bufio"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/utils/logger"
)

const (
	NginxFilePattern = "NGINX#.*\\.out"
)

// sample line:
// 127.0.0.1 - - [01/Oct/2019:00:38:19 +0000] "POST /ts3/api/datapoints HTTP/1.1" 201 0 "-" "Go-http-client/1.1"
func timeFromNginxLine(line string) *time.Time {
	l := strings.Index(line, "[")
	r := strings.Index(line, "]")
	if l < 0 || r < 0 {
		return nil
	}

	t := line[l+1 : r]

	layout := "02/Jan/2006:15:04:05 +0000"
	tm, err := time.Parse(layout, t)
	if err != nil {
		return nil
	}

	return &tm
}

// getLinesBetween returns lines from the specified file
// that START in range [startOffset, endOffset).
func getLinesBetween(file *os.File, startOffset, endOffset int64) (lines []string, err error) {
	// Discard the end of prev line. (Discard line at offset startOffset - 1).
	offset := startOffset - 1
	if offset < 0 {
		offset = 0
	}

	offset, err = file.Seek(offset, io.SeekStart)
	if err != nil {
		return nil, err
	}

	reader := bufio.NewReader(file)

	if startOffset > 0 {
		_, _, err = reader.ReadLine() // Skip until the next line.
		if err != nil {
			return nil, err
		}
	}

	for offset < endOffset {
		line, err := reader.ReadString(byte('\n')) // Use ReadString to include the \n for correct offset updating.
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}

		lines = append(lines, line)
		offset += int64(len(line)) // Seek(0, io.SeekCurrent) bugs in some cases.
	}

	return lines, nil
}

func getNginxLogsSortedByTimeDesc(dir string) ([]os.FileInfo, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	files := make([]fs.FileInfo, 0, len(entries))
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			return nil, err
		}
		files = append(files, info)
	}

	filtered := []os.FileInfo{}

	for _, f := range files {
		re := regexp.MustCompile(NginxFilePattern)
		if re.Match([]byte(f.Name())) {
			filtered = append(filtered, f)
		}
	}

	// Sort logs by time (last first).
	sort.Slice(filtered, func(a, b int) bool {
		return filtered[a].ModTime().After(filtered[b].ModTime())
	})

	return filtered, nil
}

func GetLatestNginxTimestamp(logDir string, clusterIPAddresses []string) (*time.Time, error) {
	log := logger.L()
	files, err := getNginxLogsSortedByTimeDesc(logDir)
	if err != nil {
		return nil, err
	}

	// This is temporary debugging code, will be removed when
	// the bug of test for latest timestamp is found and fixed.
	log.Info("Returned files: ")
	for _, file := range files {
		log.Info(file.Name())
	}

	// We exclude local and IPs within the cluster
	// We skip all internal local(127.0.0.1) requests since we only expose port 443 so all outside requests will hit App server and be logged.
	skippedPatterns := append(clusterIPAddresses, []string{"127.0.0.1", "HealthCheck", "/api/ping", "/informant/metrics", "GoogleHC", "HTTP Banner Detection", " 400 ", " 401", " 403 ", " 404 ", " 405 "}...)
	log.Info(skippedPatterns)
	avgLogLine := int64(100)   // Size of an average log line (100 bytes).
	linesToCheck := int64(100) // Check 100 lines at a time.
	bytesToCheck := avgLogLine * linesToCheck

	for _, f := range files {
		file, err := os.Open(filepath.Join(logDir, f.Name()))
		if err != nil {
			return nil, err
		}
		defer file.Close()

		// Go to the back of the file.
		endOffset, err := file.Seek(0, io.SeekEnd)
		startOffset := endOffset - bytesToCheck
		if err != nil {
			return nil, err
		}

		for endOffset > 0 {
			lines, err := getLinesBetween(file, startOffset, endOffset)
			if err != nil {
				return nil, err
			}

			for i := len(lines) - 1; i >= 0; i-- {
				shouldSkip := false
				for _, pattern := range skippedPatterns {
					if strings.Contains(lines[i], pattern) {
						shouldSkip = true
						continue
					}
				}

				if shouldSkip {
					continue
				}

				// Since we are checking from the end (logs are sorted by time too), the first activity log we encounter has to be the latest.
				t := timeFromNginxLine(lines[i])
				if t != nil {
					log.Info("On return log timestamp, file name is: ", f.Name())
					return t, nil
				}
			}

			endOffset -= bytesToCheck
			startOffset = endOffset - bytesToCheck
		}
	}

	// If no log files available, will return nil for both timestamp and error
	return nil, nil
}
