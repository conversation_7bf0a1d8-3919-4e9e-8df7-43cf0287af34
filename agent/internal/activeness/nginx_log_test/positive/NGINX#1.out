2023/12/13 20:55:21 [error] 1125#0: *1 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1126#0: *5 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1126#0: *7 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1126#0: *9 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1127#0: *2 connect() to unix:/home/<USER>/tigergraph/tmp/rest/restpp-nginx.fcgi.sock failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "GET /restpp/endpoints? HTTP/1.1", upstream: "fastcgi://unix:/home/<USER>/tigergraph/tmp/rest/restpp-nginx.fcgi.sock:", host: "localhost:14240"
127.0.0.1 - - [13/Dec/2023:20:55:21 +0000] "GET /restpp/endpoints HTTP/1.1" 502 150 "-" "Go-http-client/1.1"
2023/12/13 20:55:21 [error] 1126#0: *11 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1128#0: *13 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1126#0: *15 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1127#0: *17 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1127#0: *19 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1125#0: *21 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1127#0: *23 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1128#0: *25 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
2023/12/13 20:55:21 [error] 1127#0: *27 connect() failed (111: Connection refused) while connecting to upstream, client: 127.0.0.1, server: , request: "POST /ts3/api/datapoints HTTP/1.1", upstream: "http://127.0.0.1:19000/api/datapoints", host: "127.0.0.1:14240"
127.0.0.1 - - [13/Dec/2023:20:55:30 +0000] "GET /version HTTP/1.1" 400 248 "-" "-"
************ - - [13/Dec/2023:20:55:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:55:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:55:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:55:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:56:00 +0000] "POST /restppkafkaloader/ HTTP/1.1" 200 117 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:56:00 +0000] "GET /internal/gsqlserver/gsql/loading-jobs HTTP/1.1" 200 58 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:56:00 +0000] "GET /gsqlserver/gsql/loading-jobs HTTP/1.1" 200 53 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:56:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:56:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:56:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:56:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:56:21 +0000] "GET /restpp/endpoints HTTP/1.1" 200 89135 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:56:21 +0000] "GET /restpp/statistics HTTP/1.1" 200 405 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:56:30 +0000] "POST /builtins/MyGraph HTTP/1.1" 200 983 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:56:30 +0000] "POST /builtins/MyGraph HTTP/1.1" 200 447 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:56:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:56:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:56:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:56:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:57:00 +0000] "POST /restppkafkaloader/ HTTP/1.1" 200 117 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:57:00 +0000] "GET /gsqlserver/gsql/loading-jobs HTTP/1.1" 200 53 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:57:00 +0000] "GET /internal/gsqlserver/gsql/loading-jobs HTTP/1.1" 200 53 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:57:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:57:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:57:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:57:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:57:21 +0000] "GET /restpp/endpoints HTTP/1.1" 200 89135 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:57:21 +0000] "GET /restpp/statistics HTTP/1.1" 200 616 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:57:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:57:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:57:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:57:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:58:00 +0000] "POST /restppkafkaloader/ HTTP/1.1" 200 117 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:58:00 +0000] "GET /gsqlserver/gsql/loading-jobs HTTP/1.1" 200 53 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:58:00 +0000] "GET /internal/gsqlserver/gsql/loading-jobs HTTP/1.1" 200 53 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:58:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:58:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:58:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:58:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:58:21 +0000] "GET /restpp/endpoints HTTP/1.1" 200 89135 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:58:21 +0000] "GET /restpp/statistics HTTP/1.1" 200 392 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:58:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:58:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:58:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:58:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - tigergraph [13/Dec/2023:20:58:51 +0000] "POST /internal/gsqlserver/gsql/simpleauth HTTP/1.1" 200 781 "-" "Go-http-client/1.1"
127.0.0.1 - tigergraph [13/Dec/2023:20:58:51 +0000] "POST /gsqlserver/gsql/simpleauth HTTP/1.1" 200 781 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:58:51 +0000] "POST /internal/gsqlserver/gsql/login HTTP/1.1" 200 364 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:58:51 +0000] "POST /gsqlserver/gsql/login HTTP/1.1" 200 364 "-" "Go-http-client/1.1"
127.0.0.1 - tigergraph [13/Dec/2023:20:58:52 +0000] "POST /internal/gsqlserver/gsql/simpleauth HTTP/1.1" 200 781 "-" "Go-http-client/1.1"
127.0.0.1 - tigergraph [13/Dec/2023:20:58:52 +0000] "POST /gsqlserver/gsql/simpleauth HTTP/1.1" 200 781 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:59:00 +0000] "POST /restppkafkaloader/ HTTP/1.1" 200 117 "-" "Apache-HttpClient/4.5.13 (Java/*********)"
127.0.0.1 - - [13/Dec/2023:20:59:00 +0000] "GET /gsqlserver/gsql/loading-jobs HTTP/1.1" 200 58 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:59:00 +0000] "GET /internal/gsqlserver/gsql/loading-jobs HTTP/1.1" 200 58 "-" "go-resty/2.4.0 (https://github.com/go-resty/resty)"
127.0.0.1 - - [13/Dec/2023:20:59:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:59:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:59:13 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:59:13 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:59:21 +0000] "GET /restpp/endpoints HTTP/1.1" 200 89135 "-" "Go-http-client/1.1"
10.1.2.3. - - [13/Dec/2023:20:59:21 +0000] "GET /restpp/statistics HTTP/1.1" 200 392 "-" "Go-http-client/1.1"
127.0.0.1 - - [13/Dec/2023:20:59:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
127.0.0.1 - - [13/Dec/2023:20:59:43 +0000] "GET /internal/gui/api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:59:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
************ - - [13/Dec/2023:20:59:43 +0000] "GET /api/ping HTTP/1.1" 200 47 "-" "ELB-HealthChecker/2.0"
10.0.11.169 - - [13/Dec/2023:20:59:55 +0000] “GET /informant/metrics HTTP/1.1” 200 1426 “-” “Prometheus/2.48.1”