package activeness

import (
	"fmt"
	"regexp"
	"strconv"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const (
	GSQLLockAPILegacy = "%v/gsql/lock"
	GSQLLocksAPIV4    = "%v/gsql/v1/internal/locks"
)

var CatalogLastActiveTime *time.Time

func GetCatalogLastActiveTimeCron() {
	for {
		GetCatalogLastActiveTime()
		time.Sleep(time.Duration(config.Config.CatalogActivityUpdateIntervalSeconds) * time.Second)
	}
}

func GetCatalogLastActiveTime() error {
	log := logger.L()
	r, err := CheckCatalogActive(common.DefaultGSQLServerURL)
	if err != nil {
		log.Errorf("Failed to get catalog active status: %v", err)
		return err
	}

	if *r {
		t := time.Now()
		CatalogLastActiveTime = &t
	}

	return nil
}

func CheckCatalogActive(serverURL string) (*bool, error) {
	var err error
	var resp []byte

	input := &rest.RequestInput{
		URL:     fmt.Sprintf(GSQLLocksAPIV4, serverURL),
		NoRetry: true,
	}

	resp, err = rest.Get(input)
	if err != nil {
		return nil, err
	}

	apiResponse := string(resp)
	re := regexp.MustCompile(`Locked: (true|false)`)
	match := re.FindStringSubmatch(apiResponse)

	result := false

	if len(match) == 2 {
		result, err = strconv.ParseBool(match[1])
		if err != nil {
			return nil, err
		}

		return &result, nil
	} else {
		return nil, fmt.Errorf("no match found")
	}

}
