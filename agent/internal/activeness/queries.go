package activeness

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

type ShowProcessListAllResponse struct {
	Code    string                     `json:"code"`
	Error   bool                       `json:"error"`
	Message string                     `json:"message"`
	Results []ShowProcessListAllResult `json:"results"`
}

type ShowProcessListAllResult struct {
	RequestId      string `json:"requestid"`
	StartTime      string `json:"startTime"`
	ExpirationTime string `json:"expirationTime"`
	Url            string `json:"url"`
	ElapsedTime    int    `json:"elapsedTime"`
}

var QueryLastActiveTime *time.Time

func GetQueryLastActiveTimeCron() {
	for {
		log := logger.L()
		ts, err := GetQueriesTimeStamp(common.DefaultGUIServerURL)
		if err != nil {
			log.Errorf("Failed to get query last active time: %v", err)
		}
		if ts != nil {
			QueryLastActiveTime = ts
		}
		time.Sleep(time.Duration(config.Config.QueryActivityUpdateIntervalSeconds) * time.Second)
	}
}

func GetQueriesTimeStamp(serverURL string) (*time.Time, error) {
	input := &rest.RequestInput{
		URL:       fmt.Sprintf("%v/api/restpp/showprocesslistall", serverURL),
		AuthToken: config.Config.SystemAuthToken,
		NoRetry:   true,
	}

	resp, err := rest.Get(input)
	if err != nil {
		return nil, err
	}

	var response ShowProcessListAllResponse

	err = json.Unmarshal(resp, &response)
	if err != nil {
		return nil, err
	}

	if len(response.Results) > 0 {
		t := time.Now()
		return &t, nil
	} else {
		return nil, nil
	}
}
