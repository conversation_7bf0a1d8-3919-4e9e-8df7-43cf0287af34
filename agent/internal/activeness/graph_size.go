package activeness

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/agent/internal/observability/metrics"
)

var LastTopologyValue *GraphTopology

// GUIResponse is the response body of GUI server.
type GUIResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results interface{} `json:"results"`
	Done    *bool       `json:"done,omitempty"`
}

// GStatusGraphResponse is the response of graph topology data from gstatusgraph.
type GStatusGraphResponse struct {
	Nodes              []nodeTopologyInfo `json:"nodes"`
	TopologyBytes      int64              `json:"topology_bytes" validate:"required"`
	TopologyLimitBytes int64              `json:"topology_limit_bytes"`
	VertexCount        int64              `json:"vertex_count" validate:"required"`
	EdgeCount          int64              `json:"edge_count" validate:"required"`
}

type nodeTopologyInfo struct {
	Name                 string `json:"name"`
	PartitionSize        int64  `json:"partition_size"`
	IdsSize              int64  `json:"ids_size"`
	VertexCount          int64  `json:"vertex_count"`
	EdgeCount            int64  `json:"edge_count"`
	NumOfDeletedVertices int64  `json:"num_of_deleted_vertices"`
	NumOfSkippedVertices int64  `json:"num_of_skipped_vertices"`
}

type GraphTopology struct {
	TotalGraphTopologyBytes      int64
	TotalGraphTopologyLimitBytes int64
	TotalVertexCount             int64
	TotalEdgeCount               int64
}

func UpdateTopologyCron() {
	for {
		log := logger.L()
		topology, err := GetGraphTopology(common.DefaultGUIServerURL)
		if err != nil {
			log.Warnf("Failed to get graph topology: %w", err)
		}
		if topology != nil {
			LastTopologyValue = topology
			log.Info("Graph topology size updated.")
		}
		time.Sleep(time.Duration(config.Config.TopologyUpdateIntervalSeconds) * time.Second)
	}
}

// RequestGraphTopology requests the daemon to call the GUS '/gstatusgraph' endpoint
// to retrieve the graph topology.
func GetGraphTopologyLegacy(serverURL string) (response *GraphTopology, err error) {
	log := logger.L()
	input := rest.RequestInput{
		URL:       fmt.Sprintf("%s/api/system/gstatusgraph", serverURL),
		AuthToken: config.Config.SystemAuthToken,
		QueryParams: map[string]string{
			"force": strconv.FormatBool(config.Config.EnableTopologyForceUpdate),
		},
		NoRetry: true,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		log.Errorf("Failed to get graph topology: %v", err)
		return nil, err
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	jsonResult, err := json.Marshal(guiResp.Results)
	if err != nil {
		log.Errorf("Failed to marshal result: %v", err)
		return nil, err
	}

	gstatus := &GStatusGraphResponse{}
	err = json.Unmarshal(jsonResult, gstatus)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	reportGstatusMetric(gstatus)

	return &GraphTopology{
		TotalGraphTopologyBytes: gstatus.TopologyBytes,
		TotalVertexCount:        gstatus.VertexCount,
		TotalEdgeCount:          gstatus.EdgeCount,
	}, nil
}

func GetGraphTopology(serverURL string) (response *GraphTopology, err error) {
	log := logger.L()
	if err := common.IsGUIUp(serverURL); err != nil {
		return nil, err
	}
	input := rest.RequestInput{
		URL:       fmt.Sprintf("%s/api/system/gse-status", serverURL),
		AuthToken: config.Config.SystemAuthToken,
		NoRetry:   true,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		// To be compatibility with legacy version
		if strings.HasPrefix(err.Error(), "Status Code: 404 Not Found,") {
			log.Infof("gse-status not found, fallback to gstatusgraph")
			return GetGraphTopologyLegacy(serverURL)
		}
		log.Errorf("Failed to get graph topology: %v", err)
		return nil, err
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	jsonResult, err := json.Marshal(guiResp.Results)
	if err != nil {
		log.Errorf("Failed to marshal result: %v", err)
		return nil, err
	}

	gstatus := &GStatusGraphResponse{}
	err = json.Unmarshal(jsonResult, gstatus)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	reportGstatusMetric(gstatus)

	topology := &GraphTopology{
		TotalGraphTopologyBytes:      gstatus.TopologyBytes,
		TotalVertexCount:             gstatus.VertexCount,
		TotalEdgeCount:               gstatus.EdgeCount,
		TotalGraphTopologyLimitBytes: gstatus.TopologyLimitBytes,
	}

	return topology, nil
}

func reportGstatusMetric(gstatus *GStatusGraphResponse) {
	metrics.ReportGraphStatusMetrics(metrics.GraphStatusMetric{
		GraphTopologyBytes:      gstatus.TopologyBytes,
		GraphTopologyLimitBytes: gstatus.TopologyLimitBytes,
	})
}
