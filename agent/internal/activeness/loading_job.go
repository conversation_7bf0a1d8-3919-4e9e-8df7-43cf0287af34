package activeness

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/tigergraph/cloud-universe/utils/rest"
)

const (
	KafkaStreamLLURL = "http://localhost:30004"
)

type LoadingJobStatus struct {
	Status    string `json:"status"`
	ID        string `json:"id"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

func GetLoadingJobTimestamp(serverURL string) (*time.Time, error) {
	var loadingJobResponse map[string]string
	input := &rest.RequestInput{
		URL:     fmt.Sprintf("%v/log-aggregation/loading-status", serverURL),
		NoRetry: true,
	}

	resp, err := rest.Get(input)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp, &loadingJobResponse)
	if err != nil {
		return nil, err
	}

	latestTimeStamp := time.Unix(0, 0)

	for key := range loadingJobResponse {
		timeStamp, err := GetTimeStampByJobID(serverURL, key)
		if err != nil {
			return nil, err
		}
		if timeStamp.After(latestTimeStamp) {
			latestTimeStamp = *timeStamp
		}
	}

	if latestTimeStamp == time.Unix(0, 0) {
		return nil, nil
	}

	return &latestTimeStamp, nil
}

func GetTimeStampByJobID(serverURL, jobId string) (*time.Time, error) {
	var loadingJobStatus LoadingJobStatus
	input := &rest.RequestInput{
		URL:     fmt.Sprintf("%v/log-aggregation/loading-progress/%v", serverURL, jobId),
		NoRetry: true,
	}

	resp, err := rest.Get(input)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(resp, &loadingJobStatus)
	if err != nil {
		return nil, err
	}

	timeStamp := time.Unix(loadingJobStatus.EndTime/1000, 0)
	return &timeStamp, nil
}
