package activeness

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func MockKafkaStreamLL(resp1, jobId, resp2 string) *httptest.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/log-aggregation/loading-status", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp1)
	})
	mux.HandleFunc(fmt.Sprintf("/log-aggregation/loading-progress/%v", jobId), func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp2)
	})
	return httptest.NewServer(mux)
}
func TestGetLoadingJobTimestamp(t *testing.T) {
	resp1 := `{"qwerty.file_load_job_16bd3359fe.file.m1.1722378784288":"FINISHED"}`
	resp2 := `{"status":"FINISHED","id":"zxcvb.file_load_job_5a37718e71.file.m1.1722387402599","startTime":1722387402600,"endTime":1722387446572,"completedWorker":1,"totalWorker":1,"workers":{"RESTPP-LOADER_1":{"id":"RESTPP-LOADER_1","status":"FINISHED","completedTaskIDs":{"MyDataSource:/home/<USER>/tigergraph/data/gui/loading_data/<EMAIL>/SearchTrend.csv":true},"totalTask":1,"tasks":{"MyDataSource:/home/<USER>/tigergraph/data/gui/loading_data/<EMAIL>/SearchTrend.csv":{"id":"MyDataSource:/home/<USER>/tigergraph/data/gui/loading_data/<EMAIL>/SearchTrend.csv","size":68026,"totalSize":68062,"startTime":1722387440593,"endTime":1722387446572,"status":"FINISHED","statistics":{"sourceFileName":"","parsingStatistics":{"objectLevel":{"vertex":[{"typeName":"SearchTrend","validObject":1558}]},"fileLevel":{"validLine":1558}}}}},"completedTask":1}},"graphName":"zxcvb"}`
	server1 := MockKafkaStreamLL(resp1, "qwerty.file_load_job_16bd3359fe.file.m1.1722378784288", resp2)
	latestTimeStamp, err := GetLoadingJobTimestamp(server1.URL)
	require.NoError(t, err)
	require.Equal(t, time.Unix(1722387446572/1000, 0), *latestTimeStamp)
	server1.Close()
}

func TestGetLoadingJobTimestampEmpty(t *testing.T) {
	resp2 := `{}`
	server2 := MockKafkaStreamLL(resp2, "", "")
	latestTimeStamp1, err := GetLoadingJobTimestamp(server2.URL)
	require.NoError(t, err)
	require.Nil(t, latestTimeStamp1)
	server2.Close()
}
