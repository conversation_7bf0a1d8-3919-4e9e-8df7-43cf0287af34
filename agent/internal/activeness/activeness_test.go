package activeness

import (
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
)

func TestGetNginxTimeStamp(t *testing.T) {
	ts, err := GetLatestNginxTimestamp("./nginx_log_test/positive", []string{"1.1.1.1"})
	assert.Nil(t, err)
	assert.NotNil(t, ts)
	expectedTime := time.Date(2023, 12, 13, 20, 59, 21, 0, time.UTC)
	assert.Equal(t, *ts, expectedTime)

	ts, err = GetLatestNginxTimestamp("./nginx_log_test/negative", []string{"1.1.1.1"})
	assert.Nil(t, err)
	assert.Nil(t, ts)
}

func MockGsqlLock() *httptest.Server {
	counter := 0
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if counter%2 == 0 {
			io.WriteString(w, "Locked: true\n")
		} else {
			io.WriteString(w, "Locked: false\n")
		}
		counter += 1
	})
	return httptest.NewServer(mux)
}

func TestCatalogActivity(t *testing.T) {
	server := MockGsqlLock()
	defer server.Close()
	r, err := CheckCatalogActive(server.URL)
	assert.NoError(t, err)
	assert.True(t, *r)
	r, err = CheckCatalogActive(server.URL)
	assert.NoError(t, err)
	assert.False(t, *r)
}

func TestGetLatestTime(t *testing.T) {
	t1 := time.Date(2024, 1, 1, 20, 30, 00, 651387237, time.UTC)
	t2 := time.Date(2024, 1, 2, 20, 30, 00, 651387237, time.UTC)
	t3 := time.Date(2024, 1, 3, 20, 30, 00, 651387237, time.UTC)
	lt := GetLatestTime([]*time.Time{&t1, &t2, &t3})
	assert.Equal(t, *lt, t3)

	lt1 := GetLatestTime([]*time.Time{&t1, &t2, nil})
	assert.Equal(t, *lt1, t2)

	lt3 := GetLatestTime([]*time.Time{nil, nil, nil})
	var nilTime *time.Time
	assert.Equal(t, lt3, nilTime)
}

func MockSystemHostList() *httptest.Server {
	resp := `{"error": false, "message": "", "results": {"System.HostList":[{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-0.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m1","Region":""},{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-1.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m2","Region":""}]},"done": true}`
	//`[{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-0.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m1","Region":""},{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-1.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m2","Region":""}]`
	mux := http.NewServeMux()
	mux.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		cookie1 := &http.Cookie{Name: "TigerGraphApp", Value: "sample", HttpOnly: false}
		http.SetCookie(w, cookie1)
		io.WriteString(w, "")
	})
	mux.HandleFunc("/api/config", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp)
	})
	return httptest.NewServer(mux)
}

func TestGetClusterIPAddresses(t *testing.T) {
	server := MockSystemHostList()
	defer server.Close()
	config.Config.WorkgroupId = "325297d8-2a96-4353-a25e-3ab744d2629b"
	_, err := GetClusterIPAddresses(server.URL)
	if err != nil {
		assert.Contains(t, err.Error(), "tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service.325297d8-2a96-4353-a25e-3ab744d2629b.svc.cluster.local")
	}
}
