package activeness

import (
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func MockGraphSizeLegacy() (server *httptest.Server) {
	resp := `{
		"error": false,
		"message": "Success",
		"results": {
		  "nodes": [
			{
			  "name": "m1",
			  "partition_size": 1000,
			  "ids_size": 1000,
			  "vertex_count": 10000,
			  "edge_count": 0,
			  "num_of_deleted_vertices": 0,
			  "num_of_skipped_vertices": 0
			},
			 {
			  "name": "m2",
			  "partition_size": 1000,
			  "ids_size": 1000,
			  "vertex_count": 10000,
			  "edge_count": 0,
			  "num_of_deleted_vertices": 0,
			  "num_of_skipped_vertices": 0
			}
		  ],
		  "topology_bytes": 4000,
		  "vertex_count": 20000,
		  "edge_count": 0
		}
	  }`

	mux := http.NewServeMux()
	pingResponse := `{"error":false,"message":"pong","results":{},"done":true}`
	mux.HandleFunc("/api/ping", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, pingResponse)
	})
	mux.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		cookie1 := &http.Cookie{Name: "TigerGraphApp", Value: "sample", HttpOnly: false}
		http.SetCookie(w, cookie1)
		io.WriteString(w, "")
	})
	mux.HandleFunc("/api/system/gstatusgraph", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp)
	})

	return httptest.NewServer(mux)
}

func MockGraphSize() (server *httptest.Server) {
	resp := `{
		"error": false,
		"message": "Success",
		"results": {
		  "topology_bytes": 4000,
		  "vertex_count": 20000,
		  "edge_count": 0
		}
	  }`

	mux := http.NewServeMux()
	pingResponse := `{"error":false,"message":"pong","results":{},"done":true}`
	mux.HandleFunc("/api/ping", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, pingResponse)
	})
	mux.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		cookie1 := &http.Cookie{Name: "TigerGraphApp", Value: "sample", HttpOnly: false}
		http.SetCookie(w, cookie1)
		io.WriteString(w, "")
	})
	mux.HandleFunc("/api/system/gstatusgraph", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp)
	})
	mux.HandleFunc("/api/system/gse-status", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, resp)
	})

	return httptest.NewServer(mux)
}

func TestGraphSize(t *testing.T) {
	server := MockGraphSize()
	defer server.Close()
	r, err := GetGraphTopology(server.URL)
	assert.NoError(t, err)
	assert.Equal(t, int64(4000), r.TotalGraphTopologyBytes)
	assert.Equal(t, int64(20000), r.TotalVertexCount)
	assert.Equal(t, int64(0), r.TotalEdgeCount)

	t.Run("Test GUI is down", func(t *testing.T) {
		server = httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusServiceUnavailable)
		}))
		defer server.Close()

		_, err := GetGraphTopology(server.URL)
		assert.Error(t, err)
	})
}

func TestGraphSizeLegacy(t *testing.T) {
	server := MockGraphSizeLegacy()
	defer server.Close()
	r, err := GetGraphTopology(server.URL)
	assert.NoError(t, err)
	assert.Equal(t, int64(4000), r.TotalGraphTopologyBytes)
	assert.Equal(t, int64(20000), r.TotalVertexCount)
	assert.Equal(t, int64(0), r.TotalEdgeCount)

	t.Run("Test GUI is down", func(t *testing.T) {
		server = httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusServiceUnavailable)
		}))
		defer server.Close()

		_, err := GetGraphTopology(server.URL)
		assert.Error(t, err)
	})

}

func TestGraph(t *testing.T) {
	server := MockGraphSize()
	defer server.Close()
	r, err := GetGraphTopology(server.URL)
	assert.NoError(t, err)
	assert.Equal(t, int64(4000), r.TotalGraphTopologyBytes)
	assert.Equal(t, int64(20000), r.TotalVertexCount)
	assert.Equal(t, int64(0), r.TotalEdgeCount)

	t.Run("Test GUI is down", func(t *testing.T) {
		server = httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusServiceUnavailable)
		}))
		defer server.Close()

		_, err := GetGraphTopology(server.URL)
		assert.Error(t, err)
	})

}
