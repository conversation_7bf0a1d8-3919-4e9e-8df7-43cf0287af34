package activeness

import (
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
)

func GetCatalogActivity() *time.Time {
	// query installation and schema change
	return CatalogLastActiveTime
}

func GetLoadingJobActivity() (*time.Time, error) {
	// loading jobs
	return GetLoadingJobTimestamp(KafkaStreamLLURL)
}

func GetQueryActivity() *time.Time {
	// query
	return QueryLastActiveTime
}

func GetNginxActivity() (*time.Time, error) {
	nginxLogDir := config.Config.NGINXLogDir
	ipAddresses, err := GetClusterIPAddresses(common.DefaultGUIServerURL)
	if err != nil {
		return nil, err
	}
	return GetLatestNginxTimestamp(nginxLogDir, ipAddresses)
}

func GetLatestTime(times []*time.Time) *time.Time {
	var maxTime *time.Time

	for _, t := range times {
		if t == nil {
			continue
		}

		if maxTime == nil || t.After(*maxTime) {
			maxTime = t
		}

	}
	return maxTime
}

// GetClusterIPAddresses() sets the IP addresses of the Pod itself and other nodes in the TG cluster
// And exclude them in the activeness check for auto stop
func GetClusterIPAddresses(serverURL string) ([]string, error) {
	var clusterIPAddresses []string
	var addresses []net.Addr

	hostList, err := common.GetSystemHostList(serverURL)
	if err != nil {
		return nil, err
	}

	// If number of nodes > 1, get IP addresses of the neighbors
	if len(hostList) > 1 {
		// An example of `Hostname` in `hostList`:
		// tg-c1826d38-1b90-41e2-a9d3-df52144c28be-0.tg-c1826d38-1b90-41e2-a9d3-df52144c28be-internal-service
		res := strings.Split(hostList[0].Hostname, ".")
		hostNamePrefix := res[len(res)-1]
		workspaceHostName := fmt.Sprintf("%v.%v.svc.cluster.local", hostNamePrefix, config.Config.WorkgroupId)
		// An example of `workspaceHostName`:
		// tg-c1826d38-1b90-41e2-a9d3-df52144c28be-internal-service.9bb3ff09-fc48-4d87-a159-578894d71cfc.svc.cluster.local
		resolvedIPs, err := net.LookupIP(workspaceHostName)
		if err != nil {
			return nil, err
		}

		for _, ip := range resolvedIPs {
			clusterIPAddresses = append(clusterIPAddresses, ip.To4().String())
		}

	}

	// Get the IP address(es) of the current pod
	addresses, err = net.InterfaceAddrs()
	if err != nil {
		return nil, err
	}

	for _, addr := range addresses {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				clusterIPAddresses = append(clusterIPAddresses, ipnet.IP.To4().String())
			}
		}
	}

	return clusterIPAddresses, nil

}
