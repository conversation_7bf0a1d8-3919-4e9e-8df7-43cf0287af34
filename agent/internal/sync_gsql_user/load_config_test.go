package sync_gsql_user

import (
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
)

func TestLoadConfig(t *testing.T) {
	os.Setenv("WORKSPACE_NAME", "tg-f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("ORG_ID", "org_qwerty")
	os.Setenv("WORKGROUP_ID", "cfbad676-5b90-48ac-80bd-f6257f0fc54e")
	os.Setenv("WORKSPACE_ID", "f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("AUTH0_DOMAIN", "tgcloud-dev.auth0.com")
	os.Setenv("IAM_SERVER", "https://api.tgcloud-dev.com/iam")
	os.Setenv("SQS_URL", "https://sqs.us-east-1.amazonaws.com/123456789/agent-heatbeat")
	os.Setenv("M2M_CLIENT_ID", "qwerty")
	os.Setenv("M2M_CLIENT_SECRET", "asdfghjkl")
	os.Setenv("GSQL_DB_USER_META_FILE_FOLDER", "/path/to")
	os.Setenv("TG_PASSWORD", "zxcvbnm")
	os.Setenv("SYSTEM_AUTH_TOKEN", "token")
	os.Setenv("IAM_API_KEY", "key")
	os.Setenv("TOPOLOGY_UPDATE_INTERVAL_SECONDS", "30")
	os.Setenv("ROLE_ARN", "arn:aws:iam::123456789:role/workgroup-6bbfe57e-c781-44e3-9331-cf20b3470af3-sa-role")

	os.Setenv("ENABLE_TOPOLOGY_FORCE_UPDATE", "true")
	os.Setenv("CATALOG_ACTIVITY_UPDATE_INTERVAL_SECONDS", "60")
	os.Setenv("QUERY_ACTIVITY_UPDATE_INTERVAL_SECONDS", "60")
	os.Setenv("HEARTBEAT_INTERVAL_SECONDS", "60")

	config.LoadConfig()

	expectedMeta := &workspaceMeta{
		WorkgroupID:              uuid.MustParse("cfbad676-5b90-48ac-80bd-f6257f0fc54e"),
		WorkspaceID:              uuid.MustParse("f8b57bb0-960b-453e-b74d-4f264de45fef"),
		OrgID:                    "org_qwerty",
		TigergraphPassword:       "zxcvbnm",
		GSQLDBUserMetaFileFolder: "/path/to",
	}
	meta, err := loadWorkspaceMeta()
	assert.NoError(t, err)
	assert.Equal(t, meta, expectedMeta)
}
