package sync_gsql_user

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/robfig/cron/v3"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	gsqlUser "github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

const (
	SyncInterval = 3 // Number of seconds per sync.
)

type Service struct {
	iamV2Svc               tgIAM.TGIAM
	iamAPIKey              string
	gsqlUserSvc            gsqlUser.UserService
	gsqlServerPort         int
	gsqlServerPos          int
	gsqlServerTotalServers int
	workgroupID            uuid.UUID
	workspaceID            uuid.UUID
	orgID                  string
}

func New() (svc *Service) {
	defer func() {
		// Automatically start the crontab once the service is initiated.
		// If this fails, daemon will exit due to the panic, the solution will not be ready to controller.
		if err := svc.StartCrontab(); err != nil {
			panic(err)
		}
	}()
	common.WaitUntilServicesUp()
	return &Service{}
}

// Run implements the job interface in cron package so it can be added to crontab.
// Run performs necessary checks to see if gsql user sync can be started and update configs.
func (svc *Service) Run() {
	log := logger.L()
	log.Info("running gsql sync")

	if err := common.IsGUIUp(common.DefaultGUIServerURL); err != nil {
		log.Warn("GUI is not up, sync skipped.")
		return
	}

	svc.gsqlServerPort = config.Config.GSQLServerPort

	// Check if the current machine has a running gsql server.
	if !gsqlUser.CheckGSQLServer() {
		log.Warn("No running GSQL server found, sync skipped.")
		return
	}

	// Get the position of current host in the sorted list of GSQL servers if not already.
	if svc.gsqlServerTotalServers == 0 {
		nodePos, totalServers, err := common.GetCurrentHostPosInGSQLServerList(common.DefaultGUIServerURL)
		if err != nil {
			log.Errorf("Failed to get the position of current host in GSQL server list: %v", err)
			return
		}
		svc.gsqlServerPos = nodePos
		svc.gsqlServerTotalServers = totalServers
	}

	// Check if it is my turn.
	if !isMyTurn(svc.gsqlServerPos, svc.gsqlServerTotalServers, time.Now().Second(), SyncInterval) {
		log.Info("Not my turn, sync skipped.")
		return
	}

	// Check if config is updated.
	err := svc.loadConfig()
	if err != nil {
		log.Errorf("Failed to load configs for synchronizing gsql user: %v.", err)
		return
	}

	svc.SyncUsers()
}

func (svc *Service) StartCrontab() error {
	crontabs := cron.New()

	// WARNING: the recover job wrapper should be put at very last position otherwise it will not work correctly.
	// More about crontab: https://programmer.ink/think/cron-of-go-daily.html
	_, err := crontabs.AddJob(
		fmt.Sprintf("@every %vs", SyncInterval),
		cron.NewChain(
			cron.SkipIfStillRunning(cron.DefaultLogger),
			cron.Recover(cron.DefaultLogger),
		).Then(svc),
	)

	if err != nil {
		return err
	}

	crontabs.Start()
	return nil
}
