package gsql_user

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
)

func TestWriteDBUsersToFile(t *testing.T) {
	svc := New("", "/tmp/test_folder", "/tmp/test_folder/test_file.yaml", 1234)

	users := map[string]time.Time{
		"user1": time.Now(),
		"user2": time.Now().Add(-time.Hour),
	}

	err := svc.WriteDBUsersToFile(users)
	assert.NoError(t, err)

	defer os.RemoveAll("/tmp/test_folder")

	readUsers, err := svc.ReadDBUsersFromFile()
	assert.NoError(t, err)

	yml1, err := yaml.Marshal(users)
	yml2, err := yaml.Marshal(readUsers)

	assert.Equal(t, yml1, yml2)
}

func TestReadDBUsersFromFile_NoFile(t *testing.T) {
	svc := New("", "/tmp/test_folder", "/tmp/test_folder/test_file.yaml", 1234)

	users, err := svc.ReadDBUsersFromFile()
	assert.NoError(t, err)
	assert.Empty(t, users)
}

func TestReadDBUsersFromFile_InvalidYAML(t *testing.T) {
	gsqlDBUserMetaFileFolder := "/tmp/test_folder"
	gsqlDBUserMetaFilePath := "/tmp/test_folder/test_file.yaml"

	svc := New("", gsqlDBUserMetaFileFolder, gsqlDBUserMetaFilePath, 1234)

	err := os.MkdirAll(gsqlDBUserMetaFileFolder, 0755)
	assert.NoError(t, err)
	err = os.WriteFile(gsqlDBUserMetaFilePath, []byte("invalid_yaml"), 0666)
	assert.NoError(t, err)

	defer os.RemoveAll(gsqlDBUserMetaFileFolder)

	_, err = svc.ReadDBUsersFromFile()
	assert.Error(t, err)
}
