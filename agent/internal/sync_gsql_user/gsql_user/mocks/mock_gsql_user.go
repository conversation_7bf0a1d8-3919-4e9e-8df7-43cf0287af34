package mocks

import (
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user"
)

type MockGSQLUserService struct {
	mock.Mock
}

func (m *MockGSQLUserService) GetExistingGSQLUsers(allowCache bool) (users map[string]*gsql_user.GSQLUser, err error) {
	args := m.Called(allowCache)
	return args.Get(0).(map[string]*gsql_user.GSQLUser), args.Error(1)
}

func (m *MockGSQLUserService) CreateGSQLUser(name string) (err error) {
	args := m.Called(name)
	return args.Error(0)
}

func (m *MockGSQLUserService) CreateGSQLUserWithPassword(name, password string) (err error) {
	args := m.Called(name, password)
	return args.Error(0)
}

func (m *MockGSQLUserService) AssignGSQLUserRole(userNames []string, role string) (err error) {
	args := m.Called(userNames, role)
	return args.Error(0)
}

func (m *MockGSQLUserService) RevokeGSQLUserRole(userName, role string) (err error) {
	args := m.Called(userName, role)
	return args.Error(0)
}

func (m *MockGSQLUserService) DeleteGSQLUser(userName string) (err error) {
	args := m.Called(userName)
	return args.Error(0)
}

func (m *MockGSQLUserService) ChangeGSQLUserPassword(userName, password string) (err error) {
	args := m.Called(userName, password)
	return args.Error(0)
}

func (m *MockGSQLUserService) WriteDBUsersToFile(users map[string]time.Time) (err error) {
	args := m.Called(users)
	return args.Error(0)
}

func (m *MockGSQLUserService) ReadDBUsersFromFile() (users map[string]time.Time, err error) {
	args := m.Called()
	return args.Get(0).(map[string]time.Time), args.Error(1)
}
