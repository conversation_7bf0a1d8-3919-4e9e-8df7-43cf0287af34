package gsql_user

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sethvargo/go-password/password"

	"github.com/tigergraph/cloud-universe/utils/list"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const (
	GSQLUsersAPIV4      = "http://localhost:%v/gsql/scim/v2/Users"
	GSQLRoleGrantAPIV4  = "http://localhost:%v/gsql/v1/roles/grant"
	GSQLRoleRevokeAPIV4 = "http://localhost:%v/gsql/v1/roles/revoke"
)

type GetGSQLUserResp struct {
	Error   bool       `json:"error"`
	Message string     `json:"message"`
	Results []GSQLUser `json:"results"`
}

func (svc *Service) GetExistingGSQLUsers(allowCache bool) (users map[string]*GSQLUser, err error) {
	log := logger.L()
	if !allowCache {
		svc.clearCachedUsers()
	}

	// return cached users if users are fetched within cached duration.
	if svc.userExpires != nil && svc.users != nil && time.Now().Before(*svc.userExpires) {
		return svc.users, nil
	}

	// Clean up all cached auth cookies and users if error occurs.
	defer func() {
		if err != nil {
			svc.clearCachedUsers()
		}
	}()

	input := rest.RequestInput{
		Client: svc.client,
		URL:    fmt.Sprintf(GSQLUsersAPIV4, svc.gsqlServerPort),
		QueryParams: map[string]string{
			"gsqlFormat": "true",
		},
		AuthToken: svc.authToken,
		NoRetry:   true, // No need to retry since it will be retried by other nodes.
	}

	resp, err := rest.Get(&input)
	if err != nil {
		return nil, err
	}

	rawResp := GetGSQLUserResp{}
	err = json.Unmarshal(resp, &rawResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: body %v, error %v", string(resp), err)
		return nil, err
	}

	if rawResp.Error {
		return nil, fmt.Errorf("%v", rawResp.Message)
	}

	// Filter out users that are reserved like tigergraph.
	svc.users = make(map[string]*GSQLUser)
	for i := range rawResp.Results {
		gUser := rawResp.Results[i]
		if !list.ContainsString(reservedUserNames, gUser.Name) {
			svc.users[gUser.Name] = &gUser
		}
	}

	// Set an expiration time so we can refresh the cache at that time.
	// Note this cache will also be actively cleaned up when adding/deleting users or granting superuser roles to them.
	userExpires := time.Now().Add(cachedUserDuration)
	svc.userExpires = &userExpires
	return svc.users, nil
}

func (svc *Service) CreateGSQLUser(name string) (err error) {
	randomPwd, err := password.Generate(64, 10, 0, false, true)
	if err != nil {
		return err
	}

	return svc.CreateGSQLUserWithPassword(name, randomPwd)
}

func (svc *Service) AssignGSQLUserRole(userNames []string, role string) (err error) {
	if role == "" {
		return fmt.Errorf("role cannot be empty")
	}

	defer func() {
		// Intentionally clean up cached users since we updated the role assignment.
		svc.clearCachedUsers()
	}()

	input := rest.RequestInput{
		Client: svc.client,
		URL:    fmt.Sprintf(GSQLRoleGrantAPIV4, svc.gsqlServerPort),
		Body: map[string]interface{}{
			"roles": []string{role},
			"users": userNames,
		},
		AuthToken: svc.authToken,
		NoRetry:   true,
	}

	resp, err := rest.Post(&input)
	if err != nil {
		return err
	}
	return checkIfGSQLServerRespIsError(resp)
}

func (svc *Service) RevokeGSQLUserRole(userName, role string) (err error) {
	var resp []byte

	if role == "" {
		return fmt.Errorf("role cannot be empty")
	}

	defer func() {
		// Intentionally clean up cached users since we updated the role assignment.
		svc.clearCachedUsers()
	}()

	input := rest.RequestInput{
		Client: svc.client,
		URL:    fmt.Sprintf(GSQLRoleRevokeAPIV4, svc.gsqlServerPort),
		Body: map[string]interface{}{
			"roles": []string{role},
			"users": []string{userName},
		},
		AuthToken: svc.authToken,
		NoRetry:   true,
	}

	resp, err = rest.Post(&input)
	if err != nil {
		return err
	}

	return checkIfGSQLServerRespIsError(resp)
}

func (svc *Service) DeleteGSQLUser(userName string) (err error) {
	var input rest.RequestInput

	defer func() {
		// Intentionally clean up cached users since we removed user.
		svc.clearCachedUsers()
	}()

	input = rest.RequestInput{
		Client:    svc.client,
		URL:       fmt.Sprintf(GSQLUsersAPIV4+"/%v", svc.gsqlServerPort, userName),
		AuthToken: svc.authToken,
		NoRetry:   true,
	}

	resp, err := rest.Delete(&input)
	if err != nil {
		return err
	}

	err = checkIfGSQLServerRespIsError(resp)

	// Return silently if the user does not exist in GSQL (can be deleted already).
	if err != nil && strings.Contains(err.Error(), "could not be found") {
		return nil
	}

	return err
}

func (svc *Service) ChangeGSQLUserPassword(userName, password string) (err error) {
	var input rest.RequestInput

	input = rest.RequestInput{
		Client: svc.client,
		URL:    fmt.Sprintf(GSQLUsersAPIV4+"/%v", svc.gsqlServerPort, userName),
		QueryParams: map[string]string{
			"gsqlFormat": "true",
		},
		Body: map[string]interface{}{
			"password": password,
		},
		AuthToken: svc.authToken,
		NoRetry:   true,
	}

	resp, err := rest.Put(&input)
	if err != nil {
		return err
	}

	err = checkIfGSQLServerRespIsError(resp)

	// Return silently if the password was not changed.
	if err != nil && strings.Contains(err.Error(), "New password cannot be the same as old password") {
		return nil
	}

	return err
}

// CreateGSQLUserWithPassword creates a gsql user with the provided name and password.
func (svc *Service) CreateGSQLUserWithPassword(name, password string) (err error) {
	var input rest.RequestInput

	defer func() {
		// Intentionally clears cached users so we can get updated list of users next time we call getExistingGSQLUsers.
		svc.clearCachedUsers()
	}()

	input = rest.RequestInput{
		Client: svc.client,
		URL:    fmt.Sprintf(GSQLUsersAPIV4, svc.gsqlServerPort),
		Body: map[string]interface{}{
			"userName": name,
			"password": password,
		},
		AuthToken: svc.authToken,
		NoRetry:   true,
	}

	resp, err := rest.Post(&input)
	if err != nil {
		return err
	}

	return checkIfGSQLServerRespIsError(resp)
}

func (svc *Service) clearCachedUsers() {
	svc.users = nil
	svc.userExpires = nil
}
