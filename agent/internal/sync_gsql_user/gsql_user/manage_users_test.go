package gsql_user

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const (
	authToken     = "token"
	port          = 8123
	userEmail1    = "<EMAIL>"
	userEmail2    = "<EMAIL>"
	testTGVersion = "4.1.0"
)

func TestRunAll(t *testing.T) {
	TestGetExisingGSQLUsers(t)
	TestCreateGSQLUser(t)
	TestDeleteGSQLUser(t)
	TestAssignGSQLUserRole(t)
	TestAssignEmptyGSQLUserRole(t)
	TestRevokeGSQLUserRole(t)
	TestRevokeEmptyGSQLUserRole(t)
	TestChangeGSQLUserPassword(t)
}

func TestGetExisingGSQLUsers(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	gsqlUser1 := GSQLUser{
		Name:        userEmail1,
		IsSuperUser: true,
	}

	gsqlUser2 := GSQLUser{
		Name:        userEmail2,
		IsSuperUser: false,
	}

	gsqlUsers := []GSQLUser{gsqlUser1, gsqlUser2}

	url := fmt.Sprintf("http://localhost:%v/gsql/scim/v2/Users", port)
	httpmock.RegisterResponder("GET", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &GetGSQLUserResp{
				Error:   false,
				Message: "Successfully fetched GSQL users.",
				Results: gsqlUsers,
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)

	users, err := svc.GetExistingGSQLUsers(true)

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["GET "+url], "Expected GET call to %v was not made", url)
	assert.Equal(t, 2, len(users), "Expected number of users not fetched")
	assert.Equal(t, gsqlUser1, *users[userEmail1], "Expected user not fetched")
	assert.Equal(t, gsqlUser2, *users[userEmail2], "Expected user not fetched")
}

func TestCreateGSQLUser(t *testing.T) {
	client := resty.New()
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	url := fmt.Sprintf("http://localhost:%v/gsql/scim/v2/Users", 8123)
	httpmock.RegisterResponder("POST", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &gsqlServerResp{
				Error:   false,
				Message: "Successfully created GSQL user.",
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)
	err := svc.CreateGSQLUser(userEmail1)

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["POST "+url], "Expected POST call to %v was not made", url)
}

func TestDeleteGSQLUser(t *testing.T) {
	client := resty.New()
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	url := fmt.Sprintf("http://localhost:%v/gsql/scim/v2/Users/<USER>", port, userEmail1)
	httpmock.RegisterResponder("DELETE", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &gsqlServerResp{
				Error:   false,
				Message: "Successfully deleted GSQL user.",
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)

	err := svc.DeleteGSQLUser(userEmail1)

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["DELETE "+url], "Expected DELETE call to %v was not made", url)
}

func TestAssignGSQLUserRole(t *testing.T) {
	client := resty.New()
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	url := fmt.Sprintf("http://localhost:%v/gsql/v1/roles/grant", port)
	httpmock.RegisterResponder("POST", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &gsqlServerResp{
				Error:   false,
				Message: "Successfully assigned user superuser role.",
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)

	err := svc.AssignGSQLUserRole([]string{userEmail1}, string(tgIAM.GSQLSuperUser))

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["POST "+url], "Expected POST call to %v was not made", url)
}

func TestAssignEmptyGSQLUserRole(t *testing.T) {
	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
	}

	err := svc.AssignGSQLUserRole([]string{userEmail1}, "")
	assert.NotNil(t, err)
}

func TestRevokeGSQLUserRole(t *testing.T) {
	client := resty.New()
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	url := fmt.Sprintf("http://localhost:%v/gsql/v1/roles/revoke", port)
	httpmock.RegisterResponder("POST", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &gsqlServerResp{
				Error:   false,
				Message: "Successfully revoked user superuser role.",
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)

	err := svc.RevokeGSQLUserRole(userEmail1, string(tgIAM.GSQLSuperUser))

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["POST "+url], "Expected DELETE call to %v was not made", url)
}

func TestRevokeEmptyGSQLUserRole(t *testing.T) {
	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
	}

	err := svc.RevokeGSQLUserRole(userEmail1, "")
	assert.NotNil(t, err)
}

func TestNegativeRevokeGSQLUserRole(t *testing.T) {
	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
	}

	err := svc.RevokeGSQLUserRole(userEmail1, string(tgIAM.GSQLSuperUser))
	assert.Error(t, err)
}

func TestChangeGSQLUserPassword(t *testing.T) {
	client := resty.New()
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	svc := Service{
		authToken:      authToken,
		gsqlServerPort: port,
		client:         client,
	}

	url := fmt.Sprintf("http://localhost:%v/gsql/scim/v2/Users/<USER>", port, userEmail1)
	httpmock.RegisterResponder("PUT", url,
		func(req *http.Request) (*http.Response, error) {
			resp := &gsqlServerResp{
				Error:   false,
				Message: "Successfully changed user password.",
			}
			respBytes, _ := json.Marshal(resp)
			return httpmock.NewBytesResponse(200, respBytes), nil
		},
	)

	err := svc.ChangeGSQLUserPassword(userEmail1, "new_password")

	info := httpmock.GetCallCountInfo()
	assert.Nil(t, err)
	assert.Equal(t, 1, httpmock.GetTotalCallCount(), "Expected number of HTTP calls not made")
	assert.Equal(t, 1, info["PUT "+url], "Expected PUT call to %v was not made", url)
}

func TestCheckGSQLServer(t *testing.T) {
	assert.False(t, CheckGSQLServer())
}
