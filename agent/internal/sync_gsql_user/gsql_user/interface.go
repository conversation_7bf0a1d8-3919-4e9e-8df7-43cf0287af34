package gsql_user

import "time"

type UserService interface {
	GetExistingGSQLUsers(allowCache bool) (users map[string]*GS<PERSON>U<PERSON>, err error)
	CreateGSQLUser(name string) (err error)
	CreateGSQLUserWithPassword(name, password string) (err error)
	DeleteGS<PERSON>User(userName string) (err error)
	AssignGSQLUserRole(userNames []string, role string) (err error)
	RevokeGSQLUserRole(userName, role string) (err error)
	ChangeGSQLUserPassword(userName, password string) (err error)
	WriteDBUsersToFile(users map[string]time.Time) (err error)
	ReadDBUsersFromFile() (users map[string]time.Time, err error)
}
