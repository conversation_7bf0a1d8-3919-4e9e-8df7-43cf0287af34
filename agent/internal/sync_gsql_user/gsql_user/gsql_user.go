package gsql_user

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	cachedUserDuration = 10 * time.Minute // Users got from gsql server will be cached for 10 min to reduce API calls.
)

var (
	TigerGraphUserName = "tigergraph"
	reservedUserNames  = []string{TigerGraphUserName}
)

type GSQLUser struct {
	IsSuperUser bool                `json:"isSuperUser"`
	Roles       map[string][]string `json:"roles"`
	Name        string              `json:"name"`
}

type Service struct {
	authToken                string
	users                    map[string]*GSQLUser // Map of user names (emails) to GSQLUser.
	userExpires              *time.Time
	client                   *resty.Client
	gsqlServerPort           int
	gsqlDBUserMetaFileFolder string
	gsqlDBUserMetaFilePath   string // Saves metadata of db users from IAM to track last time of password change for each db user).
}

func New(authToken, gsqlDBUserMetaFileFolder, gsqlDBUserMetaFilePath string, gsqlServerPort int) UserService {
	return &Service{
		authToken:                authToken,
		gsqlServerPort:           gsqlServerPort,
		gsqlDBUserMetaFileFolder: gsqlDBUserMetaFileFolder,
		gsqlDBUserMetaFilePath:   gsqlDBUserMetaFilePath,
	}
}
