package gsql_user

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"

	"github.com/tigergraph/cloud-universe/utils/os/file"
)

/*
	DB User = users customers create for programmatic access.
	Whenever a db user is created/updated, we fetch a new copy of user metadata from iam and save to disk.
	If the user is updated again in IAM (i.e. password change),
	we will be able to see the change by checking the lastUpdate field in metadata from IAM and thus re-sync again.
*/

func (svc *Service) WriteDBUsersToFile(users map[string]time.Time) (err error) {
	yml, err := yaml.Marshal(users)
	if err != nil {
		return fmt.Errorf("failed to dump to yaml: %w", err)
	}

	err = os.MkdirAll(svc.gsqlDBUserMetaFileFolder, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	return os.WriteFile(svc.gsqlDBUserMetaFilePath, yml, 0666) // #nosec: G306
}

func (svc *Service) ReadDBUsersFromFile() (users map[string]time.Time, err error) {
	users = map[string]time.Time{}

	if !file.Exists(svc.gsqlDBUserMetaFilePath) {
		return users, nil
	}

	buf, err := os.ReadFile(svc.gsqlDBUserMetaFilePath)
	if err != nil {
		return nil, err
	}

	err = yaml.Unmarshal(buf, &users)
	if err != nil {
		return nil, err
	}

	return users, nil
}
