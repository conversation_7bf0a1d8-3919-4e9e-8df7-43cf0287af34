package gsql_user

import (
	"encoding/json"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/tigergraph/cloud-universe/agent/internal/common"
)

const (
	GSQLHelpAPIV4 = "%v/gsql/v1/help"
)

// CheckGSQLServer returns whether the GSQL server in local is responding.
// Note GSQL server only runs in 5 of the nodes in a cluster if the cluster has more than 5 nodes.
func CheckGSQLServer() bool {
	_, err := resty.New().R().Get(fmt.Sprintf(GSQLHelpAPIV4, common.DefaultGSQLServerURL))
	return err == nil
}

type gsqlServerResp struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

func checkIfGSQLServerRespIsError(resp []byte) error {
	gsqlResp := gsqlServerResp{}
	if err := json.Unmarshal(resp, &gsqlResp); err != nil {
		return fmt.Errorf("failed to unmarshall resp: %v", err)
	}

	if gsqlResp.Error {
		return fmt.Errorf("%v", gsqlResp.Message)
	}

	return nil
}
