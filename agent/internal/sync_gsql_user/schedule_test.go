package sync_gsql_user

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type ScheduleTestSuite struct {
	suite.Suite
}

func TestSchedule(t *testing.T) {
	suite.Run(t, new(ScheduleTestSuite))
}

func (s *ScheduleTestSuite) TestSchedule00() {
	pos := 0
	totalWorkers := 4
	interval := 3
	currSecond := 0

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(true, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule02() {
	pos := 0
	totalWorkers := 4
	interval := 3
	currSecond := 2

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(true, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule04() {
	pos := 0
	totalWorkers := 4
	interval := 3
	currSecond := 4

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(false, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule011() {
	pos := 0
	totalWorkers := 4
	interval := 3
	currSecond := 11

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(false, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule012() {
	pos := 0
	totalWorkers := 4
	interval := 3
	currSecond := 12

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(true, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule12() {
	pos := 1
	totalWorkers := 4
	interval := 3
	currSecond := 2

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(false, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule13() {
	pos := 1
	totalWorkers := 4
	interval := 3
	currSecond := 3

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(true, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule15() {
	pos := 1
	totalWorkers := 4
	interval := 3
	currSecond := 5

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(true, myTurn)
}

func (s *ScheduleTestSuite) TestSchedule16() {
	pos := 1
	totalWorkers := 4
	interval := 3
	currSecond := 6

	myTurn := isMyTurn(pos, totalWorkers, currSecond, interval)

	s.Require().Equal(false, myTurn)
}
