package sync_gsql_user

// isMyTurn uses Round-Robin scheduling to determine whether current position's worker shall have the turn to work.
// Example: if totalWorker = 4, interval = 3
// isMyTurn = true for pos = 0's worker if currSecond = (0 to 2)  or (12 to 14) ...
// isMyTurn = true for pos = 1's worker if currSecond = (3 to 5)  or (15 to 17) ...
// isMyTurn = true for pos = 2's worker if currSecond = (6 to 8)  or (18 to 20) ...
// isMyTurn = true for pos = 3's worker if currSecond = (9 to 11) or (21 to 23) ...
func isMyTurn(pos, totalWorkers, currSecond,
	interval int, //nolint:unparam // may change in the future
) bool {
	// interval should not equal to zero.
	if interval == 0 {
		return false
	}

	// totalWorkers should not equal to zero.
	if totalWorkers == 0 {
		return false
	}

	currentTurn := (currSecond / interval) % totalWorkers
	return currentTurn == pos
}
