package sync_gsql_user

import (
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/set"
)

// SyncUsers compares users attached to current solution in IAM and GSQL and
// make necessary changes to match GSQL users with principals listed in IAM policy.

//nolint:gocyclo  // optimize in the future
func (svc *Service) SyncUsers() {
	log := logger.L()
	// Users authenticated by SSO OIDC (humman users).
	humanUsersInIAM, err := svc.getAttachedUserFromIAM()
	if err != nil {
		log.Errorf("Failed to get users in IAM: %v.", err)
		return
	}

	dbUsersInIAM, err := svc.getInDatabaseGSQLUsersFromIAM()
	if err != nil {
		log.Errorf("Failed to get db users in IAM: %v.", err)
		return
	}

	// Note reserved users (currently only tigergraph) will be excluded from GetExistingGSQLUsers's output.
	allUsersInGSQL, err := svc.gsqlUserSvc.GetExistingGSQLUsers(true)
	if err != nil {
		log.Errorf("Failed to get users in GSQL: %v.", err)
		return
	}

	// For db (programmatic access) users in GSQL, in addition to adding it in GSQL,
	// we also save a copy of metadata of these users from IAM on disk to track password changing event.
	// Note this file will only be used to check if a password update is needed for db users.
	// IAM is still the single source of truth for adding/removing GSQL users.
	dbUsersInYAMLFile, err := svc.gsqlUserSvc.ReadDBUsersFromFile()
	if err != nil {
		log.Errorf("Failed to get db user meta from file: %v.", err)
		return
	}

	toAddHumanUsers, toAddDBUsers, toUpdateDBUsers, toRemoveUsers, toAssignRoles, toRevokeRoles := svc.GetUsersToBeUpdated(humanUsersInIAM, dbUsersInIAM, allUsersInGSQL, dbUsersInYAMLFile)

	// If any changes are needed, get users from GSQL again without using cache in case it is due to outdated cache.
	if len(toAddHumanUsers) > 0 || len(toAddDBUsers) > 0 || len(toRemoveUsers) > 0 || len(toUpdateDBUsers) > 0 || len(toAssignRoles) > 0 || len(toRevokeRoles) > 0 {
		allUsersInGSQL, err = svc.gsqlUserSvc.GetExistingGSQLUsers(false)
		if err != nil {
			log.Errorf("Failed to get users in GSQL: %v.", err)
			return
		}
		toAddHumanUsers, toAddDBUsers, toUpdateDBUsers, toRemoveUsers, toAssignRoles, toRevokeRoles = svc.GetUsersToBeUpdated(humanUsersInIAM, dbUsersInIAM, allUsersInGSQL, dbUsersInYAMLFile)
	}

	for u, role := range toAssignRoles {
		if err := svc.gsqlUserSvc.AssignGSQLUserRole([]string{u}, role); err != nil {
			log.Errorf("Failed to assign user to superuser role %v: %v.", u, err)
		} else {
			log.Infof("Successfully granted user: %v %v role.", u, role)
		}
	}

	for u, role := range toRevokeRoles {
		if err := svc.gsqlUserSvc.RevokeGSQLUserRole(u, role); err != nil {
			log.Errorf("Failed to revoke superuser role from user %v: %v.", u, err)
		} else {
			log.Infof("Successfully revoked superuser role from user: %v.", u)
		}
	}

	// Note: for new human users to be added to GSQL, if the user belongs to admin groups in IAM,
	// the user will be granted with superuser role in GSQL.
	for _, u := range toAddHumanUsers {
		if err := svc.gsqlUserSvc.CreateGSQLUser(u); err != nil {
			log.Errorf("Failed to create gsql user %v: %v.", u, err)
			continue
		}

		log.Infof("Successfully added user: %v to GSQL.", u)

		gsqlUserRole := humanUsersInIAM[u]
		if err := svc.gsqlUserSvc.AssignGSQLUserRole([]string{u}, gsqlUserRole); err != nil {
			log.Errorf("Failed to assign user to %v role %v: %v.", gsqlUserRole, u, err)
		} else {
			log.Infof("Successfully granted user: %v %v role.", u, gsqlUserRole)
		}
	}

	for _, u := range toAddDBUsers {
		password, err := svc.getInDatabaseGSQLUserPasswordFromIAM(u)
		if err != nil {
			dbUsersInIAM[u] = dbUsersInYAMLFile[u]
			log.Errorf("Failed to get password for db user %v: %v", u, err)
			continue
		}

		err = svc.gsqlUserSvc.CreateGSQLUserWithPassword(u, password)
		if err != nil {
			dbUsersInIAM[u] = dbUsersInYAMLFile[u]
			log.Errorf("Failed to create db user %v: %v", u, err)
		} else {
			log.Infof("Successfully added db user: %v", u)
		}
	}

	for _, u := range toUpdateDBUsers {
		password, err := svc.getInDatabaseGSQLUserPasswordFromIAM(u)
		if err != nil {
			dbUsersInIAM[u] = dbUsersInYAMLFile[u]
			log.Errorf("Failed to get password for db user %v: %v", u, err)
			continue
		}

		err = svc.gsqlUserSvc.ChangeGSQLUserPassword(u, password)
		if err != nil {
			if !strings.Contains(err.Error(), "The new password cannot be the same as old password.") {
				dbUsersInIAM[u] = dbUsersInYAMLFile[u]
			}
			log.Errorf("Failed to update password for db user %v: %v", u, err)
		} else {
			log.Infof("Successfully updated db user password for: %v", u)
		}
	}

	for _, u := range toRemoveUsers {
		if err := svc.gsqlUserSvc.DeleteGSQLUser(u); err != nil {
			dbUsersInIAM[u] = dbUsersInYAMLFile[u]
			log.Errorf("Failed to remove user %v: %v.", u, err)
		} else {
			log.Infof("Successfully removed user: %v.", u)
		}
	}

	if len(toAddDBUsers) > 0 || len(toRemoveUsers) > 0 || len(toUpdateDBUsers) > 0 {
		if err := svc.gsqlUserSvc.WriteDBUsersToFile(dbUsersInIAM); err != nil {
			log.Errorf("Failed to save IAM db users into file: %v", err)
		} else {
			log.Info("Successfully saved IAM db users into file.")
		}
	}
}

func (svc *Service) GetUsersToBeUpdated(
	humanUsersInIAM map[string]string,
	dbUsersInIAM map[string]time.Time,
	allUsersInGSQL map[string]*gsql_user.GSQLUser,
	dbUsersInYAMLFile map[string]time.Time,
) (toAddHumanUsers,
	toAddDBUsers,
	toUpdateDBUsers,
	toRemoveUsers []string,
	toAssignRoles,
	toRevokeRoles map[string]string,
) {
	toAddHumanUsers = []string{}
	toAddDBUsers = []string{}
	toUpdateDBUsers = []string{}
	toRemoveUsers = []string{}
	toAssignRoles = map[string]string{}
	toRevokeRoles = map[string]string{}

	// Check to add human users.
	for u := range humanUsersInIAM {
		if allUsersInGSQL[u] == nil {
			toAddHumanUsers = append(toAddHumanUsers, u)
		}
	}

	// Check to add db users.
	for u := range dbUsersInIAM {
		if allUsersInGSQL[u] == nil {
			toAddDBUsers = append(toAddDBUsers, u)
		}
	}

	// Check to remove users.
	for _, u := range allUsersInGSQL {
		var isHumanUserInIAM, isDBUserInIAM bool
		_, isHumanUserInIAM = humanUsersInIAM[u.Name]
		_, isDBUserInIAM = dbUsersInIAM[u.Name]
		if !isHumanUserInIAM && !isDBUserInIAM {
			toRemoveUsers = append(toRemoveUsers, u.Name)
		}
	}

	// Check to update role of human users and update password of db users.
	for _, u := range allUsersInGSQL {
		if _, ok := humanUsersInIAM[u.Name]; ok {
			gsqlUserRolesGSQL := set.New()
			if roles, ok := u.Roles["1"]; ok {
				for _, role := range roles {
					gsqlUserRolesGSQL.Add(role)
				}
			}
			gsqlUserRoleIAM := humanUsersInIAM[u.Name]

			isAdmin := gsqlUserRoleIAM == "superuser"
			if isAdmin != u.IsSuperUser {
				if !gsqlUserRolesGSQL.Has(gsqlUserRoleIAM) {
					toAssignRoles[u.Name] = gsqlUserRoleIAM
				}

				if !isAdmin {
					toRevokeRoles[u.Name] = string(tgIAM.GSQLSuperUser)
				}
			}
		}

		if _, ok := dbUsersInIAM[u.Name]; ok {
			if dbUsersInYAMLFile[u.Name].Before(dbUsersInIAM[u.Name]) {
				toUpdateDBUsers = append(toUpdateDBUsers, u.Name)
			}
		}
	}

	return toAddHumanUsers, toAddDBUsers, toUpdateDBUsers, toRemoveUsers, toAssignRoles, toRevokeRoles
}
