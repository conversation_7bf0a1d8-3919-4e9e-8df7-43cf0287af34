package sync_gsql_user

import (
	"fmt"

	"github.com/google/uuid"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	gsqlUser "github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user"
	"github.com/tigergraph/cloud-universe/tgIAM"
)

// loadConfig loads soliution id, org id, auth0 and iam configs into the service.
// loadConfig also creates all dependent services if missing or configs are updated.
func (svc *Service) loadConfig() error {
	// Load meta data from userConfig file.
	workspaceMeta, err := loadWorkspaceMeta()
	if err != nil {
		return err
	}

	svc.orgID = workspaceMeta.OrgID
	svc.workgroupID = workspaceMeta.WorkgroupID
	svc.workspaceID = workspaceMeta.WorkspaceID

	// Initialize IAM V2
	if config.Config.IAMServer == "" {
		return fmt.Errorf("IAM server not configured")
	}
	svc.iamV2Svc, err = tgIAM.NewTGIAMService(
		config.Config.IAMServer,
		"",
		nil)
	if err != nil {
		return err
	}

	svc.iamAPIKey = config.Config.IAMAPIKey
	if svc.gsqlUserSvc == nil {
		svc.gsqlUserSvc = gsqlUser.New(
			config.Config.SystemAuthToken,
			workspaceMeta.GSQLDBUserMetaFileFolder,
			fmt.Sprintf("%v/gsql_db_user_meta_%v.yml", workspaceMeta.GSQLDBUserMetaFileFolder, workspaceMeta.WorkspaceID),
			svc.gsqlServerPort,
		)
	}

	return nil
}

type workspaceMeta struct {
	WorkgroupID              uuid.UUID
	WorkspaceID              uuid.UUID
	OrgID                    string
	TigergraphPassword       string
	GSQLDBUserMetaFileFolder string
}

func loadWorkspaceMeta() (*workspaceMeta, error) {
	workspaceMeta := &workspaceMeta{}

	var err error
	workspaceMeta.WorkgroupID, err = uuid.Parse(config.Config.WorkgroupId)
	if err != nil {
		return nil, err
	}

	workspaceMeta.WorkspaceID, err = uuid.Parse(config.Config.WorkspaceId)
	if err != nil {
		return nil, err
	}

	workspaceMeta.OrgID = config.Config.OrgId
	workspaceMeta.TigergraphPassword, err = common.ReadTigerGraphPassword()
	if err != nil {
		return nil, err
	}
	workspaceMeta.GSQLDBUserMetaFileFolder = config.Config.GSQLDBUserMetaFileFolder

	if workspaceMeta.WorkgroupID == uuid.Nil {
		return nil, fmt.Errorf("workgroup id is empty")
	}

	if workspaceMeta.WorkspaceID == uuid.Nil {
		return nil, fmt.Errorf("workspace id is empty")
	}

	if workspaceMeta.OrgID == "" {
		return nil, fmt.Errorf("org id is empty")
	}

	if workspaceMeta.TigergraphPassword == "" {
		return nil, fmt.Errorf("tg password is empty")
	}

	if workspaceMeta.GSQLDBUserMetaFileFolder == "" {
		return nil, fmt.Errorf("gsql db user meta file folder is empty")
	}

	return workspaceMeta, nil
}
