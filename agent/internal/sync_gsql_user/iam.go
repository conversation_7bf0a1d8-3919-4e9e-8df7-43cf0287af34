package sync_gsql_user

import (
	"context"
	"time"

	"github.com/tigergraph/cloud-universe/tgIAM"
)

// (V2) getAttachedUserFromIAM returns list of emails of users attached to this workspace.
func (svc *Service) getAttachedUserFromIAM() (map[string]string, error) {
	users, err := svc.iamV2Svc.GetWorkspaceAttachedUsers(context.Background(), svc.iamAPIKey, svc.orgID, svc.workgroupID.String(), svc.workspaceID.String())
	if err != nil {
		return nil, err
	}

	return users, nil
}

func (svc *Service) getInDatabaseGSQLUsersFromIAM() (map[string]time.Time, error) {
	dbUsers, err := svc.iamV2Svc.ListInDatabaseGSQLUsers(context.Background(), &tgIAM.AuthToken{
		APIKey: &svc.iamAPIKey,
	}, svc.orgID, svc.workgroupID.String(), svc.workspaceID.String())
	if err != nil {
		return nil, err
	}

	users := map[string]time.Time{}
	for _, dbUser := range dbUsers {
		users[dbUser.Username] = dbUser.Updated
	}

	return users, nil
}

func (svc *Service) getInDatabaseGSQLUserPasswordFromIAM(username string) (string, error) {
	return svc.iamV2Svc.GetInDatabaseGSQLUserPassword(context.Background(), svc.iamAPIKey, svc.orgID, svc.workgroupID.String(), svc.workspaceID.String(), username)
}
