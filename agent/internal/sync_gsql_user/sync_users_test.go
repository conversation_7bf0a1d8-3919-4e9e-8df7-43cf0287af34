package sync_gsql_user

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"

	"github.com/tigergraph/cloud-universe/tgIAM"

	"github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user"
	mockGSQLUserSvc "github.com/tigergraph/cloud-universe/agent/internal/sync_gsql_user/gsql_user/mocks"
)

var testIAMAPIKey = "test-key"

const (
	orgID         = "org_123"
	userEmail1    = "<EMAIL>"
	userEmail2    = "<EMAIL>"
	userEmail3    = "<EMAIL>"
	databaseUser1 = "user001"
	databaseUser2 = "user002"
	password      = "password"
)

func TestRunAll(t *testing.T) {
	TestNoChangeNeeded(t)
	TestAddRegularUser(t)
	TestNegAddRegularUser(t)
	TestAddDatabaseUser(t)
	TestNegAddDatabaseUser(t)
	TestRemoveDatabaseUser(t)
	TestNegRemoveDatabaseUser(t)
	TestChangeDatabaseUserPassword(t)
	TestNegChangeDatabaseUserPassword(t)
	TestAddSuperUser(t)
	TestRemoveSuperUser(t)
	TestSwitchGSQLUserRole(t)
	TestNegSwitchGSQLUserRole(t)
	TestDeleteUser(t)
	TestNeedBothAddAndDelete(t)
}

func TestNoChangeNeeded(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	updateTime := time.Now()

	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{
		{
			Updated:  updateTime,
			Username: databaseUser1,
		},
	}
	databaseUsersFile := map[string]time.Time{
		databaseUser1: updateTime,
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
		},
		userEmail2: {
			Name:        userEmail2,
			IsSuperUser: false,
		},
		databaseUser1: {
			Name:        databaseUser1,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)
	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestAddRegularUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "globalobserver",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("CreateGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLUser)).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNegAddRegularUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "globalobserver",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("CreateGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLUser)).Return(fmt.Errorf("Error"))

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestAddDatabaseUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	updateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{
		{
			Updated:  updateTime,
			Username: databaseUser1,
		},
		{
			Updated:  updateTime,
			Username: databaseUser2,
		},
	}
	databaseUsersFile := map[string]time.Time{}
	databaseUsersFileSave := map[string]time.Time{
		databaseUser1: updateTime,
		databaseUser2: updateTime,
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser1).Return(password, nil)
	mockedGSQLSvc.On("CreateGSQLUserWithPassword", databaseUser1, password).Return(nil)
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser2).Return(password, nil)
	mockedGSQLSvc.On("CreateGSQLUserWithPassword", databaseUser2, password).Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFileSave).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNegAddDatabaseUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	updateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{
		{
			Updated:  updateTime,
			Username: databaseUser1,
		},
		{
			Updated:  updateTime,
			Username: databaseUser2,
		},
	}
	databaseUsersFile := map[string]time.Time{}
	databaseUsersFileSave := map[string]time.Time{
		databaseUser1: {},
		databaseUser2: {},
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser1).Return("", fmt.Errorf("Error"))
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser2).Return(password, nil)
	mockedGSQLSvc.On("CreateGSQLUserWithPassword", databaseUser2, password).Return(fmt.Errorf("Error"))
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFileSave).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestRemoveDatabaseUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	updateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{
		databaseUser1: updateTime,
		databaseUser2: updateTime,
	}
	databaseUsersFileSave := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		databaseUser1: {
			Name:        databaseUser1,
			IsSuperUser: false,
		},
		databaseUser2: {
			Name:        databaseUser2,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("DeleteGSQLUser", databaseUser1).Return(nil)
	mockedGSQLSvc.On("DeleteGSQLUser", databaseUser2).Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFileSave).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNegRemoveDatabaseUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	updateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{
		databaseUser1: updateTime,
		databaseUser2: updateTime,
	}
	databaseUsersFileSave := map[string]time.Time{
		databaseUser1: updateTime,
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		databaseUser1: {
			Name:        databaseUser1,
			IsSuperUser: false,
		},
		databaseUser2: {
			Name:        databaseUser2,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("DeleteGSQLUser", databaseUser1).Return(fmt.Errorf("Error"))
	mockedGSQLSvc.On("DeleteGSQLUser", databaseUser2).Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFileSave).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestChangeDatabaseUserPassword(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	oldUpdateTime := time.Now().Add(-time.Hour)
	newUpdateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{
		{
			Updated:  newUpdateTime,
			Username: databaseUser1,
		},
	}
	databaseUsersFile := map[string]time.Time{
		databaseUser1: oldUpdateTime,
	}
	databaseUsersFileSave := map[string]time.Time{
		databaseUser1: newUpdateTime,
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		databaseUser1: {
			Name:        databaseUser1,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser1).Return(password, nil)
	mockedGSQLSvc.On("ChangeGSQLUserPassword", databaseUser1, password).Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFileSave).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNegChangeDatabaseUserPassword(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	oldUpdateTime := time.Now().Add(-time.Hour)
	newUpdateTime := time.Now()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{
		{
			Updated:  newUpdateTime,
			Username: databaseUser1,
		},
		{
			Updated:  newUpdateTime,
			Username: databaseUser2,
		},
	}
	databaseUsersFile := map[string]time.Time{
		databaseUser1: oldUpdateTime,
		databaseUser2: oldUpdateTime,
	}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		databaseUser1: {
			Name:        databaseUser1,
			IsSuperUser: false,
		},
		databaseUser2: {
			Name:        databaseUser2,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser1).Return("", fmt.Errorf("Error"))
	mockedIAMV2.On("GetInDatabaseGSQLUserPassword", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String(), databaseUser2).Return(password, nil)
	mockedGSQLSvc.On("ChangeGSQLUserPassword", databaseUser2, password).Return(fmt.Errorf("Error"))
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFile).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestAddSuperUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "superuser",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("CreateGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLSuperUser)).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestRemoveSuperUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "globalobserver",
		userEmail2: "globalobserver",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
			Roles: map[string][]string{
				"1": {"superuser"},
			},
		},
		userEmail2: {
			Name:        userEmail2,
			IsSuperUser: true,
			Roles: map[string][]string{
				"1": {"superuser", "globalobserver"},
			},
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("RevokeGSQLUserRole", "<EMAIL>", string(tgIAM.GSQLSuperUser)).Return(nil)
	mockedGSQLSvc.On("RevokeGSQLUserRole", "<EMAIL>", string(tgIAM.GSQLSuperUser)).Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLUser)).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestSwitchGSQLUserRole(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "globalobserver",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: false,
			Roles: map[string][]string{
				"1": {"globalobserver"},
			},
		},
		userEmail2: {
			Name:        userEmail2,
			IsSuperUser: true,
			Roles: map[string][]string{
				"1": {"superuser"},
			},
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLSuperUser)).Return(nil)
	mockedGSQLSvc.On("RevokeGSQLUserRole", "<EMAIL>", string(tgIAM.GSQLSuperUser)).Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLUser)).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNegSwitchGSQLUserRole(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "superuser",
		userEmail2: "globalobserver",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: false,
			Roles: map[string][]string{
				"1": {"globalobserver"},
			},
		},
		userEmail2: {
			Name:        userEmail2,
			IsSuperUser: true,
			Roles: map[string][]string{
				"1": {"superuser"},
			},
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLSuperUser)).Return(fmt.Errorf("Error"))
	mockedGSQLSvc.On("RevokeGSQLUserRole", "<EMAIL>", string(tgIAM.GSQLSuperUser)).Return(fmt.Errorf("Error"))
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLUser)).Return(fmt.Errorf("Error"))

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestDeleteUser(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: true,
		},
		userEmail2: {
			Name:        userEmail2,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("DeleteGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("DeleteGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFile).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}

func TestNeedBothAddAndDelete(t *testing.T) {
	workgroupID := uuid.New()
	workspaceID := uuid.New()
	authToken := &tgIAM.AuthToken{APIKey: &testIAMAPIKey}
	iamUsers := map[string]string{
		userEmail1: "",
		userEmail2: "superuser",
	}
	databaseUsersIAM := []*tgIAM.IAMUser{}
	databaseUsersFile := map[string]time.Time{}
	gsqlUsers := map[string]*gsql_user.GSQLUser{
		userEmail1: {
			Name:        userEmail1,
			IsSuperUser: false,
		},
		userEmail3: {
			Name:        userEmail3,
			IsSuperUser: false,
		},
	}

	mockedIAMV2 := new(tgIAM.MockTGIAM)
	mockedGSQLSvc := new(mockGSQLUserSvc.MockGSQLUserService)

	mockedIAMV2.On("GetWorkspaceAttachedUsers", testIAMAPIKey, orgID, workgroupID.String(), workspaceID.String()).Return(iamUsers, nil)
	mockedIAMV2.On("ListInDatabaseGSQLUsers", authToken, orgID, workgroupID.String(), workspaceID.String()).Return(databaseUsersIAM, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", true).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("ReadDBUsersFromFile").Return(databaseUsersFile, nil)
	mockedGSQLSvc.On("GetExistingGSQLUsers", false).Return(gsqlUsers, nil)
	mockedGSQLSvc.On("CreateGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("AssignGSQLUserRole", []string{"<EMAIL>"}, string(tgIAM.GSQLSuperUser)).Return(nil)
	mockedGSQLSvc.On("DeleteGSQLUser", "<EMAIL>").Return(nil)
	mockedGSQLSvc.On("WriteDBUsersToFile", databaseUsersFile).Return(nil)

	syncSvc := &Service{
		iamV2Svc:    mockedIAMV2,
		iamAPIKey:   testIAMAPIKey,
		gsqlUserSvc: mockedGSQLSvc,
		orgID:       orgID,
		workgroupID: workgroupID,
		workspaceID: workspaceID,
	}

	syncSvc.SyncUsers()
	mockedIAMV2.AssertExpectations(t)
	mockedGSQLSvc.AssertExpectations(t)
}
