package config

import (
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
	"github.com/tigergraph/cloud-universe/utils/codec"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

var SecretEncryptionKey string // Pass from argument
var Config AgentConfig

type AgentConfig struct {
	WorkspaceName                          string `validate:"required" mapstructure:"WORKSPACE_NAME"`
	OrgId                                  string `validate:"required" mapstructure:"ORG_ID"`
	WorkgroupId                            string `validate:"required" mapstructure:"WORKGROUP_ID"`
	WorkspaceId                            string `validate:"required" mapstructure:"WORKSPACE_ID"`
	IAMServer                              string `validate:"required" mapstructure:"IAM_SERVER"`
	SQSURL                                 string `validate:"required" mapstructure:"SQS_URL"`
	TGPassword                             string `validate:"required" mapstructure:"TG_PASSWORD"`
	SystemAuthToken                        string `validate:"required" mapstructure:"SYSTEM_AUTH_TOKEN"`
	IAMAPIKey                              string `validate:"required" mapstructure:"IAM_API_KEY"`
	GSQLDBUserMetaFileFolder               string `validate:"required" mapstructure:"GSQL_DB_USER_META_FILE_FOLDER"`
	EnableTopologyForceUpdate              bool   `validate:"required" mapstructure:"ENABLE_TOPOLOGY_FORCE_UPDATE"`
	HeartbeatIntervalSeconds               int    `validate:"required" mapstructure:"HEARTBEAT_INTERVAL_SECONDS"`
	TopologyUpdateIntervalSeconds          int    `validate:"required" mapstructure:"TOPOLOGY_UPDATE_INTERVAL_SECONDS"`
	CatalogActivityUpdateIntervalSeconds   int    `validate:"required" mapstructure:"CATALOG_ACTIVITY_UPDATE_INTERVAL_SECONDS"`
	QueryActivityUpdateIntervalSeconds     int    `validate:"required" mapstructure:"QUERY_ACTIVITY_UPDATE_INTERVAL_SECONDS"`
	LicenseExpirationUpdateIntervalSeconds int    `mapstructure:"LICENSE_EXPIRATION_UPDATE_INTERVAL_SECONDS"`
	RoleARN                                string `mapstructure:"ROLE_ARN"`
	NGINXLogDir                            string `mapstructure:"NGINX_LOG_DIR"`    // default /log/nginx/logs
	GSQLServerPort                         int    `mapstructure:"GSQL_SERVER_PORT"` // default 8192
	EnableEncryption                       bool   `mapstructure:"ENABLE_ENCRYPTION"`
	EncryptionPort                         int    `mapstructure:"ENCRYPTION_PORT"` // default 8090
}

func LoadConfig() {
	log := logger.L()
	viper.SetConfigName("tgagent")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")

	// Set default values
	Config.NGINXLogDir = "/log/nginx/logs"
	Config.GSQLServerPort = 8123
	Config.EncryptionPort = 8090
	//TODO: update to 3600
	Config.LicenseExpirationUpdateIntervalSeconds = 10

	viper.AutomaticEnv()
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			panic(err)
		}
		fmt.Println("Config file not found; reading from environment")
	} else {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	}
	err := viper.Unmarshal(&Config)
	if err != nil {
		panic(err)
	}

	// Decrypt secrets.
	iamAPIKey, err := codec.AESGCMDecrypt(SecretEncryptionKey, Config.IAMAPIKey)
	if err != nil {
		log.Infof("Failed to decrypt iam api key: %v", err)
	}

	systemAuthToken, err := codec.AESGCMDecrypt(SecretEncryptionKey, Config.SystemAuthToken)
	if err != nil {
		log.Infof("Failed to decrypt system auth token: %v", err)
	}

	tgPassword, err := codec.AESGCMDecrypt(SecretEncryptionKey, Config.TGPassword)
	if err != nil {
		log.Infof("Failed to decrypt tigergraph password: %v", err)
	}
	Config.IAMAPIKey = iamAPIKey
	Config.SystemAuthToken = systemAuthToken
	Config.TGPassword = tgPassword

	log.Infof("WorkspaceName: %v", Config.WorkspaceName)
	log.Infof("OrgId: %v", Config.OrgId)
	log.Infof("WorkgroupId: %v", Config.WorkgroupId)
	log.Infof("WorkspaceId: %v", Config.WorkspaceId)
	log.Infof("IAMServer: %v", Config.IAMServer)
	log.Infof("SQSURL: %v", Config.SQSURL)
	log.Infof("GSQLDBUserMetaFileFolder: %v", Config.GSQLDBUserMetaFileFolder)
	log.Infof("RoleARN: %v", Config.RoleARN)
	log.Infof("NGINXLogDir: %v", Config.NGINXLogDir)
	log.Infof("GSQLServerPort: %v", Config.GSQLServerPort)
	log.Infof("EnableEncryption: %v", Config.EnableEncryption)
	log.Infof("EncryptionPort: %v", Config.EncryptionPort)

	log.Infof("EnableTopologyForceUpdate: %v", Config.EnableTopologyForceUpdate)
	log.Infof("HeartbeatIntervalSeconds: %v", Config.HeartbeatIntervalSeconds)
	log.Infof("TopologyUpdateIntervalSeconds: %v", Config.TopologyUpdateIntervalSeconds)
	log.Infof("CatalogActivityUpdateIntervalSeconds: %v", Config.CatalogActivityUpdateIntervalSeconds)
	log.Infof("QueryActivityUpdateIntervalSeconds: %v", Config.QueryActivityUpdateIntervalSeconds)

	if err = validator.New().Struct(Config); err != nil {
		panic(fmt.Errorf("invalid config: %v", err))
	}
}
