package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoadConfigFail(t *testing.T) {
	if os.Getenv("CI") != "" {
		t.Skip("Skipping testing in CI environment")
	}

	os.Unsetenv("WORKSPACE_NAME")
	os.Unsetenv("ORG_ID")
	os.Unsetenv("WORKGROUP_ID")
	os.Unsetenv("WORKSPACE_ID")
	os.Unsetenv("AUTH0_DOMAIN")
	os.Unsetenv("IAM_SERVER")
	os.Unsetenv("SQS_URL")
	os.Unsetenv("M2M_CLIENT_ID")
	os.Unsetenv("M2M_CLIENT_SECRET")
	os.Unsetenv("GSQL_DB_USER_META_FILE_FOLDER")
	os.Unsetenv("TG_PASSWORD")
	os.Unsetenv("ROLE_ARN")

	// Expect panic since `TG_PASSWORD` is not set
	os.Setenv("WORKSPACE_NAME", "tg-f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("ORG_ID", "org_qwerty")
	os.Setenv("WORKGROUP_ID", "cfbad676-5b90-48ac-80bd-f6257f0fc54e")
	os.Setenv("WORKSPACE_ID", "f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("AUTH0_DOMAIN", "tgcloud-dev.auth0.com")
	os.Setenv("IAM_SERVER", "https://api.tgcloud-dev.com/iam")
	os.Setenv("SQS_URL", "https://sqs.us-east-1.amazonaws.com/123456789/agent-heatbeat")
	os.Setenv("M2M_CLIENT_ID", "qwerty")
	os.Setenv("M2M_CLIENT_SECRET", "asdfghjkl")
	os.Setenv("GSQL_DB_USER_META_FILE_FOLDER", "/path/to")
	//os.Setenv("TG_PASSWORD", "zxcvbnm")
	os.Setenv("ROLE_ARN", "arn:aws:iam::123456789:role/workgroup-6bbfe57e-c781-44e3-9331-cf20b3470af3-sa-role")

	assert.Panics(t, LoadConfig, "`LoadConfig` did not panic")
}

func TestLoadConfig(t *testing.T) {
	os.Unsetenv("WORKSPACE_NAME")
	os.Unsetenv("ORG_ID")
	os.Unsetenv("WORKGROUP_ID")
	os.Unsetenv("WORKSPACE_ID")
	os.Unsetenv("AUTH0_DOMAIN")
	os.Unsetenv("IAM_SERVER")
	os.Unsetenv("SQS_URL")
	os.Unsetenv("M2M_CLIENT_ID")
	os.Unsetenv("M2M_CLIENT_SECRET")
	os.Unsetenv("GSQL_DB_USER_META_FILE_FOLDER")
	os.Unsetenv("TG_PASSWORD")
	os.Unsetenv("ROLE_ARN")

	os.Setenv("WORKSPACE_NAME", "tg-f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("ORG_ID", "org_qwerty")
	os.Setenv("WORKGROUP_ID", "cfbad676-5b90-48ac-80bd-f6257f0fc54e")
	os.Setenv("WORKSPACE_ID", "f8b57bb0-960b-453e-b74d-4f264de45fef")
	os.Setenv("AUTH0_DOMAIN", "tgcloud-dev.auth0.com")
	os.Setenv("IAM_SERVER", "https://api.tgcloud-dev.com/iam")
	os.Setenv("SQS_URL", "https://sqs.us-east-1.amazonaws.com/123456789/agent-heatbeat")
	os.Setenv("M2M_CLIENT_ID", "qwerty")
	os.Setenv("M2M_CLIENT_SECRET", "asdfghjkl")
	os.Setenv("GSQL_DB_USER_META_FILE_FOLDER", "/path/to")
	os.Setenv("TG_PASSWORD", "zxcvbnm")
	os.Setenv("SYSTEM_AUTH_TOKEN", "token")
	os.Setenv("IAM_API_KEY", "key")
	os.Setenv("ENABLE_TOPOLOGY_FORCE_UPDATE", "true")
	os.Setenv("HEARTBEAT_INTERVAL_SECONDS", "60")
	os.Setenv("TOPOLOGY_UPDATE_INTERVAL_SECONDS", "30")
	os.Setenv("CATALOG_ACTIVITY_UPDATE_INTERVAL_SECONDS", "30")
	os.Setenv("QUERY_ACTIVITY_UPDATE_INTERVAL_SECONDS", "30")
	os.Setenv("ROLE_ARN", "arn:aws:iam::123456789:role/workgroup-6bbfe57e-c781-44e3-9331-cf20b3470af3-sa-role")

	os.Setenv("ENABLE_TOPOLOGY_FORCE_UPDATE", "true")
	os.Setenv("HEARTBEAT_INTERVAL_SECONDS", "60")
	os.Setenv("CATALOG_ACTIVITY_UPDATE_INTERVAL_SECONDS", "60")
	os.Setenv("QUERY_ACTIVITY_UPDATE_INTERVAL_SECONDS", "60")

	LoadConfig()
	assert.Equal(t, "zxcvbnm", Config.TGPassword)
	assert.Equal(t, "https://sqs.us-east-1.amazonaws.com/123456789/agent-heatbeat", Config.SQSURL)
	assert.Equal(t, "/log/nginx/logs", Config.NGINXLogDir)
	assert.Equal(t, 8123, Config.GSQLServerPort)
	assert.Equal(t, 30, Config.TopologyUpdateIntervalSeconds)

	os.Unsetenv("HEARTBEAT_INTERVAL_SECONDS")
	os.Setenv("HEARTBEAT_INTERVAL_SECONDS", "90")
	os.Unsetenv("NGINX_LOG_DIR")
	os.Setenv("NGINX_LOG_DIR", "/etc/nginx_log")
	os.Unsetenv("GSQL_SERVER_PORT")
	os.Setenv("GSQL_SERVER_PORT", "8123")
	LoadConfig()
	assert.Equal(t, 90, Config.HeartbeatIntervalSeconds)
	assert.Equal(t, "/etc/nginx_log", Config.NGINXLogDir)
	assert.Equal(t, 8123, Config.GSQLServerPort)
}
