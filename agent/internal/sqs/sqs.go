package sqs

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials/stscreds"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	utils "github.com/tigergraph/cloud-universe/utils/aws"
)

func Send(content string) error {

	queueURL := config.Config.SQSURL
	roleARN := config.Config.RoleARN

	sqsRegion, err := utils.ExtractSQSRegionFromUrl(queueURL)
	if err != nil {
		return err
	}

	sess := session.Must(session.NewSession(
		&aws.Config{
			Region: aws.String(sqsRegion),
		},
	))
	creds := stscreds.NewCredentials(sess, roleARN)

	var svc *sqs.SQS
	//TODO: validate role arn
	if roleARN != "" {
		svc = sqs.New(sess, &aws.Config{Credentials: creds})
	} else {
		svc = sqs.New(sess)
	}

	_, err = svc.SendMessage(&sqs.SendMessageInput{
		DelaySeconds: aws.Int64(10),
		MessageAttributes: map[string]*sqs.MessageAttributeValue{
			"Version": {
				DataType:    aws.String("String"),
				StringValue: aws.String("v1"),
			},
		},
		MessageBody: aws.String(content),
		QueueUrl:    &queueURL,
	})

	return err
}
