package kms

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/resource-manager/operator"
)

func TestRequireSystemAuthToken(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Mock the expected system auth token
	config.Config.SystemAuthToken = "valid-token"

	// Initialize the middleware
	kmsServer := &KMSServer{}
	middleware := kmsServer.RequireSystemAuthToken()

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "Valid Token",
			authHeader:     "Bearer valid-token",
			expectedStatus: http.StatusOK,
			expectedBody:   "Success",
		},
		{
			name:           "Missing Token",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   "Invalid auth token",
		},
		{
			name:           "Malformed Token",
			authHeader:     "invalid-format-token",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   "Invalid auth token",
		},
		{
			name:           "Invalid Token",
			authHeader:     "Bearer wrong-token",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   "Unauthorized auth token",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new Gin router and apply the middleware
			router := gin.New()
			router.GET("/test", middleware, func(c *gin.Context) {
				c.String(http.StatusOK, "Success")
			})

			// Create a request
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			// Record the response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assertions
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)
		})
	}
}

func TestRetrievePlainTextDEK2(t *testing.T) {
	operator.EncryptionMountPath = "/tmp"
	config.SecretEncryptionKey = "6368616e676520746869732070617373"
	dekPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSEncryptedDEKSecretKey)
	externalIDPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSExternalIDSecretKey)
	keyIDPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSKeyIDSecretKey)
	regionPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSRegionSecretKey)
	roleARNPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSRoleARNSecretKey)

	if err := os.MkdirAll(operator.EncryptionMountPath, 0770); err != nil {
		t.Fatalf("Failed to create directory: %v", err)
	}

	kmsServer := &KMSServer{}

	tests := []struct {
		name           string
		createFiles    []string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "Disabled Encryption",
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "Failed to retrieve plaintext dek: encryption is not enabled",
		},
		{
			name:           "Missing Data Encryption Key File",
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: data encryption key file does not exist",
		},
		{
			name:           "Missing External ID File",
			createFiles:    []string{dekPath},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: external id file does not exist",
		},
		{
			name:           "Missing Key ID File",
			createFiles:    []string{dekPath, externalIDPath},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: key id file does not exist",
		},
		{
			name:           "Missing Region File",
			createFiles:    []string{dekPath, externalIDPath, keyIDPath},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: region file does not exist",
		},
		{
			name:           "Missing Role ARN File",
			createFiles:    []string{dekPath, externalIDPath, keyIDPath, regionPath},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: role arn file does not exist",
		},
		{
			name:           "Fail to Decrypt Data with KMS",
			createFiles:    []string{dekPath, externalIDPath, keyIDPath, regionPath, roleARNPath},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   "Failed to retrieve plaintext dek: InvalidParameter:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name != "Disabled Encryption" {
				config.Config.EnableEncryption = true
			}

			for _, file := range tt.createFiles {
				if _, err := os.Create(file); err != nil {
					t.Fatalf("Failed to create file: %v", err)
				}
			}

			defer func() {
				for _, file := range tt.createFiles {
					os.Remove(file)
				}
			}()

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(
				http.MethodGet,
				"/",
				nil,
			)

			kmsServer.RetrievePlaintextDEK(c)
			assert.Contains(t, w.Body.String(), tt.expectedBody)
			assert.Equal(t, c.Writer.Status(), tt.expectedStatus)
		})
	}
}
