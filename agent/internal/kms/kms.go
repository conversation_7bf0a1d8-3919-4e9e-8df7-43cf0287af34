package kms

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	mw "github.com/tigergraph/cloud-universe/controller/middleware"
	awsSvc "github.com/tigergraph/cloud-universe/resource-manager/aws"
	"github.com/tigergraph/cloud-universe/resource-manager/interfaces"
	"github.com/tigergraph/cloud-universe/resource-manager/operator"
	"github.com/tigergraph/cloud-universe/utils/codec"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	"github.com/tigergraph/cloud-universe/utils/os/file"
	"golang.org/x/sync/errgroup"
)

type DEKResponse struct {
	Data []byte `json:"data"`
}

type KMSServer struct {
	router *gin.Engine
	server *http.Server
}

func InitKMSServer(ctx context.Context, port string, debug bool) {
	if !debug {
		gin.SetMode(gin.ReleaseMode)
	}

	kmsServer := &KMSServer{}
	kmsServer.router = gin.New()
	kmsServer.router.Use(requestid.GinReqIDSetter)
	kmsServer.router.GET("/kms/dek",
		kmsServer.RequireSystemAuthToken(),
		kmsServer.RetrievePlaintextDEK)
	kmsServer.server = &http.Server{
		Addr:        port,
		Handler:     kmsServer.router,
		ReadTimeout: 10 * time.Second,
	}
	kmsServer.Serve(ctx)
}

func (s *KMSServer) RequireSystemAuthToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.L().WithContext(c)

		tokStr := c.Request.Header.Get("Authorization")
		authToks := strings.Split(tokStr, " ")
		if len(authToks) != 2 {
			errMsg := "Invalid auth token"
			log.Error(errMsg)
			mw.Abort(c, mw.ErrUnauthorized, errMsg)
			return
		}

		token := authToks[1]
		if token != config.Config.SystemAuthToken {
			errMsg := "Unauthorized auth token"
			log.Error(errMsg)
			mw.Abort(c, mw.ErrUnauthorized, errMsg)
			return
		}
	}
}

func (s *KMSServer) Serve(ctx context.Context) error {
	g, gCtx := errgroup.WithContext(ctx)
	log := logger.L()
	wg := &sync.WaitGroup{}
	wg.Add(2)

	g.Go(func() error {
		defer wg.Done()
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.L().Fatalf("kms server listen: %s\n", err)
		}
		return nil
	})

	g.Go(func() error {
		defer wg.Done()
		<-gCtx.Done()
		log.Info("Received exit signal, sending an exit request to kms server...")
		return s.server.Close()
	})

	wg.Wait()

	return nil
}

func (s *KMSServer) RetrievePlaintextDEK(c *gin.Context) {
	if !config.Config.EnableEncryption {
		mw.Abort(c, mw.ErrBadRequest, "Failed to retrieve plaintext dek: encryption is not enabled")
		return
	}

	dekPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSEncryptedDEKSecretKey)
	externalIDPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSExternalIDSecretKey)
	keyIDPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSKeyIDSecretKey)
	regionPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSRegionSecretKey)
	roleARNPath := fmt.Sprintf("%v/%v", operator.EncryptionMountPath, operator.KMSRoleARNSecretKey)

	if !file.Exists(dekPath) {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: data encryption key file does not exist")
		return
	}
	encryptedDEK, err := os.ReadFile(dekPath)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	if !file.Exists(externalIDPath) {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: external id file does not exist")
		return
	}
	externalIDBytes, err := os.ReadFile(externalIDPath)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}
	externalID, err := codec.AESGCMDecrypt(config.SecretEncryptionKey, string(externalIDBytes))
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	if !file.Exists(keyIDPath) {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: key id file does not exist")
		return
	}
	keyIDBytes, err := os.ReadFile(keyIDPath)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}
	keyID, err := codec.AESGCMDecrypt(config.SecretEncryptionKey, string(keyIDBytes))
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	if !file.Exists(regionPath) {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: region file does not exist")
		return
	}
	regionBytes, err := os.ReadFile(regionPath)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}
	region, err := codec.AESGCMDecrypt(config.SecretEncryptionKey, string(regionBytes))
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	if !file.Exists(roleARNPath) {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: role arn file does not exist")
		return
	}
	roleARNBytes, err := os.ReadFile(roleARNPath)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}
	roleARN, err := codec.AESGCMDecrypt(config.SecretEncryptionKey, string(roleARNBytes))
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	var keyRoleKeySvc interfaces.AWSKey
	assumeKeyRole := func() error {
		keyRoleKeySvc, err = awsSvc.NewAWSKeyService("retrieveKey", region, roleARN, externalID)
		if err != nil {
			return err
		}

		return nil
	}

	expBackOff := backoff.NewExponentialBackOff()
	err = backoff.Retry(assumeKeyRole, expBackOff)
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Failed to assume role with retry: %v", err)
		return
	}

	plaintextDEK, err := keyRoleKeySvc.DecryptDataWithKMS(context.Background(), encryptedDEK, keyID)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to retrieve plaintext dek: %v", err)
		return
	}

	mw.Reply(c, "Successfully retrieve plaintext dek.",
		&DEKResponse{
			Data: plaintextDEK,
		})
}
