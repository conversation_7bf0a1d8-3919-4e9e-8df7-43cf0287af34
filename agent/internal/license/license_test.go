package license_test

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/agent/internal/license"
	"golang.org/x/mod/semver"
)

func MockLicenseExpirationEndpoint() *httptest.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/v2", func(w http.ResponseWriter, r *http.Request) {
		var resp string
		bodyBytes, err := io.ReadAll(r.Body)
		if err != nil {
			resp = ""
			return
		}
		bodyString := string(bodyBytes)
		if bodyString == `{"query":"query { License { IssueTime EndTime } }","variables":{}}` {
			resp = `{"data":{"License":{"IssueTime":**********,"EndTime":**********}}}`
		} else {
			resp = ""
		}
		io.WriteString(w, resp)
	})
	return httptest.NewServer(mux)
}

func MockGUIVersion(version string) *httptest.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/version", func(w http.ResponseWriter, r *http.Request) {
		resp := fmt.Sprintf(`{"error":false,"message":"","results":{"build_time":"Tue Mar 11 20:27:03 UTC 2025","git_commit":"e7c7718b009af5a5e8a770f6a94e397745a8da7d","build_num":"3900","tigergraph_version":"%v","is_graphstudio_enabled":true,"is_adminportal_enabled":true,"is_insights_enabled":true,"is_graphql_enabled":true,"is_gsqlshell_enabled":true,"is_community_edition":false}}`, version)
		io.WriteString(w, resp)
	})
	mux.HandleFunc("/api/ping", func(w http.ResponseWriter, r *http.Request) {
		resp := `{"error":false,"message":"pong","results":null}`
		io.WriteString(w, resp)
	})
	return httptest.NewServer(mux)
}

func TestGetLicenseExpirationTime(t *testing.T) {
	server := MockLicenseExpirationEndpoint()
	defer server.Close()
	licenseExpirationTimeStamp, err := license.GetLicenseExpirationTime(server.URL)
	assert.NoError(t, err)
	assert.Equal(t, int64(**********), licenseExpirationTimeStamp)
}

func TestSemVer(t *testing.T) {
	v := semver.IsValid("v3.1.1")
	assert.True(t, v)
	c := semver.Compare("v3.1.1", "v4.2.0")
	assert.Equal(t, -1, c)
}

func TestLicenseExpirationVersionCheck(t *testing.T) {
	t.Run("TestLicenseExpirationVersionCheck_4.1.1", func(t *testing.T) {
		server := MockGUIVersion("4.1.1")
		defer server.Close()
		res := license.LicenseExpiraionCronPreCheck(server.URL)
		assert.False(t, res)
	})

	t.Run("TestLicenseExpirationVersionCheck_4.2.0", func(t *testing.T) {
		server := MockGUIVersion("4.2.0")
		defer server.Close()
		res := license.LicenseExpiraionCronPreCheck(server.URL)
		assert.True(t, res)
	})

	t.Run("TestLicenseExpirationVersionCheck_4.2.1", func(t *testing.T) {
		server := MockGUIVersion("4.2.1")
		defer server.Close()
		res := license.LicenseExpiraionCronPreCheck(server.URL)
		assert.True(t, res)
	})

	t.Run("TestLicenseExpirationVersionCheck_5.2.1", func(t *testing.T) {
		server := MockGUIVersion("5.2.1")
		defer server.Close()
		res := license.LicenseExpiraionCronPreCheck(server.URL)
		assert.True(t, res)
	})

}

func TestReportLicenseExpirationTime(t *testing.T) {
	server := MockLicenseExpirationEndpoint()
	defer server.Close()
	err := license.ReportLicenseExpirationTime(server.URL)
	assert.NoError(t, err)
}
