package license

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/agent/internal/observability/metrics"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
	"golang.org/x/mod/semver"
)

type LicenseExpirationResponse struct {
	Data struct {
		License struct {
			IssueTime int64 `json:"IssueTime"`
			EndTime   int64 `json:"EndTime"`
		} `json:"License"`
	} `json:"data"`
}

func GetLicenseExpirationTime(serverURL string) (int64, error) {
	log := logger.L()

	body := `{"query":"query { License { IssueTime EndTime } }","variables":{}}`
	input := rest.RequestInput{
		URL:       fmt.Sprintf("%s/api/v2", serverURL),
		AuthToken: config.Config.SystemAuthToken,
		NoRetry:   true,
		Body:      body,
	}
	resp, err := rest.Post(&input)
	if err != nil {
		return 0, err
	}
	// Example response:
	// `{"data":{"License":{"IssueTime":**********,"EndTime":**********}}}`
	licenseResp := &LicenseExpirationResponse{}
	err = json.Unmarshal(resp, licenseResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return 0, err
	}

	return licenseResp.Data.License.EndTime, nil
}

func ReportLicenseExpirationTime(serverURL string) error {
	log := logger.L()
	licenseExpirationTimeStamp, err := GetLicenseExpirationTime(serverURL)
	if err != nil {
		log.Errorf("Failed to get license expiration time: %v", err)
		return err
	}
	t := time.Unix(licenseExpirationTimeStamp, 0)
	days := int(time.Until(t).Hours() / 24)
	metrics.ReportLicenseExpirationMetrics(days)
	log.Infof("License expiration time: %v", t)
	return nil
}

func LicenseExpiraionCronPreCheck(serverURL string) bool {
	log := logger.L()
	version, err := common.GetTigerGraphVersion(serverURL)
	if err != nil {
		log.Warnf("Failed to get TigerGraph version: %v", err)
		return false
	}
	// License expiration check is added in 4.2.0
	log.Infof("TigerGraph version: %v", version)
	compare := semver.Compare(fmt.Sprintf("v%v", version), "v4.2.0")
	if compare < 0 {
		log.Infof("TigerGraph version %v is less than 4.2.0, license expiration metric is disabled", version)
		return false
	}
	return true
}

func UpdateLicenseExpirationCron() {
	common.WaitUntilServicesUp()
	if !LicenseExpiraionCronPreCheck(common.DefaultGUIServerURL) {
		return
	}
	for {
		log := logger.L()
		err := ReportLicenseExpirationTime(common.DefaultGUIServerURL)
		if err != nil {
			log.Warnf("Failed to get license expiration time: %w", err)
		}
		time.Sleep(time.Duration(config.Config.LicenseExpirationUpdateIntervalSeconds) * time.Second)
	}
}
