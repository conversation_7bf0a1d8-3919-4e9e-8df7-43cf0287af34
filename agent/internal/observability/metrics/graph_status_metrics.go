package metrics

import (
	"sync"

	"github.com/prometheus/client_golang/prometheus"
)

const (
	metricTopologyBytes      = "topology_bytes"
	metricTopologyLimitBytes = "topology_limit_bytes"
)

var (
	metricTopologyBytesVec = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "tigergraph",
		Name:      metricTopologyBytes,
		Help:      "The size of topology.",
	}, nil)

	metricTopologyLimitBytesVec = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "tigergraph",
		Name:      metricTopologyLimitBytes,
		Help:      "The size of topology limit.",
	}, nil)

	graphStatusMetricOnce = &sync.Once{}
)

func registerGraphStatusMetrics() {
	prometheus.MustRegister(metricTopologyBytesVec, metricTopologyLimitBytesVec)
}

type GraphStatusMetric struct {
	GraphTopologyBytes      int64
	GraphTopologyLimitBytes int64
}

func ReportGraphStatusMetrics(metrics GraphStatusMetric) {
	graphStatusMetricOnce.Do(registerGraphStatusMetrics)
	metricTopologyBytesVec.WithLabelValues().Set(float64(metrics.GraphTopologyBytes))
	metricTopologyLimitBytesVec.WithLabelValues().Set(float64(metrics.GraphTopologyLimitBytes))
}
