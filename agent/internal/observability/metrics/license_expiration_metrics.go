package metrics

import (
	"sync"

	"github.com/prometheus/client_golang/prometheus"
)

const (
	licenseExpirationDays = "license_expiration_days"
)

var (
	metricLicenseExpirationVec = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Namespace: "tigergraph",
		Name:      licenseExpirationDays,
		Help:      "The number of days until license expiration.",
	}, nil)

	licenseExpirationMetricOnce = &sync.Once{}
)

func registerLicenseExpirationMetrics() {
	prometheus.MustRegister(metricLicenseExpirationVec)
}

func ReportLicenseExpirationMetrics(days int) {
	licenseExpirationMetricOnce.Do(registerLicenseExpirationMetrics)
	metricLicenseExpirationVec.WithLabelValues().Set(float64(days))
}
