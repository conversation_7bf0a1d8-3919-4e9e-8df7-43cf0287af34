package agent

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/agent/internal/activeness"
	"github.com/tigergraph/cloud-universe/agent/internal/common"
	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/agent/internal/sqs"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

var (
	StartTime         time.Time
	TopologyUpdatedAt *time.Time
)

func Report() {
	StartTime = time.Now()
	for {
		log := logger.L()
		SendHeartbeat()
		// https://golang.org/pkg/runtime/#MemStats
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		log.Debugf("\tSys = %v MiB", bToMb(m.Sys))
		time.Sleep(time.Duration(config.Config.HeartbeatIntervalSeconds) * time.Second)
	}
}

func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

type Heartbeat struct {
	Version                  string
	ServicesStatus           string
	ServicesReadyTime        *time.Time `json:",omitempty"`
	LastActiveTime           *time.Time `json:",omitempty"`
	LastActiveNginxTime      *time.Time `json:",omitempty"`
	LastActiveCatalogTime    *time.Time `json:",omitempty"`
	LastActiveLoadingJobTime *time.Time `json:",omitempty"`
	LastActiveQueriesTime    *time.Time `json:",omitempty"`
	GraphTopologySizeBytes   *int64
	VertexCount              *int64
	EdgeCount                *int64
	CurrentTime              *time.Time
	PodStartTime             *time.Time
	WorkspaceID              uuid.UUID
	NodeID                   string
}

func SendHeartbeat() {
	log := logger.L()
	currTime := time.Now()

	lastActiveNginx, err := activeness.GetNginxActivity()
	if err != nil {
		log.Warnf("Failed to get last active time from nginx: %w", err)
	}

	lastActiveCatalog := activeness.GetCatalogActivity()

	lastActiveLoadingJob, err := activeness.GetLoadingJobActivity()
	if err != nil {
		log.Warnf("Failed to get loading job last active time: %w", err)
	}

	lastActiveQueries := activeness.GetQueryActivity()

	if lastActiveNginx != nil {
		log.Info(fmt.Sprintf("lastActiveNginx: %v", *lastActiveNginx))
	} else {
		log.Info("lastActiveNginx is nil")
	}

	if lastActiveCatalog != nil {
		log.Info(fmt.Sprintf("lastActiveCatalog: %v", *lastActiveCatalog))
	} else {
		log.Info("lastActiveCatalog is nil")
	}

	if lastActiveLoadingJob != nil {
		log.Info(fmt.Sprintf("lastActiveLoadingJob: %v", *lastActiveLoadingJob))
	} else {
		log.Info("lastActiveLoadingJob is nil")
	}

	if lastActiveQueries != nil {
		log.Info(fmt.Sprintf("lastActiveQueries: %v", *lastActiveQueries))
	} else {
		log.Info("lastActiveQueries is nil")
	}

	// TODO add active time from engine queries
	lastActiveTime := activeness.GetLatestTime([]*time.Time{lastActiveNginx, lastActiveCatalog, lastActiveLoadingJob, lastActiveQueries})
	// If lastActiveTime is earlier than the start time of the pod, which may happen after the workspace is resumed,
	// Set it to the start time of the pod
	if lastActiveTime == nil || StartTime.After(*lastActiveTime) {
		lastActiveTime = &StartTime
	}

	nodeIP, err := common.GetIP()
	if err != nil {
		log.Warnf("failed to retrieve current host IP: %s", err.Error())
	}

	nodeHostname, err := os.Hostname()
	if err != nil {
		log.Warnf("failed to retrieve current host name: %s", err.Error())
	}
	nodeID, err := common.GetCurrentHostID(common.DefaultGUIServerURL, nodeIP, nodeHostname)
	if err != nil {
		log.Warnf("failed to retrieve current host position: %s", err.Error())
	}

	heartbeatPayload := Heartbeat{
		Version:                  "v1",
		LastActiveTime:           lastActiveTime,
		LastActiveNginxTime:      lastActiveNginx,
		LastActiveCatalogTime:    lastActiveCatalog,
		LastActiveLoadingJobTime: lastActiveLoadingJob,
		LastActiveQueriesTime:    lastActiveQueries,
		CurrentTime:              &currTime,
		PodStartTime:             &StartTime,
		WorkspaceID:              uuid.MustParse(config.Config.WorkspaceId),
		NodeID:                   nodeID,
	}

	if activeness.LastTopologyValue != nil {
		heartbeatPayload.GraphTopologySizeBytes = &activeness.LastTopologyValue.TotalGraphTopologyBytes
		heartbeatPayload.VertexCount = &activeness.LastTopologyValue.TotalVertexCount
		heartbeatPayload.EdgeCount = &activeness.LastTopologyValue.TotalEdgeCount
	}

	sqsPayload, _ := json.Marshal(&heartbeatPayload)
	log.Info(string(sqsPayload))
	err = sqs.Send(string(sqsPayload))
	if err != nil {
		log.Error(err.Error())
	}
}
