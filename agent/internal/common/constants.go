package common

import (
	"fmt"
	"path"
)

// Version is daemon's version
type Version string

// Daemon versions
const (
	Version3x Version = "version3.x"
	Version30 Version = "version3.0"
)

// Version agnostic constants
const (
	// GUI cookie key
	GUICookieKey = "TigerGraphApp"
	// TGUsername OS username for running tigergraph db system
	TGUsername = "tigergraph"
	// NetD<PERSON><PERSON><PERSON> is the owner of NetData.
	NetDataUser = "netdata"
	// TigerGraphUser is the owner of TigerGraph.
	TigerGraphUser = "tigergraph"
	// TigerGraphRoot is the root folder of TigerGraph user.
	TigerGraphRoot = "/home/<USER>"
	// TigerGraphReadyFile is a placeholder file to check ready status.
	TigerGraphReadyFile = "/home/<USER>/ready"
	// TigerGraphRefreshFile is a placeholder file to determine whether to run auto-refresh or not.
	TigerGraphRefreshFile = "/home/<USER>/refresh"
)

// TigerGraph installation and configration files.
const (
	UserConfigPath       = "/home/<USER>/user_conf.yml"
	PasswordTempfilePath = "/tmp/tigergraph_password" // #nosec: G101
	IAMAuth0ConfigPath   = "/home/<USER>/iam_auth0_conf.yml"
	ClusterConfigPath    = "/home/<USER>/offline/install_conf.json"
	InstallSSHKeyPath    = "/home/<USER>/.ssh/install.key"
	SetupScriptPath      = "/home/<USER>/setup.sh"
)

// Version agnostic constants
var (
	TGSSLCertFile = path.Join(TigerGraphRoot, "tigergraph", "ssl.cert")
	TGSSLKeyFile  = path.Join(TigerGraphRoot, "tigergraph", "ssl.key")
)

// Constants dependent on version.
// Set version3.x constants by default.
var (
	GadminBin           = path.Join(TigerGraphRoot, "tigergraph", "app", "cmd", "gadmin")
	GSQLBin             = path.Join(TigerGraphRoot, "tigergraph", "app", "cmd", "gsql")
	GadminSetLicenseCMD = "license set"
	NetdataSSLDir       = "/opt/netdata/etc/netdata/ssl"
	NginxDir            = "tigergraph/log/nginx/logs/"
	KafkaDir            = "tigergraph/data/kafka/"
	NginxFilePattern    = "NGINX#.*\\.out"
)

// UseConstants overwrites version-specific "constants" for use in common functions.
// It is called only on daemon initialization.
func UseConstants(v Version) error {
	switch v {
	case Version30:
		NginxFilePattern = "nginx.*\\.access\\.log"
	case Version3x:
		NginxFilePattern = "NGINX#.*\\.out"
	default:
		return fmt.Errorf("unsupported version %v", v)
	}
	return nil
}
