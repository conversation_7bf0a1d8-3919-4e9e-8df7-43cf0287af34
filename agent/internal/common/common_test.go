package common

import (
	"io"
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHostnameOrIpMatches(t *testing.T) {
	r1 := HostnameOrIpMatches("tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-0", "*********", "tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-0.tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-internal-service")
	assert.Equal(t, true, r1)
	r2 := HostnameOrIpMatches("tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-0", "*********", "tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-1.tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-internal-service")
	assert.Equal(t, false, r2)
	r3 := HostnameOrIpMatches("tg-cae67f59-2ac1-4291-8653-2a5c8ffc03d5-0", "*********", "*********")
	assert.Equal(t, true, r3)
}

func MockSystemHostListAndNodeConfig() *httptest.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/auth/login", func(w http.ResponseWriter, r *http.Request) {
		cookie1 := &http.Cookie{Name: "TigerGraphApp", Value: "sample", HttpOnly: false}
		http.SetCookie(w, cookie1)
		io.WriteString(w, "")
	})
	mux.HandleFunc("/api/config", func(w http.ResponseWriter, r *http.Request) {
		key := r.URL.Query().Get("key")
		if key == "GSQL.BasicConfig.Nodes" {
			resp := `{"error": false, "message": "", "results": {"GSQL.BasicConfig.Nodes":[{"HostID":"m1","Partition":0,"Replica":1},{"HostID":"m2","Partition":0,"Replica":1}]},"done": true}`
			io.WriteString(w, resp)
			return
		}
		if key == "System.HostList" {
			resp := `{"error": false, "message": "", "results": {"System.HostList":[{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-0.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m1","Region":""},{"Hostname":"tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-1.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service","ID":"m2","Region":""}]},"done": true}`
			io.WriteString(w, resp)
			return
		}
	})
	mux.HandleFunc("/api/version", func(w http.ResponseWriter, r *http.Request) {
		resp := `{"error":false,"message":"","results":{"build_time":"Tue Mar 11 20:27:03 UTC 2025","git_commit":"e7c7718b009af5a5e8a770f6a94e397745a8da7d","build_num":"3900","tigergraph_version":"4.2.0","is_graphstudio_enabled":true,"is_adminportal_enabled":true,"is_insights_enabled":true,"is_graphql_enabled":true,"is_gsqlshell_enabled":true,"is_community_edition":false}}`
		io.WriteString(w, resp)
	})
	return httptest.NewServer(mux)
}

func TestGetCurrentHostID(t *testing.T) {
	server := MockSystemHostListAndNodeConfig()
	defer server.Close()
	hostID, err := GetCurrentHostID(server.URL, "*******", "tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-0.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service")
	assert.NoError(t, err)
	assert.Equal(t, "m1", hostID)

	hostID1, err := GetCurrentHostID(server.URL, "*******", "tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-1.tg-006b4939-b8d1-4e82-8d6a-eb1fb2a27122-internal-service")
	assert.NoError(t, err)
	assert.Equal(t, "m2", hostID1)
}

func TestGetIP(t *testing.T) {
	ip, err := GetIP()
	assert.NoError(t, err)
	assert.NotEqual(t, "127.0.0.1", ip)
	var ipv4Pattern = `^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])$`
	re := regexp.MustCompile(ipv4Pattern)
	assert.True(t, re.MatchString(ip))
}

func TestUseVersion(t *testing.T) {
	err := UseConstants(Version30)
	assert.NoError(t, err)
	assert.Equal(t, NginxFilePattern, "nginx.*\\.access\\.log")

	err = UseConstants(Version3x)
	assert.NoError(t, err)
	assert.Equal(t, NginxFilePattern, "NGINX#.*\\.out")

	err = UseConstants("qwerty")
	assert.Error(t, err)
}

func TestTigerGraphVersion(t *testing.T) {
	server := MockSystemHostListAndNodeConfig()
	defer server.Close()

	version, err := GetTigerGraphVersion(server.URL)
	assert.NoError(t, err)
	assert.Equal(t, "4.2.0", version)
}
