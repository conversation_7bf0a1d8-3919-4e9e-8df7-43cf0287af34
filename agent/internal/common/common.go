package common

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/exec"
	"sort"
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/agent/internal/config"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"

	backoff "github.com/cenkalti/backoff/v4"
)

const (
	MinDynamicPort       = 49152
	MaxDynamicPort       = 65535
	DefaultGUIServerURL  = "http://localhost:14240"
	DefaultGSQLServerURL = "http://localhost:8123"
)

// GUIResponse is the response body of GUI server.
type GUIResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results interface{} `json:"results"`
	Done    *bool       `json:"done,omitempty"`
}

// MemoryConfigData is the request body for updating memory configs via gadmin.
type MemoryConfigData struct {
	SysMinFreePct             int  `binding:"required"`
	SysAlertFreePct           int  `binding:"required"`
	MaxLicenseViolation       *int `binding:"required"`
	KafkaConnectMaxMemorySize int  `binding:"required"`
}

// GStatusGraphResponse is the response of graph topology data from gstatusgraph.
type GStatusGraphResponse struct {
	Nodes         []nodeTopologyInfo `json:"nodes"`
	TopologyBytes int64              `json:"topology_bytes"`
}

// GSQLTypenamesResponse is the response of typenames from GSQL.
type GSQLTypenamesResponse struct {
	Graphs []string `json:"1"`
}

type nodeTopologyInfo struct {
	Name                 string `json:"name"`
	PartitionSize        int64  `json:"partition_size"`
	IdsSize              int64  `json:"ids_size"`
	VertexCount          int64  `json:"vertex_count"`
	EdgeCount            int64  `json:"edge_count"`
	NumOfDeletedVertices int64  `json:"num_of_deleted_vertices"`
	NumOfSkippedVertices int64  `json:"num_of_skipped_vertices"`
}

// NodeConfig is used to contain the node config output of a specific service from 'gadmin config get <service>.BasicConfig.Nodes'.
type NodeConfig struct {
	HostID    string
	Partition int
	Replica   int
}

type NodeConfigWrapper struct {
	GSQLBasicConfigNodes []NodeConfig `json:"GSQL.BasicConfig.Nodes"`
}

// HostInfo is used to contain the host config output of the system from 'gadmin config get System.HostList'.
type HostInfo struct {
	Hostname string `yaml:"Hostname" json:"Hostname"`
	ID       string `yaml:"ID" json:"ID"`
}

type InitConfig struct {
	SystemHostList []HostInfo `json:"System.HostList" yaml:"System.HostList"`
}

type TigerGraphVersionResp struct {
	BuildTime            string `json:"build_time"`
	GitCommit            string `json:"git_commit"`
	BuildNum             string `json:"build_num"`
	TigerGraphVersion    string `json:"tigergraph_version"`
	IsGraphStudioEnabled bool   `json:"is_graphstudio_enabled"`
	IsAdminPortalEnabled bool   `json:"is_adminportal_enabled"`
	IsInsightsEnabled    bool   `json:"is_insights_enabled"`
	IsGraphQLEnabled     bool   `json:"is_graphql_enabled"`
	IsGSQLShellEnabled   bool   `json:"is_gsqlshell_enabled"`
	IsCommunityEdition   bool   `json:"is_community_edition"`
}

func GetSystemHostList(serverURL string) ([]HostInfo, error) {
	log := logger.L()
	input := rest.RequestInput{
		URL:       fmt.Sprintf("%s/api/config?key=System.HostList", serverURL),
		AuthToken: config.Config.SystemAuthToken,
		NoRetry:   true,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		log.Errorf("Failed to get host list: %v", err)
		return nil, err
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	jsonResult, err := json.Marshal(guiResp.Results)
	if err != nil {
		log.Errorf("Failed to marshal result: %v", err)
		return nil, err
	}

	hostList := &InitConfig{}
	err = json.Unmarshal(jsonResult, hostList)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	return hostList.SystemHostList, nil
}

// GetNodeConfigForService retrieves the node config for the specified service.
func GetNodeConfigForService(serverURL string, service string) ([]NodeConfig, error) {
	log := logger.L()
	input := rest.RequestInput{
		URL:       fmt.Sprintf("%s/api/config?key=%v.BasicConfig.Nodes", serverURL, service),
		AuthToken: config.Config.SystemAuthToken,
		NoRetry:   true,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		log.Errorf("Failed to get %v node config: %v", service, err)
		return nil, err
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		log.Errorf("Failed to unmarshal response: %v", err)
		return nil, err
	}

	nodeConfigJson, err := json.Marshal(guiResp.Results)
	if err != nil {
		log.Errorf("Failed to marshal result: %v", err)
		return nil, err
	}

	var nodeConfigWrapper NodeConfigWrapper
	err = json.Unmarshal([]byte(nodeConfigJson), &nodeConfigWrapper)
	if err != nil {
		return nil, fmt.Errorf("cannot parse json config for service %v: %v", service, err)
	}

	return nodeConfigWrapper.GSQLBasicConfigNodes, err
}

func CommandExists(cmd string) bool {
	_, err := exec.LookPath(cmd)
	return err == nil
}

// GetIP retrieves the system's ip.
func GetIP() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", fmt.Errorf("unable to obtain ip: %v", err)
	}

	for _, a := range addrs {
		if ipnet, ok := a.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String(), nil
			}
		}
	}

	return "", fmt.Errorf("unable to obtain ip")
}

func HostnameOrIpMatches(hostname, ip, hostnameInConfig string) bool {
	if ip == hostnameInConfig {
		return true
	}
	if strings.Contains(hostnameInConfig, hostname) {
		return true
	}
	return false
}

// getGUICookie gets GUI cookie through login API.
func getGUICookie(serverURL string) (*http.Cookie, error) {
	log := logger.L()
	tgPassword, err := ReadTigerGraphPassword()
	if err != nil {
		log.Errorf("Failed to get tigergraph password: %v", err)
		return nil, err
	}

	input := rest.RequestInput{
		URL: fmt.Sprintf("%s/api/auth/login", serverURL),
		Body: map[string]interface{}{
			"username": "tigergraph",
			"password": tgPassword,
		},
		NoRetry: true, // too many failed attempts will lock the account
	}

	resp, err := rest.PostWithHTTPResponse(&input)
	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("status code: %s, body: %s", resp.Status(), resp.Body())
	}

	for _, c := range resp.Cookies() {
		if c.Name == GUICookieKey {
			return c, nil
		}
	}
	log.Error("Failed to get GUI cookie from response header.")

	return nil, fmt.Errorf("failed to get cookie")
}

func ReadTigerGraphPassword() (string, error) {
	return config.Config.TGPassword, nil
}

func IsGUIUp(serverURL string) error {
	input := rest.RequestInput{
		URL:     fmt.Sprintf("%s/api/ping", serverURL),
		NoRetry: true,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		return fmt.Errorf("GUI is not ready")
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		return fmt.Errorf("GUI is not ready")
	}

	if guiResp.Message != "pong" {
		return fmt.Errorf("GUI is not ready")
	}

	return nil
}

func WaitUntilServicesUp() {
	log := logger.L()
	b := backoff.NewExponentialBackOff()
	b.MaxElapsedTime = 15 * time.Minute

	err := backoff.Retry(func() error {
		log.Info("Waiting for GUI to be up...")
		return IsGUIUp(DefaultGUIServerURL)
	}, b)
	if err != nil {
		log.Errorf("error after retrying: %v", err)
	}
}

// GetCurrentHostPosInGSQLServerList returns the position of current host in the list of GSQL servers.
// Will return error if current host does not have GSQL server.
// nodePos will be from [0, totalServers).
func GetCurrentHostPosInGSQLServerList(serverURL string) (nodePos, totalServers int, err error) {
	nodeIP, err := GetIP()
	if err != nil {
		return 0, 0, err
	}

	nodeHostname, err := os.Hostname()
	if err != nil {
		return 0, 0, err
	}
	nodeID, err := GetCurrentHostID(serverURL, nodeIP, nodeHostname)
	if err != nil {
		return 0, 0, err
	}

	/*
		serviceNode example:
		{
			"HostID": "m001",
			"Partition": 0,
			"Replica": 1
		}
	*/
	serviceNodes, err := GetNodeConfigForService(serverURL, "GSQL")
	if err != nil {
		return 0, 0, err
	}
	sort.Slice(serviceNodes, func(i, j int) bool { return serviceNodes[i].HostID < serviceNodes[j].HostID })

	for i, node := range serviceNodes {
		if node.HostID == nodeID {
			return i, len(serviceNodes), nil
		}
	}

	return 0, 0, fmt.Errorf("current node does not have running GSQL server")
}

func GetCurrentHostID(serverURL, nodeIP, nodeHostname string) (string, error) {
	// Obtain host list.
	/*
		host example:
		{
			"Hostname": "**********",
			"ID": "m001",
		}
	*/
	hostList, err := GetSystemHostList(serverURL)
	if err != nil {
		return "", err
	}

	// For single node, Hostname = "127.0.0.1", we must return earlier otherwise it will not find a matching host.
	if len(hostList) == 1 {
		return "m1", err
	}

	nodeID := ""
	for _, host := range hostList {
		if HostnameOrIpMatches(nodeHostname, nodeIP, host.Hostname) {
			nodeID = host.ID
			break
		}
	}
	if nodeID == "" {
		return "", fmt.Errorf("unable to find the ID of current host")
	}
	return nodeID, nil
}

func GetTigerGraphVersion(serverURL string) (string, error) {
	input := rest.RequestInput{
		URL:     fmt.Sprintf("%s/api/version", serverURL),
		NoRetry: false,
	}

	resp, err := rest.Get(&input)
	if err != nil {
		return "", err
	}

	guiResp := &GUIResponse{}
	err = json.Unmarshal(resp, guiResp)
	if err != nil {
		return "", err
	}

	tigergraphVersionJson, err := json.Marshal(guiResp.Results)
	if err != nil {
		return "", err
	}

	var tigerGraphVersionResp TigerGraphVersionResp
	err = json.Unmarshal([]byte(tigergraphVersionJson), &tigerGraphVersionResp)
	if err != nil {
		return "", fmt.Errorf("cannot parse tigergraph version response %v: %v", tigergraphVersionJson, err)
	}
	return tigerGraphVersionResp.TigerGraphVersion, nil
}
