package internal

import (
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

var config Config

type Config struct {
	XTestEnv       string `mapstructure:"X_TEST_ENV"`
	AutoStartToken string `validate:"required" mapstructure:"AUTO_START_TOKEN"`
	ControllerUrl  string `validate:"required" mapstructure:"CONTROLLER_URL"`
}

func LoadConfig() {
	viper.SetConfigName("autostarthandler")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AutomaticEnv()
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			panic(err)
		}
		fmt.Println("Config file not found; reading from environment")
	} else {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	}
	err := viper.Unmarshal(&config)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", config)
	// Validate config.
	if err = validator.New().Struct(config); err != nil {
		panic(fmt.Errorf("invalid config: %v", err))
	}
}
