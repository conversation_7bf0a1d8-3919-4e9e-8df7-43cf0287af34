package internal

import (
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestParseWorkspaceIdFromHost(t *testing.T) {
	id, err := parseWorkspaceIdFromHost("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud.io")
	assert.NoError(t, err)
	assert.Equal(t, id, uuid.MustParse("46feb1a9-117a-4a3e-8700-c9bf4f8664be"))
	id, err = parseWorkspaceIdFromHost("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud-dev.com")
	assert.NoError(t, err)
	assert.Equal(t, id, uuid.MustParse("46feb1a9-117a-4a3e-8700-c9bf4f8664be"))
	id, err = parseWorkspaceIdFromHost("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.ap-northeast-1.i.tgcloud.io")
	assert.NoError(t, err)
	assert.Equal(t, id, uuid.MustParse("46feb1a9-117a-4a3e-8700-c9bf4f8664be"))
}

func TestValidateHostname(t *testing.T) {
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-1.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-west-1.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-west-2.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.eu-west-2.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.ap-northeast-1.i.tgcloud.io"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud-dev.com"))
	assert.NoError(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.tg-123.i.tgcloud-dev.com"))
	assert.Error(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664b.us-east-2.i.google.com"))
	assert.Error(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664bz.us-east-2.tgcloud.io"))
	assert.Error(t, validateHostname("tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be-i.tgcloud.io"))
	assert.Error(t, validateHostname("46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud.io"))
	assert.Error(t, validateHostname("tg-zzzeb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud.io"))
	assert.Error(t, validateHostname("tg-46feb1a9-117a-4a3e-8700.us-east-2.i.tgcloud.io"))
	assert.Error(t, validateHostname("aaatg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.tg-123.i.tgcloud-dev.com"))
}

func MockController() {
	httpServer := &http.Server{
		Addr:              "127.0.0.1:51845",
		ReadTimeout:       1 * time.Second,
		WriteTimeout:      1 * time.Second,
		IdleTimeout:       30 * time.Second,
		ReadHeaderTimeout: 2 * time.Second,
	}
	http.HandleFunc("/autostart/workspace/46feb1a9-117a-4a3e-8700-c9bf4f8664be", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, "pong")
	})

	http.HandleFunc("/autostart/workspace/39f62734-9501-44fe-b1a8-c014fcd0d4b7", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, "is already in the process of being resumed")
	})

	http.HandleFunc("/autostart/workspace/4719b4a4-51c7-414a-877e-d1793d578d4b", func(w http.ResponseWriter, r *http.Request) {
		io.WriteString(w, "pong")
	})

	http.HandleFunc("/autostart/workspace/c8640841-67b5-4333-af7a-13223a2c6864", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(500)
		io.WriteString(w, "Auto start is not enabled for this workspace")
	})
	httpServer.ListenAndServe()
}

func TestHandler(t *testing.T) {
	go MockController()

	time.Sleep(5 * time.Second)

	os.Setenv("AUTO_START_TOKEN", "qwerty")
	os.Setenv("CONTROLLER_URL", "http://127.0.0.1:51845")

	LoadConfig()

	// Normal case
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	tc, _ := gin.CreateTestContext(w)
	u, _ := url.Parse("https://tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud-dev.com/api/informant")
	tc.Request = &http.Request{
		Method: "GET",
		Host:   "tg-46feb1a9-117a-4a3e-8700-c9bf4f8664be.us-east-2.i.tgcloud-dev.com",
		URL:    u,
	}

	AutoStart(tc)

	assert.Equal(t, 202, w.Code)
	b, err := io.ReadAll(w.Body)
	assert.NoError(t, err)
	expectString := "{\n    \"message\": \"Workspace 46feb1a9-117a-4a3e-8700-c9bf4f8664be is starting\"\n}"
	assert.Equal(t, expectString, string(b))

	// Error case: workspace is already in the process of being resumed
	gin.SetMode(gin.TestMode)
	w1 := httptest.NewRecorder()
	tc1, _ := gin.CreateTestContext(w1)
	u1, _ := url.Parse("https://tg-39f62734-9501-44fe-b1a8-c014fcd0d4b7.us-east-2.i.tgcloud-dev.com/api/informant")
	tc1.Request = &http.Request{
		Method: "GET",
		Host:   "tg-39f62734-9501-44fe-b1a8-c014fcd0d4b7.us-east-2.i.tgcloud-dev.com",
		URL:    u1,
	}

	AutoStart(tc1)

	assert.Equal(t, 202, w1.Code)
	b1, err1 := io.ReadAll(w1.Body)
	assert.NoError(t, err1)
	expectString1 := "{\n    \"message\": \"Workspace 39f62734-9501-44fe-b1a8-c014fcd0d4b7 is starting\"\n}"
	assert.Equal(t, expectString1, string(b1))

	// Error case: auto start is not enabled for this workspace
	gin.SetMode(gin.TestMode)
	w2 := httptest.NewRecorder()
	tc2, _ := gin.CreateTestContext(w2)
	u2, _ := url.Parse("https://tg-c8640841-67b5-4333-af7a-13223a2c6864.us-east-2.i.tgcloud-dev.com/api/informant")
	tc2.Request = &http.Request{
		Method: "GET",
		Host:   "tg-c8640841-67b5-4333-af7a-13223a2c6864.us-east-2.i.tgcloud-dev.com",
		URL:    u2,
	}

	AutoStart(tc2)

	assert.Equal(t, 500, w2.Code)
	b2, err2 := io.ReadAll(w2.Body)
	assert.NoError(t, err2)
	expectString2 := "{\n    \"message\": \"Auto start is not enabled for this workspace\"\n}"
	assert.Equal(t, expectString2, string(b2))

	w3 := httptest.NewRecorder()
	tc3, _ := gin.CreateTestContext(w3)
	u3, _ := url.Parse("https://tg-4719b4a4-51c7-414a-877e-d1793d578d4b.us-east-2.i.tgcloud-dev.com/")
	tc3.Request = &http.Request{
		Method: "GET",
		Host:   "tg-4719b4a4-51c7-414a-877e-d1793d578d4b.us-east-2.i.tgcloud-dev.com",
		URL:    u3,
	}

	AutoStart(tc3)

	assert.Equal(t, 200, w3.Code)
	b3, err3 := io.ReadAll(w3.Body)
	assert.NoError(t, err3)
	expectString3 := "<html>"
	assert.Contains(t, string(b3), expectString3)
}
