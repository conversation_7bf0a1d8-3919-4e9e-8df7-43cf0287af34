package internal

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLoadConfigFail(t *testing.T) {
	if os.Getenv("CI") != "" {
		t.Skip("Skipping testing in CI environment")
	}
	// Expect panic since `AUTO_START_TOKEN` is not set
	os.Unsetenv("AUTO_START_TOKEN")
	os.Unsetenv("CONTROLLER_URL")
	assert.Panics(t, LoadConfig, "`LoadConfig` did not panic")
}

func TestLoadConfig(t *testing.T) {
	os.Unsetenv("X_TEST_ENV")
	os.Unsetenv("CONTROLLER_URL")
	os.Unsetenv("AUTO_START_TOKEN")

	os.Setenv("AUTO_START_TOKEN", "qwerty")
	os.Setenv("CONTROLLER_URL", "https://tgcloud.io")
	LoadConfig()
	assert.Equal(t, "qwerty", config.AutoStartToken)
	assert.Equal(t, "https://tgcloud.io", config.ControllerUrl)
	assert.Equal(t, "", config.XTestEnv)

	os.Setenv("X_TEST_ENV", "cloud#dev-TCE-1234")
	os.Setenv("CONTROLLER_URL", "https://google.com")
	os.Setenv("AUTO_START_TOKEN", "qwerty")
	LoadConfig()
	assert.Equal(t, "qwerty", config.AutoStartToken)
	assert.Equal(t, "https://google.com", config.ControllerUrl)
	assert.Equal(t, "cloud#dev-TCE-1234", config.XTestEnv)
}
