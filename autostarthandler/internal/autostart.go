package internal

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

func AutoStart(c *gin.Context) {
	log := logger.L().WithContext(c)
	host := c.Request.Host
	if host == "" {
		xForwardedHost := c.Request.Header["X-Forwarded-Host"]
		if len(xForwardedHost) > 0 {
			host = xForwardedHost[0]
		}
	}

	if err := validateHostname(host); err != nil {
		c.IndentedJSON(http.StatusBadRequest, gin.H{"message": "invalid hostname"})
		return
	}

	workspaceId, err := parseWorkspaceIdFromHost(host)
	if err != nil {
		c.IndentedJSON(http.StatusInternalServerError, gin.H{"message": "failed to parse workspace id"})
		return
	}

	tmpl, err := template.New("html").Parse(htmlTemplate)
	if err != nil {
		c.IndentedJSON(http.StatusInternalServerError, gin.H{"message": "internal error"})
		return
	}

	// We use a traefik middleware to capture 502 and 504 error to avoid displaying the page to user.
	// This is to handle the extra slash added by the middleware.
	isAPIRequest := strings.HasPrefix(c.Request.URL.Path, "/api") || strings.HasPrefix(c.Request.URL.Path, "//api")

	err = StartWorkspace(c, workspaceId)
	if err != nil && !strings.Contains(err.Error(), "is already in the process of being resumed") {

		errorMessage := fmt.Sprintf("failed to start workspace %v", err.Error())
		if strings.Contains(err.Error(), "Auto start is not enabled for this workspace") {
			errorMessage = "Auto start is not enabled for this workspace"
		}

		if isAPIRequest {
			c.IndentedJSON(http.StatusInternalServerError, gin.H{"message": errorMessage})
		} else {
			var htmlContentByte bytes.Buffer
			err = tmpl.Execute(&htmlContentByte, map[string]interface{}{
				"title":         "Failed to start workspace",
				"message":       errorMessage,
				"enableRefresh": false,
			})
			if err != nil {
				c.Data(http.StatusInternalServerError, "text/html; charset=utf-8", []byte(err.Error()))
				return
			}

			c.Data(http.StatusInternalServerError, "text/html; charset=utf-8", htmlContentByte.Bytes())
		}

		return
	}

	log.Info(fmt.Sprintf("request path: %v", c.Request.URL.Path))
	message := fmt.Sprintf("Workspace %v is starting", workspaceId.String())

	if isAPIRequest {
		// If requesting api endpoint, return json.
		c.IndentedJSON(http.StatusAccepted, gin.H{"message": message})
		return
	} else {
		// If requesting graph studio or insight, return web page.
		var htmlContentByte bytes.Buffer
		err = tmpl.Execute(&htmlContentByte, map[string]interface{}{
			"title":         "Starting workspace",
			"message":       "Starting workspace",
			"enableRefresh": true,
		})
		if err != nil {
			c.Data(http.StatusInternalServerError, "text/html; charset=utf-8", []byte(err.Error()))
			return
		}

		c.Data(http.StatusOK, "text/html; charset=utf-8", htmlContentByte.Bytes())
		return
	}

}

func HealthCheck(c *gin.Context) {
	c.IndentedJSON(http.StatusOK, gin.H{"message": "pong"})
}

func parseWorkspaceIdFromHost(host string) (uuid.UUID, error) {
	sp := strings.Split(host, ".")
	workspaceIdString := strings.TrimLeft(sp[0], "tg-")
	workspaceId, err := uuid.Parse(workspaceIdString)
	if err != nil {
		return uuid.Nil, err
	}

	return workspaceId, nil
}

func validateHostname(host string) error {
	// The pattern validates the host name starts with `tg-(uuid).`
	// We no longer validate the region, divider and the base domain in the host name since they may be different in BYOC.
	pattern := `^tg-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.`

	re, err := regexp.Compile(pattern)
	if err != nil {
		return fmt.Errorf("error compiling regular expression: %v", err)
	}

	if re.MatchString(host) {
		return nil
	} else {
		return fmt.Errorf("hostname is not valid")
	}
}

func StartWorkspace(ctx context.Context, workspaceId uuid.UUID) error {
	path := fmt.Sprintf("/autostart/workspace/%v", workspaceId.String())
	reqInput := &rest.RequestInput{
		URL: fmt.Sprintf("%s%s", config.ControllerUrl, path),
		Headers: map[string]string{
			"x-test-env":    config.XTestEnv,
			"Authorization": fmt.Sprintf("token %v", config.AutoStartToken),
		},
	}
	reqInput = reqInput.WithContext(ctx)

	_, err := rest.Post(reqInput)
	if err != nil {
		return fmt.Errorf("failed to start workspace: %v", err)
	}

	return nil
}
