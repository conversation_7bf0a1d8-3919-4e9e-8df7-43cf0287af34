package main

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	c "github.com/tigergraph/cloud-universe/autostarthandler/internal"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/logger/ctxextractor"
	"github.com/tigergraph/cloud-universe/utils/logger/loggeriface"
	lmhttp "github.com/tigergraph/cloud-universe/utils/logger/middleware/http"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
)

func main() {
	logger.ReplaceGlobal(logger.L().WithCtxExtractors([]loggeriface.CtxExtractor{ctxextractor.RequestId, ctxextractor.UserInfo}))
	c.LoadConfig()
	r := gin.Default()
	// Configure CORS
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("authorization")
	corsConf.AllowCredentials = true
	corsConf.AllowOriginFunc = func(origin string) bool {
		return true
	}
	r.Use(cors.New(corsConf), lmri.GinReqIDSetter, lmhttp.LoggerWithConfig([]string{}))
	r.GET("/ping", c.HealthCheck)
	r.NoRoute(c.AutoStart)
	r.Run("0.0.0.0:80")
}
