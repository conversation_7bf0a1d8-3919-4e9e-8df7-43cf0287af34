FROM golang:1.24.2 as builder
WORKDIR /build
COPY . .
RUN apt update -y && apt install -y curl unzip vim
RUN curl -LO https://github.com/protocolbuffers/protobuf/releases/download/v25.1/protoc-25.1-linux-x86_64.zip && \
unzip protoc-25.1-linux-x86_64.zip -d /usr/local
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.28 && \
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.2

RUN cd proto && \
./protoc.sh gateway.proto && \
./protoc.sh logger.proto && \
cd .. && \
GOOS=linux GOARCH=amd64 go build -o /app/agw agentgateway/cmd/server/server.go && chmod +x /app/agw


FROM ubuntu:25.04
# copy terraform scripts
RUN apt update && apt install -y curl vim sudo systemctl wget zip less
RUN echo "ubuntu:ubuntu" | chpasswd
RUN echo 'ubuntu ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers
RUN mkdir -p /home/<USER>/cloud-universe/terraform
WORKDIR /home/<USER>/cloud-universe/terraform
COPY ./terraform .

WORKDIR /app
COPY --from=builder /app/agw /app/agw
RUN mkdir /mnt/efs
RUN chmod +x /app/agw

COPY ./deployment/portal/transfer/teleport/setup_teleport.sh /app/setup_teleport.sh
COPY ./agentgateway/docker/start.sh /app/start.sh

# install terraform
RUN mkdir -p /terraform/plugins
RUN apt update -y && apt install -y git curl unzip netcat-traditional
RUN git clone https://github.com/tfutils/tfenv.git /home/<USER>/.tfenv
ENV TFENV_AUTO_INSTALL true
# DO NOT share the plugin cache among different workdirs as it may cause conflicts
#ENV TF_PLUGIN_CACHE_DIR /terraform/plugins
RUN /home/<USER>/.tfenv/bin/tfenv install 1.7.5
RUN /home/<USER>/.tfenv/bin/tfenv use 1.7.5

# install aws cli
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && unzip awscliv2.zip && ./aws/install

# install aws cli credential, needed by terraform aws provider
RUN mkdir -p /root/.aws
#COPY ./.aws/config /root/.aws/config

# install filebeat
RUN apt install -y wget
RUN wget https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-7.17.2-amd64.deb
RUN dpkg -i filebeat-7.17.2-amd64.deb
COPY ./agentgateway/docker/filebeat.yml /etc/filebeat/filebeat.yml

RUN chmod -R 777 /app/start.sh

EXPOSE 1234
EXPOSE 50051
EXPOSE 9955
CMD ["/app/start.sh"]
