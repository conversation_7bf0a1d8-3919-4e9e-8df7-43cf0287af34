#!/bin/bash


# Setup SSH and Teleport
mkdir -p /home/<USER>/.ssh
echo "${TELEPORT_IDENTITY}" | base64 -d | gzip -d > /home/<USER>/.ssh/teleport_identity
echo "${AGENT_GATEWAY_CONFIG}" | base64 -d >> /app/agent_gateway.yaml

# Install Teleport
teleport_auth_server=tigergraph.teleport.sh
teleport_auth_token=b7f195481f9f2136a086cc2b18d02759
teleport_version=15.4.16

echo "ubuntu" | sudo -S sh /app/setup_teleport.sh ${Env} agw 0 ${teleport_auth_server} ${teleport_auth_token} ${teleport_version}

chmod 400 /home/<USER>/.ssh/teleport_identity
echo "IdentityFile /home/<USER>/.ssh/teleport_identity" > /home/<USER>/.ssh/config

sudo sed -i "s/ES_HOSTS/$ES_HOSTS/g" /etc/filebeat/filebeat.yml
sudo systemctl enable filebeat
sudo systemctl start filebeat

echo "Starting AGW..."
# Start the main application
exec /app/agw