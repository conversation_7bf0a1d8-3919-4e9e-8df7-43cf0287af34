hostingaddr: 0.0.0.0:50051
serverexporteraddr: 0.0.0.0:9955
queueconfig:
    redisurl: redis://localhost:6379
    redisrdtimeout: 5
    rediswttimeout: 5
    workercount: 1000
    clustermode: false
servercertname: ""
serverkeyname: ""
cacertname: ""
caprivatekeyname: ""
s3bucketname: tg-agent-gateway
s3uploadtimeout: 15
enablegrpclogging: false
updatetimeout: 20
insecureskipauthserver: false
region: us-west-1
pinginterval: 60
processinterval: 3
maxheartbeatmissseconds: 120
skipinitialloading: false