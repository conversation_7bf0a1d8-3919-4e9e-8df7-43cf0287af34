# ============================== Filebeat inputs ===============================

filebeat.inputs:
- type: filestream
  enabled: true
  id: terraform
  paths:
  - /app/log/*
  parsers:
  - ndjson:
      keys_under_root: true
      overwrite_keys: true

processors:
- dissect:
    tokenizer: "/mnt/efs/terraform/cloud-provider/%{orgId}/%{platform}/%{region}/%{cpName}/%{cpId}"
    field: "workdir"
    target_prefix: ""
# ============================== Filebeat modules ==============================
filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false
# ======================= Elasticsearch template setting =======================
setup.template.enabled: true
setup.template.settings:
  index.number_of_shards: 1
setup.ilm.enabled: true
setup.ilm.policy_name: "logs"
setup.ilm.overwrite: false
setup.ilm.rollover_alias: "cloud-agentgateway-log"
setup.template.overwrite: false
setup.template.name: "cloud-agentgateway-log"
setup.template.pattern: "cloud-agentgateway-log-*"

# ---------------------------- Elasticsearch Output ----------------------------
output.elasticsearch:
  hosts: ["ES_HOSTS"]
  protocol: "http"
  index: "cloud-agentgateway-log"

# ================================== Logging ===================================
logging.level: info
