## How to build AgentGateway
### Dependencies
AgentGateway(AGW) needs the following environmental settings:
- .aws/config .aws/credentials files available in HOME.
- cloud-universe/ get checked out in HOME folder
- docker is installed
- user(ubuntu) has sudo permission

### Steps to Build
1. Assume HOME is in /home/<USER>/ to /home/<USER>/cloud-universe/
2. cd $HOME; cp ~/cloud-universe/build_image_agw.sh .
3. bash ./build_image_agw.sh

In step 3, docker build will be called and finally the generated image will be pushed to docker hub.


## How to run AgentGateway
### Dependencies
- /mnt/efs exsits in host and will be mounted as a volumne in AGW container
- you will need cloud-universe/agentgateway/docker/agent_gateway.yaml and docker-compose.yaml to get copied to a working folder, say agw/

### Steps
- cd agw/ 
- sudo docker compose up