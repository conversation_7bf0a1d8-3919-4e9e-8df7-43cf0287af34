hostingaddr: 0.0.0.0:50051
serverexporteraddr: 0.0.0.0:9955
tgiamconfig:
  iamService: https://api.tgcloud-dev.com/iam
  orgService: https://apx7zkpgw3.execute-api.us-east-1.amazonaws.com/dev
  clientID: TbaIi568sw50vN26QntoMNTgP2McaGxK
  clientSecret: "qPCUg1sGrWHrWObMUP7Y9yQ_bCmIAYxzPsbt4y6rJB5IH6Wln5XcLIl_BTqB9BOC"
  auth0HostName: tgcloud-dev.auth0.com
queueconfig:
    redisurl: redis://localhost:6379/
    redisrdtimeout: 5
    rediswttimeout: 5
    workercount: 1000
    clustermode: false
servercertname: ""
serverkeyname: ""
cacertname: ""
caprivatekeyname: ""
s3bucketname: tg-agent-gateway
s3uploadtimeout: 15
enablegrpclogging: false
updatetimeout: 20
insecureskipauthserver: false
region: us-east-2
pinginterval: 60
processinterval: 3
maxheartbeatmissseconds: 120
skipinitialloading: false
validatetoken: false
