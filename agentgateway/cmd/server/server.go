package main

import (
	"flag"
	"net"
	"os"
	"sync"

	"github.com/tigergraph/cloud-universe/utils/config"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/logger/loggeriface"
	zaplogger "github.com/tigergraph/cloud-universe/utils/logger/zap"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"

	server "github.com/tigergraph/cloud-universe/agentgateway/server"
	ctxextractor "github.com/tigergraph/cloud-universe/utils/logger/ctxextractor"
)

var (
	grpcConfig server.Configuration
	wg         sync.WaitGroup
)

func init() {
	log := logger.L()
	grpcConfig = server.Configuration{
		HostingAddr:        "0.0.0.0:50051",
		ServerExporterAddr: "0.0.0.0:9955",
		// ServerCertName:        os.Getenv("CERT_NAME"),
		// ServerKeyName:         os.Getenv("KEY_NAME"),
		// CACertName:            os.Getenv("CA_CERT_NAME"),
		// CAPrivateKeyName:      os.Getenv("CA_KEY_NAME"),
		// TransactionTableName:  os.Getenv("TRANSACTION_TABLE_NAME"),
		// InstanceTableName:     os.Getenv("INSTANCE_TABLE_NAME"),
		QueueConfig: taskqueue.QueueConfig{
			RedisURL:       "redis://localhost:6379",
			RedisRdTimeout: 5,
			RedisWtTimeout: 5,
			WorkerCount:    1000,
		},

		S3BucketName:    "tg-agent-gateway",
		S3UploadTimeout: 15,

		EnableGRPCLogging:       false,
		UpdateTimeout:           20,
		InsecureSkipAuthServer:  false,
		Region:                  "us-east-2",
		PingInterval:            60,
		ProcessInterval:         3,
		MaxHeartbeatLostSeconds: 30,
		SkipInitialLoading:      false,
		ValidateToken:           true,
	}

	if config.LoadYAMLConfig("./agent_gateway.yaml", &grpcConfig) != nil {
		log.Warn("main: failed to load agent_gateway.yaml, using default values....")
	}

	redisUrl, ok := os.LookupEnv("RedisUrl")
	if ok {
		log.Infof("main: RedisUrl env override, set to: %s", redisUrl)
		grpcConfig.QueueConfig.RedisURL = redisUrl
	}

	region, ok := os.LookupEnv("AWS_REGION")
	if ok {
		log.Infof("main: AWS_REGION env override, set to: %s", region)
		grpcConfig.Region = region
	}

	bucketName, ok := os.LookupEnv("S3_BUCKET_NAME")
	if ok {
		log.Infof("main: S3_BUCKET_NAME env override, set to: %s", bucketName)
		grpcConfig.S3BucketName = bucketName
	}

	cstr, err := config.ConfigToYAMLString(&grpcConfig)
	if err != nil {
		log.Errorf("main: failed to convert configuration to yaml string: %v", err)
	} else {
		log.Infof("main: using configuration: %+v", cstr)
	}
}

func main() {
	flag.Parse()

	wg.Add(1)
	go func() {
		logger.ReplaceGlobal(zaplogger.New(loggeriface.Config{
			Component:        "agentgateway",
			Filename:         "./log/rotated-log",
			MaxSize:          100,
			MaxAge:           30,
			MaxBackups:       100,
			LocalTime:        false,
			Compress:         true,
			Level:            loggeriface.InfoLevel,
			CtxExtractorList: []loggeriface.CtxExtractor{ctxextractor.RequestId, ctxextractor.UserInfo},
		}))
		log := logger.L()
		lis, err := net.Listen("tcp", grpcConfig.HostingAddr)
		if err != nil {
			log.Panicf("server main: failed to listen at address %v: %v", grpcConfig.HostingAddr, err)
		}

		defer wg.Done()

		s, myServer, err := server.New(grpcConfig)
		_ = myServer
		if err != nil {
			log.Panicf("server main: unable to start new gRPC service: %v", err)
		}

		log.Infof("server main: server listening at %v", lis.Addr())
		if err := s.Serve(lis); err != nil {
			log.Infof("server main: failed to serve at address %v: %v", grpcConfig.HostingAddr, err)
		}

	}()
	wg.Wait()
}
