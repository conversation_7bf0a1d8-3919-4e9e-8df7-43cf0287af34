package server

import (
	"fmt"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	tgredis "github.com/tigergraph/cloud-universe/utils/redis"
)

func TestRemoveAddAgentFromStore(t *testing.T) {
	mockRdsSvc := &tgredis.MockRedisClient{}
	server := &Server{
		AwsClients: AwsClients{
			RdsSvc: mockRdsSvc,
		},
		Addr: "test-addr",
	}

	// Test valid function call.
	stringCmd := redis.NewStringResult("test-addr||false", nil)
	mockRdsSvc.On("Get", mock.Anything, "test").Return(stringCmd)

	intCmd := redis.NewIntResult(1, nil)
	mockRdsSvc.On("Del", mock.Anything, []string{"test"}).Return(intCmd)

	server.removeAgentFromStore("test", false)

	// Test clientId not in cache.
	stringCmd2 := redis.NewStringResult("", fmt.Errorf("test-error"))
	mockRdsSvc.On("Get", mock.Anything, "test-error").Return(stringCmd2)

	server.removeAgentFromStore("test-error", false)

	// Test delete cache entry failure.
	stringCmd3 := redis.NewStringResult("test-addr||false", nil)
	mockRdsSvc.On("Get", mock.Anything, "test-error-2").Return(stringCmd3)

	intCmd3 := redis.NewIntResult(1, fmt.Errorf("test-error-2"))
	mockRdsSvc.On("Del", mock.Anything, []string{"test-error-2"}).Return(intCmd3)

	server.removeAgentFromStore("test-error-2", false)

	// Test successful addAgentToStore.
	statusCmd := redis.NewStatusResult("", nil)
	mockRdsSvc.On("Set", mock.Anything, "test", mock.Anything, mock.Anything).Return(statusCmd)
	server.addAgentToStore("test", false)

	// Test unsuccessful addAgentToStore.
	statusCmd2 := redis.NewStatusResult("", fmt.Errorf("test-error"))
	mockRdsSvc.On("Set", mock.Anything, "test-error", mock.Anything, mock.Anything).Return(statusCmd2)
	server.addAgentToStore("test-error", false)

	mockRdsSvc.AssertExpectations(t)
}
