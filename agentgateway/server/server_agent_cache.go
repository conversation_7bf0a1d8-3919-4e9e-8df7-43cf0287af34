package server

import (
	"context"
	"fmt"

	"github.com/tigergraph/cloud-universe/utils/logger"
)

// addAgentToStore adds a cache entry (clientId, AGW address) into designated redis cache
// the cache here will be used when multiple AGW exists in the future
func (s *Server) addAgentToStore(clientId string, isMaint bool) {
	log := logger.L()
	err := s.AwsClients.RdsSvc.Set(context.Background(), clientId, fmt.Sprintf("%v||%v", s.Addr, isMaint), 0).Err()
	if err != nil {
		log.Warnf("AgentGateway: [%v] failed to add [%v] to shared cache %v", s.Config.HostingAddr, clientId, err)
	} else {
		// log.Infof("AgentGateway: [%v] successfully add [%v] to shared cache", s.Config.HostingAddr, clientId)
	}
}

// removeAgentFromStore removes the cache entry with the key as clientId when the client disconnects from the service.
func (s *Server) removeAgentFromStore(clientId string, isMaint bool) {
	log := logger.L()
	// first check which queue url is handling this client
	res, err := s.AwsClients.RdsSvc.Get(context.Background(), clientId).Result()
	if err != nil {
		log.Warnf("AgentGateway: [%v] [%v] not in shared cache %v", s.Config.HostingAddr, clientId, err)
	}
	// if the agent is connecting to other service
	if res != fmt.Sprintf("%v||%v", s.Addr, isMaint) {
		log.Warnf("AgentGateway: [%v] [%v] is not connecting to this service", s.Config.HostingAddr, clientId)
		return
	}

	// if it's me, delete the entry
	_, err = s.AwsClients.RdsSvc.Del(context.Background(), clientId).Result()
	if err != nil {
		log.Warnf("AgentGateway: [%v] failed to delete [%v] from shared cache %v", s.Config.HostingAddr, clientId, err)
	} else {
		log.Infof("AgentGateway: [%v] successfully remove [%v] from shared cache", s.Config.HostingAddr, clientId)
	}
}
