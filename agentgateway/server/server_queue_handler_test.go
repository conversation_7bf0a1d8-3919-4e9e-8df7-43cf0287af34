package server

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
)

func TestTriggerCPMetaUpdateTask(t *testing.T) {
	// Code coverage of error case for 'producer.SendAndWaitResult()' in 'TriggerCPMetaUpdateTask()
	// is not always reached, so we force a timeout error.
	server := &Server{
		Addr: "test-addr",
	}

	redisServer = miniredis.RunT(t)
	defer redisServer.Close()

	mServer, err := createTestTaskQueueServer()
	assert.Nil(t, err)

	server.MachineryServer = mServer
	server.TriggerCPMetaUpdateTask(context.Background(), "test-id", &map[string][]byte{})

	// Task queue timeout is 5 seconds, so we wait 6.
	time.Sleep(6 * time.Second)
}
