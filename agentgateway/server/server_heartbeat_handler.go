package server

import (
	"context"
	"time"

	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	"github.com/tigergraph/cloud-universe/common/token"
	pb "github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/peer"
)

// GetRegistration handles the registration request from agents, keeps the connection streams in memory.
func (s *Server) HandleHeartbeat(ctx context.Context, stream pb.TGControlPlaneService_ControlAgentCommandsServer, clientMD ClientMetadata, hbTask *pb.AgentMessage) error {
	log := logger.L().WithContext(ctx)
	clientId, version, isMaintenance := clientMD.id, clientMD.version, clientMD.isMaintenance
	start := time.Now()

	// for logging purpose
	p, _ := peer.FromContext(stream.Context())
	clientIPAddr := ""
	if p != nil {
		clientIPAddr = p.Addr.String()
	}

	componentVersion := "unknown"
	orgId := ""
	params := hbTask.GetParams()
	if params != nil {
		v, ok := params["ComponentVersions"]
		if ok {
			componentVersion = string(v)
		}
		v, ok = params["OrgId"]
		if ok {
			orgId = string(v)
		}
	}

	// in case of token validation is disabled, set ttl to 2 times of renew threshold to avoid token renewal
	ttl := int(token.RenewThreshold.Seconds()) * 2
	err := error(nil)
	if s.Config.ValidateToken {
		apiTokenServ := token.NewCloudProviderTokenService(s.TGIAM, clientId, orgId, token.DefaultKeyLifeTimeInSeconds)
		ttl, err = apiTokenServ.ValidateToken(clientMD.token)
		// TODO: consider expired token as normal since it may trigger a token renewal
		if err != nil {
			log.Errorf("AgentGateway: [%v] failed to validate token for agent %v [maintenance: %v], error: %v", s.Addr, clientId, isMaintenance, err)
			IncFailureCount(clientIPAddr, codes.Unauthenticated)
			return err
		}
	}

	k := ClientMapKey{clientId: clientId, isMaint: isMaintenance}

	ci, ok := s.MyClients.Load(k)
	if ok {
		// log.Infof(
		// 	"AgentGateway: [%v] an existing connection from agent %v [maintenance: %v] is connecting again.",
		// 	s.Addr, clientId, isMaintenance)
	} else {
		// not exist, a new connection is established with this server
		log.Infof("AgentGateway: [%v] new connection from agent %v [maintenance: %v]", s.Addr, clientId, isMaintenance)
		IncNumClient()
		IncSuccessCount(clientId, clientIPAddr)

		info := NewClientInfo(clientId, stream, version, isMaintenance, s.MachineryServer, componentVersion, clientMD.token)
		s.MyClients.Store(k, info)
		s.MyClientsReflection.Store(stream, k)

		ci, ok = s.MyClients.Load(k)
		if !ok {
			log.Errorf("AgentGateway: %v ABNORMAL failed to load client info for %v [maintenance: %v]", s.Addr, clientId, isMaintenance)
			return nil
		}
	}

	clientInfo := ci.(*ClientInfo)
	clientInfo.ProcessHeartbeat(stream, hbTask, &clientMD, ttl)

	ReportRegistrationResponseTime(time.Since(start))

	s.addAgentToStore(clientId, isMaintenance)

	// log.Infof("AgentGateway: [%v] processed CA heartbeat request from %v, cv %v, current client counts: %v", s.Addr, clientId, componentVersion, s.MyClientsCnt)
	return nil
}

func (s *Server) startStaleChecker() {
	go func() {
		for range time.Tick(time.Duration(s.Config.ProcessInterval) * time.Second) {
			log := logger.L()
			// transactionId := uuid.New().String()
			// log.Infof("StaleChecker: checking at %v, transaction id %v", time.Now(), transactionId)
			// increase the stale check counter
			IncStaleCheckCount()

			s.MyClients.Range(func(k, v interface{}) bool {
				clientInfo := v.(*ClientInfo)
				if clientInfo.CurrentState() == cp.StateActive && time.Since(clientInfo.lastHeartbeat) > time.Duration(s.Config.MaxHeartbeatLostSeconds)*time.Second {
					log.Warnf("StaleChecker: agent %v [maintenance: %v] got stale, last heartbeat at %v, duration: %v",
						clientInfo.cloudProviderId, k.(ClientMapKey).isMaint, clientInfo.lastHeartbeat, time.Since(clientInfo.lastHeartbeat))
					clientInfo.ProcessHeartbeatLost()
				}
				return true
			})
		}
	}()
}
