package server

import (
	"fmt"
	"strings"

	"github.com/redis/go-redis/v9"
	"github.com/tigergraph/cloud-universe/utils/logger"
	tgredis "github.com/tigergraph/cloud-universe/utils/redis"
)

// NewRdsCli initializes a redis client
func (s *Server) NewRdsCli() {
	log := logger.L()
	log.Infof("redis cache url: %v", s.Config.QueueConfig.RedisURL)
	url := s.Config.QueueConfig.RedisURL
	clusterMode := s.Config.QueueConfig.ClusterMode

	url = strings.TrimPrefix(url, "redis://")

	if s.AwsClients.RdsSvc == nil && !clusterMode {
		s.AwsClients.RdsSvc = tgredis.NewRedisClient(&redis.Options{
			Addr:     fmt.Sprintf("%v", url),
			Password: "",
			DB:       0, // use default DB
		})
	} else if s.AwsClients.RdsSvc == nil && clusterMode {
		s.AwsClients.RdsSvc = tgredis.NewRedisClusterClient(&redis.ClusterOptions{
			Addrs:    strings.Split(url, ","),
			Password: "",
		})
	}
}
