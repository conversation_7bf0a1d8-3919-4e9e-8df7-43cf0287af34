package server

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/tidwall/gjson"
	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	pb "github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Server) NewTaskQueue() {
	log := logger.L()
	if s.MachineryServer == nil {
		var err error
		s.MachineryServer, err = taskqueue.NewMachineryServer(&s.Config.QueueConfig)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to create new machinery server: %v", err)
		}

		// create/register/start task worker
		s.AgentGatewayConsumer, err = taskqueue.NewMachineryTaskWorker(s.MachineryServer)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to create new machinery task worker: %v", err)
		}

		err = s.AgentGatewayConsumer.RegisterTask(taskqueue.ControllerTaskTypeGeneral, s.QueueTaskHandler)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to register task: %v", err)
		}
		log.Infof("NewTaskQueue: successfully registered task")

		err = s.AgentGatewayConsumer.StartWorker(taskqueue.TGTaskQueueName, s.Config.QueueConfig.WorkerCount)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to start worker: %v", err)
		}

		err = s.AgentGatewayConsumer.RegisterTask(taskqueue.ControllerTaskTypeAGW, s.AGWTaskHandler)

		if err != nil {
			log.Panicf("NewTaskQueue: failed to register AGW task: %v", err)
		}
		log.Infof("NewTaskQueue: successfully registered AGW task")

		err = s.AgentGatewayConsumer.StartWorker(taskqueue.TGTaskQueueName, 100)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to start worker: %v", err)
		}

		log.Infof("NewTaskQueue: successfully started machinery task worker")

		// create task producer
		s.ProviderStatusProducer, err = taskqueue.NewMachineryTaskProducer(s.MachineryServer)
		if err != nil {
			log.Panicf("NewTaskQueue: failed to create new machinery task producer: %v", err)
		}

		log.Infof("NewTaskQueue: successfully created machinery server and task worker")

	}
}

func (s *Server) FailAllPendingTasks() {
	s.PendingTaskMap.Range(func(key, value interface{}) bool {
		taskChan := value.(chan *pb.AgentMessage)
		taskChan <- nil
		return true
	})
}

func (s *Server) QueueTaskHandler(ctx context.Context, taskStr string) ([]byte, error) {
	// counter for all incoming tasks
	IncTotalTasksCount()
	log := logger.L().WithContext(ctx)

	task := &taskqueue.TGTask{}
	err := json.Unmarshal([]byte(taskStr), task)
	if err != nil {
		IncErrorTasksCount()
		log.Error("Failed to unmarshal task: ", err, ", task: ", taskStr)
		return nil, err
	}

	clientId := task.Target
	if clientId == "" {
		log.Warnf("QueueTaskHandler: empty target in task: %v", taskStr)
		IncErrorTasksCount()
		return nil, fmt.Errorf("empty target in task: %v", taskStr)
	}

	// default timeout is 60 seconds
	if task.Deadline.IsZero() {
		task.Deadline = time.Now().Add(time.Duration(time.Second * 60))
	}

	k := ClientMapKey{clientId: clientId, isMaint: false}

	info, ok := s.MyClients.Load(k)

	if !ok {
		log.Warnf("AgentGateway: could not find provider %v [isMaint: %v] is not connecting to the server. err: %v", clientId, false, err)
		IncErrorTasksCount()
		return nil, fmt.Errorf("agent not found")
	}
	clientInfo := info.(*ClientInfo)

	// relay the task from message queue to cloud provider
	if task.Command != taskqueue.ControllerCmdListWorkspaces {
		log.Infof("AgentGateway: received task id: %v, cmd: %v, resourceID: %v", task.Id, task.Command, clientId)
	}

	if clientInfo.GetNetState() != NetStateConnected {
		log.Warnf("AgentGateway: [%v] Agent %v is not connected", s.Id(), clientId)
		IncErrorTasksCount()
		return nil, fmt.Errorf("not connected")
	}

	if !clientInfo.AbleToComm() {
		log.Warnf("AgentGateway: [%v] Agent %v state is not able to communicate", s.Id(), clientId)
		log.Warnf("    cloud provider state: %v", clientInfo.fsm.Current())
	}

	taskId := task.Id
	if taskId == "" {
		taskId = uuid.New().String()
		log.Errorf("QueueTaskHandler: empty taskId in task: %v, generate uuid insead: %v", taskStr, taskId)
	}
	taskType := task.Type
	if taskType == "" {
		log.Errorf("QueueTaskHandler: empty taskType in task: %v, set to general instead", taskStr)
		taskType = taskqueue.ControllerTaskTypeGeneral
	}
	ts := task.Timestamp
	if ts.IsZero() {
		ts = time.Now()
	}

	IncNumRequest()
	stream := clientInfo.stream

	// Set up a context with timeout
	ctx, cancel := context.WithDeadline(ctx, task.Deadline)
	defer cancel()

	// make a new channel for the task
	taskChan := make(chan *pb.AgentMessage, 1)
	// add the task to task map
	s.PendingTaskMap.Set(taskId, taskChan)
	IncPendingTasksCount()

	err = stream.Send(lmri.ProtoMessageReqIdSetter(ctx, &pb.ControlMessage{
		Id:        taskId,
		Initiator: s.Id(),
		Target:    clientId,
		Type:      taskType,
		Command:   task.Command,
		Timestamp: timestamppb.New(ts),
		Params:    task.Params,
	}).(*pb.ControlMessage))

	if err != nil {
		log.Warnf("AgentGateway: [%v] SendMsg: can't send msg to the agent [%v]", s.Id(), clientId)
		IncErrorTasksCount()
		return nil, err
	}

	// Wait for response or timeout
	select {
	case <-ctx.Done():
		log.Warnf("AgentGateway: Task: timeout waiting for response from CP: %v, task: %v/%v", clientId, taskType, task.Command)
		// remove the task from task map
		DecPendingTasksCount()
		s.PendingTaskMap.Delete(taskId)

		IncErrorTasksCount()
		return nil, fmt.Errorf("timeout waiting for agent response")

	case resp := <-taskChan:
		// remove the task from task map
		DecPendingTasksCount()
		s.PendingTaskMap.Delete(taskId)
		if resp != nil {

			respTask := PB2Task(resp)
			resultBytes, err := json.Marshal(respTask)
			if err != nil {
				log.Error("Failed to marshal task: ", err, ", task: ", task)

				IncErrorTasksCount()
				return nil, err
			}
			return resultBytes, nil

		} else {
			log.Warnf("AgentGateway: [%v] Task: empty response", taskId)
			IncErrorTasksCount()
			return nil, fmt.Errorf("empty response, taskId: %v", taskId)
		}
	}
}

func (s *Server) FindOrAddClientInfo(clientId string) (*ClientInfo, error) {
	k := ClientMapKey{clientId: clientId, isMaint: false}
	info, ok := s.MyClients.Load(k)
	if !ok {
		// create a new client info
		clientInfo := NewClientInfo(clientId, nil, "0.0", false, s.MachineryServer, "{}", "")
		// it should be disconnect state
		clientInfo.Disconnect()
		s.MyClients.Store(k, clientInfo)
		return clientInfo, nil
	}
	return info.(*ClientInfo), nil
}

func (s *Server) TriggerCloudProviderEvent(ctx context.Context, cloudProviderId string, event string) (*ClientInfo, error) {
	// find the client info
	log := logger.L().WithContext(ctx)
	k := ClientMapKey{clientId: cloudProviderId, isMaint: false}
	info, ok := s.MyClients.Load(k)
	if !ok {
		log.Errorf("AgentGateway: could not find provider %v [isMaint: %v] is not connecting to the server", cloudProviderId, false)
		return nil, fmt.Errorf("AgentGateway: agent %v [isMaint: %v] is not connecting to the server", cloudProviderId, false)
	}
	clientInfo := info.(*ClientInfo)

	err := clientInfo.TriggerEvent(ctx, event)
	if err != nil {
		log.Errorf("AgentGateway: CloudProvider %v [maintenance: %v] failed to trigger event: %v", cloudProviderId, false, err)
		return nil, err
	}
	return clientInfo, nil
}

func (s *Server) TriggerCPMetaUpdateTask(ctx context.Context, cloudProviderId string, params *map[string][]byte) error {
	(*params)["resourceId"] = []byte(cloudProviderId)
	task := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: cloudProviderId,
		Target:    cloudProviderId,
		Type:      taskqueue.AGWTaskTypeGeneral,
		Command:   taskqueue.AGWTaskCmdUpdateCloudProviderMeta,
		Timestamp: time.Now(),
		Params:    *params,
	}

	log := logger.L().WithContext(ctx)
	const MaxRetry = 3
	go func() {
		for i := 0; i < MaxRetry; i++ {
			producer, err := taskqueue.NewMachineryTaskProducer(s.MachineryServer)
			if err != nil {
				log.Errorf("AGWTaskHandler: Unable to create producer in update CloudProvider meta, id: %v, err: %v", cloudProviderId, err)
				continue
			} else {
				resp, err := producer.SendAndWaitResult(ctx, taskqueue.TGProviderStatusQueueName, task, AGWTaskTimeout)
				if err != nil {
					log.Errorf("AGWTaskHandler: Unable to update CloudProvider meta, id: %v, err: %v", cloudProviderId, err)
				} else {
					if resp != nil && resp.Status != taskqueue.TaskStatusOk {
						log.Errorf("AGWTaskHandler: failed to update CloudProvider meta: %v", resp.Error)
					}
					if resp.Status == taskqueue.TaskStatusOk {
						log.Infof("AGWTaskHandler: successfully updated CloudProvider meta: cloud provider: %v", cloudProviderId)
						return
					}
				}
			}
			// sleep for a while before retry
			time.Sleep(time.Second * 3)
		}
		log.Errorf("failed to update CloudProvider meta after %v retries", MaxRetry)
	}()
	return nil
}

func (s *Server) AGWTaskHandler(ctx context.Context, taskStr string) ([]byte, error) {
	log := logger.L().WithContext(ctx)
	// counter for all incoming tasks
	IncTotalTasksCount()

	task := &taskqueue.TGTask{}
	err := json.Unmarshal([]byte(taskStr), task)
	if err != nil {
		IncErrorTasksCount()
		log.Error("Failed to unmarshal task: ", err, ", task: ", taskStr)
		return nil, err
	}

	clientId := task.Target
	if clientId == "" {
		log.Warnf("QueueTaskHandler: empty target in task: %v", taskStr)
		IncErrorTasksCount()
		return nil, fmt.Errorf("empty target in task")
	}

	// default timeout is 60 seconds
	if task.Deadline.IsZero() {
		task.Deadline = time.Now().Add(time.Duration(time.Second * 60))
	}

	switch task.Command {
	case taskqueue.ControllerAGWCmdProvisionProvider:
		return s.AGWTaskHandleProvision(ctx, task)
	case taskqueue.ControllerAGWCmdDeleteProvider:
		return s.AGWTaskHandleDelete(ctx, task)
	case taskqueue.ControllerAGWCmdListProviders:
		return s.AGWTaskHandleListProviders(ctx, task)
	case taskqueue.ControllerAGWCmdUpgradeProvider:
		return s.AGWTaskHandleUpgradeProvider(ctx, task)

	default:
		log.Warnf("AGWTaskHandler: unsupported command: %v", task.Command)
		IncErrorTasksCount()
		return nil, fmt.Errorf("unsupported command")
	}
}

func (s *Server) AGWTaskHandleProvision(ctx context.Context, task *taskqueue.TGTask) ([]byte, error) {
	log := logger.L().WithContext(ctx)
	providerType := string(task.Params["providerType"])
	resourceId := string(task.Params["resourceId"])
	workDir := string(task.Params["workDir"])
	config := &map[string][]byte{}
	err := json.Unmarshal(task.Params["config"], config)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to unmarshal config: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	log.Infof("AGWTaskHandler: provisioning cloud provider providerType: %v, resource: %v, workDir: %v",
		providerType, resourceId, workDir)
	clientInfo, err := s.FindOrAddClientInfo(resourceId)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to find or add client info: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	// validate client state before actually provision the resource
	if clientInfo.CurrentState() == cp.StateActive {
		log.Warn("AGWTaskHandler: client %v is able to communicate, ignoring the provision request", resourceId)
		log.Warn("   cloud provider state: ", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is already active")
	}
	if clientInfo.CurrentState() == cp.StateProvisioning {
		log.Warn("AGWTaskHandler: client %v is already provisioning, ignoring the provision request", resourceId)
		log.Warn("   cloud provider state: ", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is already provisioning")
	}
	if clientInfo.CurrentState() == cp.StateDeleting {
		log.Warn("AGWTaskHandler: client %v is deleting, ignoring the provision request", resourceId)
		log.Warn("   cloud provider state: ", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is being deleted")
	}

	// provision the cloud provider resource
	provisioner, err := s.ProvisionerFactory.GetProvisioner(providerType)

	if err != nil {
		log.Errorf("AGWTaskHandler: failed to get cloud provider provisioner: %v, type: %v", err, providerType)
		IncErrorTasksCount()
		return nil, err
	}

	err = provisioner.Provision(ctx, resourceId, workDir, config, func(resourceId string, provisionErr error, output string) {
		log.Infof("Entering AGWTaskHandler Provision callback: %v, Err: %v", resourceId, provisionErr)
		if provisionErr != nil {
			IncErrorTasksCount()

			// notify resource manager that provision failed reason
			params := map[string][]byte{
				"action":     []byte("provision"),
				"lastError":  []byte(provisionErr.Error()),
				"resourceId": []byte(resourceId),
			}
			s.TriggerCPMetaUpdateTask(ctx, resourceId, &params)

			// this is to avoid controller get racing condition for the status update
			time.Sleep(300 * time.Millisecond)

			// trigger the provision failed event
			_, err := s.TriggerCloudProviderEvent(ctx, resourceId, EventProvisionFailed)
			if err != nil {
				log.Errorf("AGWTaskHandler: provision callback failed to trigger cloud provider event: %v", err)
				IncErrorTasksCount()
				return
			}
		} else {
			// provision succeeded
			log.Infof("AGWTaskHandler: tf provision cloud provider resource succeeded, waiting for heartbeat: %v", resourceId)
			// remember the eks information from output
			eksClusterId := gjson.Get(output, "values.outputs.eks_host.value").String()
			albHostname := gjson.Get(output, "values.outputs.ingress_host_name.value").String()
			endpointServiceName := gjson.Get(output, "values.outputs.endpoint_service_name.value").String()
			availabilityZones := gjson.Get(output, "values.outputs.availability_zones.value").String()

			if eksClusterId == "" {
				log.Errorf("AGWTaskHandler: failed to extract eksClusterId from output: %v", output)
			} else {
				// update eks cluster ID to resource manager
				params := map[string][]byte{
					"eksClusterId":        []byte(eksClusterId),
					"albHostname":         []byte(albHostname),
					"resourceId":          []byte(resourceId),
					"endpointServiceName": []byte(endpointServiceName),
					"availabilityZones":   []byte(availabilityZones),
				}
				s.TriggerCPMetaUpdateTask(ctx, resourceId, &params)
			}

		}
	})
	if err != nil {
		IncErrorTasksCount()
		log.Errorf("AGWTaskHandler: failed to provision cloud provider resource: %v", err)
		return nil, err
	}

	// trigger the provision event and so that state should be changed to provisioning
	_, err = s.TriggerCloudProviderEvent(ctx, resourceId, EventProvision)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to trigger cloud provider event: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	respTask := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: s.Id(),
		Target:    task.Initiator,
		Type:      task.Type,
		Command:   task.Command,
		Timestamp: time.Now(),
		ReplyToId: task.Id,
		Status:    0,
		Params: map[string][]byte{
			"resourceId": []byte(resourceId),
		},
	}

	resultBytes, err := json.Marshal(respTask)
	if err != nil {
		log.Error("Failed to marshal task: ", err, ", task: ", task)
		IncErrorTasksCount()
		return nil, err
	}

	return resultBytes, nil
}

func (s *Server) AGWTaskHandleDelete(ctx context.Context, task *taskqueue.TGTask) ([]byte, error) {
	log := logger.L().WithContext(ctx)
	resourceId := string(task.Params["resourceId"])
	providerType := string(task.Params["providerType"])
	workDir := string(task.Params["workDir"])
	secureConnection, err := strconv.ParseBool(string(task.Params["secureConnection"]))
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to parse 'secureConnection' bool value: %s, %v", string(task.Params["secureConnection"]), err)
		IncErrorTasksCount()
		return nil, err
	}

	clientInfo, err := s.FindOrAddClientInfo(resourceId)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to find or add client info: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	// validate client state before actually delete the resource

	// we should respect DB status, not the AGW state
	// if clientInfo.CurrentState() == cp.StateDeleted {
	// 	log.Warning("AGWTaskHandler: client %v is already deleted, ignoring the delete request", resourceId)
	// 	log.Warning("   cloud provider state: ", clientInfo.fsm.Current())
	// 	IncErrorTasksCount()
	// 	return nil, fmt.Errorf("resource is already deleted")
	// }

	if clientInfo.CurrentState() == cp.StateDeleting {
		log.Warnf("AGWTaskHandler: client %v is already deleting, ignoring the delete request", resourceId)
		log.Warnf("   cloud provider state: %s", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is already being deleted")
	}
	if clientInfo.CurrentState() == cp.StateProvisioning {
		log.Warnf("AGWTaskHandler: client %v is provisioning, ignoring the delete request", resourceId)
		log.Warnf("   cloud provider state: %s", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is provisioning")
	}
	if clientInfo.CurrentState() == cp.StateUpgrading {
		log.Warnf("AGWTaskHandler: client %v is upgrading, ignoring the delete request", resourceId)
		log.Warnf("   cloud provider state: %s", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is upgrading")
	}

	// delete the cloud provider resource
	provisioner, err := s.ProvisionerFactory.GetProvisioner(providerType)

	if err != nil {
		log.Errorf("AGWTaskHandler: failed to get cloud provider provisioner: %v, type: %v", err, providerType)
		IncErrorTasksCount()
		return nil, err
	}

	err = provisioner.Delete(ctx, resourceId, workDir, secureConnection, func(resourceId string, deleteErr error, output string) {
		if deleteErr != nil {
			log.Errorf("AGWTaskHandler: failed to tf delete cloud provider resource: %v", deleteErr)
			IncErrorTasksCount()
			// notify resource manager that delete failed reason
			params := map[string][]byte{
				"action":     []byte("delete"),
				"lastError":  []byte(deleteErr.Error()),
				"resourceId": []byte(resourceId),
			}
			s.TriggerCPMetaUpdateTask(ctx, resourceId, &params)

			// this is to avoid controller get racing condition for the status update
			time.Sleep(300 * time.Millisecond)

			// trigger the delete failed event
			_, err := s.TriggerCloudProviderEvent(ctx, resourceId, EventDeleteFailed)
			if err != nil {
				log.Errorf("AGWTaskHandler: delete callback failed to trigger deletefailed event: %v", err)
				IncErrorTasksCount()
				return
			}
		} else {
			log.Infof("AGWTaskHandler: tf delete cloud provider resource succeeded, waiting for heartbeat loss: %v", resourceId)

			_, err := s.TriggerCloudProviderEvent(ctx, resourceId, EventDeleteComplete)
			if err != nil {
				log.Errorf("AGWTaskHandler: delete callback failed to trigger delete complete event: %v", err)
				IncErrorTasksCount()
				return
			}

			// do not delete metrics and workDir if the status update is failed since in that case we may retry through reconciler
			DeleteClientStateMetrics(resourceId)
			// remove .terraform directory to avoid excessive disk usage
			if len(workDir) >= 10 {
				dirToDelete := fmt.Sprintf("%v/.terraform/", workDir)
				err := os.RemoveAll(dirToDelete)
				if err != nil {
					log.Errorf("AGWTaskHandler: deleting CP, failed to remove .terraform directory: %v, dir: %v", err, dirToDelete)
				}
				dirToDelete = fmt.Sprintf("%v/helm-charts/.terraform/", workDir)
				err = os.RemoveAll(dirToDelete)
				if err != nil {
					log.Errorf("AGWTaskHandler: deleting CP, failed to remove helm-charts/.terraform directory: %v, dir: %v", err, dirToDelete)
				}
			} else {
				log.Errorf("AGWTaskHandler: deleting CP, workDir is too short: %v", workDir)
			}

		}
	})

	if err != nil {
		IncErrorTasksCount()
		log.Errorf("AGWTaskHandler: failed to delete cloud provider resource: %v", err)
		return nil, err
	}

	// trigger the delete event and so that state should be changed to deleting
	_, err = s.TriggerCloudProviderEvent(ctx, resourceId, EventDelete)
	if err != nil {
		log.Errorf("AGWTaskHandler: delete task failed to trigger cloud provider event: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	respTask := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: s.Id(),
		Target:    task.Initiator,
		Type:      task.Type,
		Command:   task.Command,
		ReplyToId: task.Id,
		Timestamp: time.Now(),
		Status:    0,
		Params: map[string][]byte{
			"resourceId": []byte(resourceId),
		},
	}

	resultBytes, err := json.Marshal(respTask)
	if err != nil {
		log.Error("Failed to marshal task: ", err, ", task: ", task)
		IncErrorTasksCount()
		return nil, err
	}
	return resultBytes, nil
}

func (s *Server) AGWTaskHandleListProviders(ctx context.Context, task *taskqueue.TGTask) ([]byte, error) {
	// list all cloud providers
	log := logger.L().WithContext(ctx)
	cloudProviders := make([]cp.CloudProviderInfo, 0)
	s.MyClients.Range(func(key, value interface{}) bool {
		clientInfo := value.(*ClientInfo)
		cpInfo := clientInfo.ToCloudProviderInfo()
		cloudProviders = append(cloudProviders, *cpInfo)
		return true
	})

	jsonBytes, err := json.Marshal(cloudProviders)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to marshal cloud providers: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	respTask := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: s.Id(),
		Target:    task.Initiator,
		Type:      task.Type,
		Command:   task.Command,
		ReplyToId: task.Id,
		Timestamp: time.Now(),
		Status:    0,
		Params: map[string][]byte{
			"CloudProviderInfo": jsonBytes,
		},
	}

	resultBytes, err := json.Marshal(respTask)
	if err != nil {
		log.Error("Failed to marshal task: ", err, ", task: ", task)
		IncErrorTasksCount()
		return nil, err
	}
	return resultBytes, nil
}

func (s *Server) AGWTaskHandleUpgradeProvider(ctx context.Context, task *taskqueue.TGTask) ([]byte, error) {
	log := logger.L().WithContext(ctx)
	providerType := string(task.Params["providerType"])
	resourceId := string(task.Params["resourceId"])
	workDir := string(task.Params["workDir"])
	config := &map[string][]byte{}
	err := json.Unmarshal(task.Params["config"], config)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to unmarshal config: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	log.Infof("AGWTaskHandler: upgrading cloud provider providerType: %v, resource: %v, workDir: %v",
		providerType, resourceId, workDir)

	// make sure workDir exists
	info, err := os.Stat(workDir)
	if os.IsNotExist(err) {
		return nil, fmt.Errorf("workDir does not exist")
	}
	if err != nil {
		return nil, err
	}
	if !info.IsDir() {
		return nil, fmt.Errorf("workDir is not a directory")
	}

	clientInfo, err := s.FindOrAddClientInfo(resourceId)
	if err != nil {
		log.Errorf("AGWTaskHandler: failed to find or add client info: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	// validate client state before actually upgrade the resource
	currentState := clientInfo.CurrentState()
	if currentState == cp.StateProvisioning {
		log.Warnf("AGWTaskHandler: client %v is provisioning, ignoring the upgrade request", resourceId)
		log.Warnf("   cloud provider state: %s", currentState)
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is provisioning")
	}
	if currentState == cp.StateDeleting {
		log.Warnf("AGWTaskHandler: client %v is deleting, ignoring the upgrade request", resourceId)
		log.Warnf("   cloud provider state: %s", currentState)
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is being deleted")
	}
	if currentState == cp.StateUpgrading {
		log.Warnf("AGWTaskHandler: client %v is already upgrading, ignoring the upgrade request", resourceId)
		log.Warnf("   cloud provider state: %s", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is upgrading")
	}
	if currentState == cp.StateDeleteFailed {
		log.Warnf("AGWTaskHandler: client %v is in delete failed, ignoring the upgrade request", resourceId)
		log.Warnf("   cloud provider state: %s", clientInfo.fsm.Current())
		IncErrorTasksCount()
		return nil, fmt.Errorf("resource is in delete failed")
	}

	// upgrade the cloud provider resource
	provisioner, err := s.ProvisionerFactory.GetProvisioner(providerType)

	if err != nil {
		log.Errorf("AGWTaskHandler: failed to get cloud provider provisioner: %v, type: %v", err, providerType)
		IncErrorTasksCount()
		return nil, err
	}

	err = provisioner.Upgrade(ctx, resourceId, workDir, config, func(resourceId string, upgradeErr error, output string) {
		if upgradeErr != nil {
			log.Errorf("AGWTaskHandler: failed to tf upgrade cloud provider resource: %v", upgradeErr)
			IncErrorTasksCount()
			// notify resource manager that upgrade failed reason
			params := map[string][]byte{
				"action":     []byte("upgrade"),
				"lastError":  []byte(upgradeErr.Error()),
				"resourceId": []byte(resourceId),
			}
			s.TriggerCPMetaUpdateTask(ctx, resourceId, &params)

			// this is to avoid controller get racing condition for the status update
			time.Sleep(300 * time.Millisecond)

			// trigger the upgrade failed event
			_, err := s.TriggerCloudProviderEvent(ctx, resourceId, EventUpgradeFailed)
			if err != nil {
				log.Errorf("AGWTaskHandler: upgrade callback failed to trigger cloud provider event: %v", err)
				IncErrorTasksCount()
				return
			}
		} else {
			log.Infof("AGWTaskHandler: tf upgrade cloud provider resource succeeded, waiting for heartbeat: %v", resourceId)
			// remember the eks information from output
			eksClusterId := gjson.Get(output, "values.outputs.eks_host.value").String()
			albHostname := gjson.Get(output, "values.outputs.ingress_host_name.value").String()
			endpointServiceName := gjson.Get(output, "values.outputs.endpoint_service_name.value").String()
			availabilityZones := gjson.Get(output, "values.outputs.availability_zones.value").String()

			if eksClusterId == "" {
				log.Errorf("AGWTaskHandler: upgrade failed to extract eksClusterId for id: %v, output: %v", resourceId, output)
			} else {
				// update eks cluster ID to resource manager
				params := map[string][]byte{
					"eksClusterId":        []byte(eksClusterId),
					"albHostname":         []byte(albHostname),
					"resourceId":          []byte(resourceId),
					"endpointServiceName": []byte(endpointServiceName),
					"availabilityZones":   []byte(availabilityZones),
					"lastError":           []byte("NoError"), // clear the last error
				}
				s.TriggerCPMetaUpdateTask(ctx, resourceId, &params)
			}

			// this is to avoid controller get racing condition for the status update
			time.Sleep(300 * time.Millisecond)

			// trigger the upgrade completed event
			_, err := s.TriggerCloudProviderEvent(ctx, resourceId, EventUpgradeComplete)
			if err != nil {
				log.Errorf("AGWTaskHandler: upgrade callback failed to trigger cloud provider complete event: %v", err)
				IncErrorTasksCount()
				return
			}

		}
	})

	if err != nil {
		IncErrorTasksCount()
		log.Errorf("AGWTaskHandler: failed to upgrade cloud provider resource: %v", err)
		return nil, err
	}

	// trigger the upgrade event and so that state should be changed to upgrading
	_, err = s.TriggerCloudProviderEvent(ctx, resourceId, EventUpgrade)
	if err != nil {
		log.Errorf("AGWTaskHandler: upgrade task failed to trigger cloud provider event: %v", err)
		IncErrorTasksCount()
		return nil, err
	}

	respTask := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: s.Id(),
		Target:    task.Initiator,
		Type:      task.Type,
		Command:   task.Command,
		ReplyToId: task.Id,
		Timestamp: time.Now(),
		Status:    0,
		Params: map[string][]byte{
			"resourceId": []byte(resourceId),
		},
	}

	resultBytes, err := json.Marshal(respTask)
	if err != nil {
		log.Error("Failed to marshal task: ", err, ", task: ", task)
		IncErrorTasksCount()
		return nil, err
	}
	return resultBytes, nil
}
