package server

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/RichardKnop/machinery/v1"
	"github.com/google/uuid"
	"github.com/looplab/fsm"
	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	tk "github.com/tigergraph/cloud-universe/common/token"
	pb "github.com/tigergraph/cloud-universe/proto"

	"github.com/tigergraph/cloud-universe/utils/logger"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

const (
	AGWTaskTimeout = 5
	RenewInterval  = 15 * time.Minute
)

const (
	NetStateConnected    = 0
	NetStateDisconnected = 1
)

type ClientInfo struct {
	sync.RWMutex
	cloudProviderId string
	stream          pb.TGControlPlaneService_ControlAgentCommandsServer
	version         string
	isMaint         bool
	lastHeartbeat   time.Time // last time the agent sent heartbeat

	createdAt       time.Time
	machineryServer *machinery.Server
	fsm             *fsm.FSM
	netState        int

	componentVersion  string
	apiToken          string
	tokenTTLInSeconds int
}

func NewClientInfo(cloudProviderId string, stream pb.TGControlPlaneService_ControlAgentCommandsServer, version string, isMaint bool, server *machinery.Server, componentVersion string, token string) *ClientInfo {
	c := &ClientInfo{
		cloudProviderId:   cloudProviderId,
		stream:            stream,
		version:           version,
		isMaint:           isMaint,
		lastHeartbeat:     time.Now(),
		createdAt:         time.Now(),
		machineryServer:   server,
		componentVersion:  componentVersion,
		apiToken:          token,
		tokenTTLInSeconds: int(tk.RenewThreshold.Seconds()) * 2, // set to 2 times of renew threshold to avoid accidental token renewal
	}
	if stream != nil {
		c.netState = NetStateConnected
	} else {
		c.netState = NetStateDisconnected
	}
	c.initFSM(cp.StateStart)
	return c
}
func NewClientInfoFromCloudProviderInfo(info *cp.CloudProviderInfo, server *machinery.Server) *ClientInfo {
	client := &ClientInfo{
		cloudProviderId:   info.CloudProviderId,
		stream:            nil,
		version:           info.Version,
		lastHeartbeat:     info.LastHeartbeat,
		createdAt:         info.CreatedAt,
		machineryServer:   server,
		componentVersion:  info.ComponentVersion,
		apiToken:          info.APIToken,
		tokenTTLInSeconds: int(tk.RenewThreshold.Seconds()) * 2, // set to 2 times of renew threshold to avoid accidental token renewal
	}

	if client.lastHeartbeat.Equal(time.Time{}) {
		client.lastHeartbeat = time.Now()
	}
	if client.stream != nil {
		client.netState = NetStateConnected
	} else {
		client.netState = NetStateDisconnected
	}

	client.initFSM(info.Status)
	return client
}

func NewClientInfoFromJson(data []byte) (*ClientInfo, error) {
	info := &cp.CloudProviderInfo{}
	err := json.Unmarshal(data, info)
	if err != nil {
		return nil, err
	}

	client := &ClientInfo{
		cloudProviderId:  info.CloudProviderId,
		version:          info.Version,
		lastHeartbeat:    info.LastHeartbeat,
		createdAt:        info.CreatedAt,
		componentVersion: info.ComponentVersion,
		apiToken:         info.APIToken,
	}
	client.initFSM(info.Status)
	return client, nil
}

func (c *ClientInfo) GetCloudProviderId() string {
	c.RLock()
	defer c.RUnlock()
	return c.cloudProviderId
}

func (c *ClientInfo) GetStream() pb.TGControlPlaneService_ControlAgentCommandsServer {
	c.RLock()
	defer c.RUnlock()
	return c.stream
}

func (c *ClientInfo) GetAPIToken() string {
	c.RLock()
	defer c.RUnlock()
	return c.apiToken
}

func (c *ClientInfo) GetComponentVersion() string {
	c.RLock()
	defer c.RUnlock()
	return c.componentVersion
}

func (c *ClientInfo) GetNetState() int {
	c.RLock()
	defer c.RUnlock()
	return c.netState
}

func (c *ClientInfo) Disconnect() int {
	c.Lock()
	defer c.Unlock()
	originalState := c.netState
	c.netState = NetStateDisconnected
	return originalState
}

func (c *ClientInfo) connect() int {
	originalState := c.netState
	c.netState = NetStateConnected
	return originalState
}

func (c *ClientInfo) newFSMStateChangeTask(srcState string, newState string, resourceId string) *taskqueue.TGTask {
	return &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: c.cloudProviderId,
		Target:    resourceId,
		Type:      taskqueue.AGWTaskTypeGeneral,
		Command:   taskqueue.AGWTaskCmdUpdateCloudProviderState,
		Timestamp: time.Now(),
		Params: map[string][]byte{
			"resourceId": []byte(resourceId),
			"SrcState":   []byte(srcState),
			"DstState":   []byte(newState),
		},
	}
}

func (c *ClientInfo) initFSM(initialState string) {
	c.fsm = fsm.NewFSM(
		initialState,
		fsm.Events{
			{Name: "heartbeat", Src: []string{"start"}, Dst: "active"},
			{Name: "provision", Src: []string{"start"}, Dst: "provisioning"},
			{Name: "provisionfailed", Src: []string{"provisioning"}, Dst: "provisionfailed"},
			{Name: "provision", Src: []string{"provisionfailed"}, Dst: "provisioning"},
			{Name: "heartbeat", Src: []string{"provisioning"}, Dst: "active"},
			{Name: "heartbeat", Src: []string{"provisionfailed"}, Dst: "active"},
			{Name: "heartbeat", Src: []string{"stale"}, Dst: "active"},
			{Name: "heartbeatlost", Src: []string{"active"}, Dst: "stale"},
			{Name: "delete", Src: []string{"start"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"active"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"stale"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"provisionfailed"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"deletefailed"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"provisioning"}, Dst: "deleting"},
			{Name: "delete", Src: []string{"deleted"}, Dst: "deleting"},
			{Name: "deletefailed", Src: []string{"deleting"}, Dst: "deletefailed"},
			{Name: "deletecomplete", Src: []string{"deleting"}, Dst: "deleted"},
			{Name: "heartbeatlost", Src: []string{"deleting"}, Dst: "deleted"},
			{Name: "heartbeatlost", Src: []string{"deletefailed"}, Dst: "deleted"},
			{Name: "upgrade", Src: []string{"start"}, Dst: "upgrading"},
			{Name: "upgrade", Src: []string{"active"}, Dst: "upgrading"},
			{Name: "upgrade", Src: []string{"stale"}, Dst: "upgrading"},
			{Name: "upgrade", Src: []string{"provisionfailed"}, Dst: "upgrading"},
			{Name: "upgrade", Src: []string{"upgradefailed"}, Dst: "upgrading"},
			{Name: "upgradefailed", Src: []string{"upgrading"}, Dst: "upgradefailed"},
			{Name: "upgradecomplete", Src: []string{"upgrading"}, Dst: "upgradecomplete"},
			{Name: "heartbeat", Src: []string{"upgradefailed"}, Dst: "active"},
			{Name: "heartbeat", Src: []string{"upgradecomplete"}, Dst: "active"},
			{Name: "delete", Src: []string{"upgradefailed", "upgradecomplete"}, Dst: "deleting"},
		},
		fsm.Callbacks{
			"enter_state": func(ctx context.Context, e *fsm.Event) {
				c.enterState(ctx, e)
			},
		},
	)
}

func (c *ClientInfo) GetFSM() *fsm.FSM {
	c.RLock()
	defer c.RUnlock()
	return c.fsm
}

func (c *ClientInfo) enterState(ctx context.Context, e *fsm.Event) {
	log := logger.L().WithContext(ctx)
	// log.Infof("AgentGateway: CloudProvider %v [maintenance: %v] enters state %v", c.cloudProviderId, c.isMaint, e.Dst)
	if e.Src != e.Dst {
		// generate task to notify controller that state has changed only when it is the state owned by AGW
		if e.Dst == cp.StateActive || e.Dst == cp.StateStale ||
			e.Dst == cp.StateProvisionFailed || e.Dst == cp.StateDeleteFailed || e.Dst == cp.StateDeleted ||
			e.Dst == cp.StateUpgradeFailed || e.Dst == cp.StateUpgradeComplete {

			task := c.newFSMStateChangeTask(e.Src, e.Dst, c.cloudProviderId)
			producer, err := taskqueue.NewMachineryTaskProducer(c.machineryServer)
			if err != nil {
				log.Errorf("AgentGateway: State Change %v [maintenance: %v] failed to create task producer: %v", c.cloudProviderId, c.isMaint, err)
				log.Errorf("    state: %v -> %v", e.Src, e.Dst)
				return
			}

			// notify controller the state change in the background
			go func() {
				resp, err := producer.SendAndWaitResult(ctx, taskqueue.TGProviderStatusQueueName, task, AGWTaskTimeout)
				if err != nil {
					log.Errorf("AgentGateway: State Change %v [maintenance: %v], state: %v -> %v, failed to send task: %v", c.cloudProviderId, c.isMaint, e.Src, e.Dst, err)
					return
				}

				if resp.Status != taskqueue.TaskStatusOk {
					log.Errorf("AgentGateway: State Change %v [maintenance: %v], state: %v -> %v, got failure from controller: %v", c.cloudProviderId, c.isMaint, e.Src, e.Dst, resp.Error)
					return
				}
				log.Infof("AgentGateway: State Change %v, state change posted to controller: %v -> %v", c.cloudProviderId, e.Src, e.Dst)
			}()

			// heartbeat may comes in before the above goroutine actually send out the task to controller
			// and when that happens, we will have a racing condition for the state change task since heartbeat may drive
			// the state change to active with another goroutine
			// so we introduce a short delay here to make sure the state change task is sent out before the next heartbeat.
			// we must do it before the return of this function since the state change will be done after this function returns.
			time.Sleep(200 * time.Millisecond)
		}
	} else {
		log.Infof("AgentGateway: CloudProvider %v [maintenance: %v] state unchanged: %v -> %v", c.cloudProviderId, c.isMaint, e.Src, e.Dst)
	}
}

func (c *ClientInfo) GetVersion() string {
	return c.version
}

func (c *ClientInfo) ProcessHeartbeat(stream pb.TGControlPlaneService_ControlAgentCommandsServer, hbTask *pb.AgentMessage, meta *ClientMetadata, tokenTTLInSeconds int) error {
	// set gauge to indicate the client is connected
	ctx := lmri.NewContextFromMessage(context.Background(), hbTask)
	log := logger.L().WithContext(ctx)
	c.Lock()
	defer c.Unlock()

	SetClientStateGauge(c.cloudProviderId, true)

	c.lastHeartbeat = time.Now()
	c.connect() // set netState to connected
	params := hbTask.GetParams()
	if params != nil {
		if componentVersion, ok := params["ComponentVersions"]; ok {
			c.componentVersion = string(componentVersion)
		}
	}

	c.tokenTTLInSeconds = tokenTTLInSeconds
	if c.apiToken != meta.token {
		// update api token in memory
		log.Infof("AgentGateway: CloudProvider %v new api token detected, update in memory", c.cloudProviderId)
		c.apiToken = meta.token
	}

	if c.stream != stream {
		log.Infof("AgentGateway: CloudProvider %v new stream detected, reconnect happens?", c.cloudProviderId)
		c.stream = stream
	}

	err := c.fsm.Event(ctx, EventHeartbeat)
	if err != nil {
		return err
	}
	return nil
}

func (c *ClientInfo) ProcessHeartbeatLost() error {
	log := logger.L()
	c.RLock()
	defer c.RUnlock()
	log.Errorf("AgentGateway: CloudProvider %v [maintenance: %v] heartbeatlost. Last Update: %v", c.cloudProviderId, c.isMaint, c.lastHeartbeat)
	// set gauge to indicate the client is disconnected
	SetClientStateGauge(c.cloudProviderId, false)

	err := c.fsm.Event(context.Background(), "heartbeatlost")
	if err != nil {
		log.Errorf("AgentGateway: CloudProvider %v heartbeatlost. event error: %v", c.cloudProviderId, err)
		return err
	}
	return nil
}

func (c *ClientInfo) TriggerEvent(ctx context.Context, event string) error {
	log := logger.L().WithContext(ctx)
	c.RLock()
	defer c.RUnlock()
	log.Infof("AgentGateway: CloudProvider %v  Event triggered: %v. Last Update: %v", c.cloudProviderId, event, c.lastHeartbeat)
	err := c.fsm.Event(context.Background(), event)
	if err != nil {
		log.Errorf("AgentGateway: CloudProvider %v Trigger event: %v, error: %v", c.cloudProviderId, event, err)
		return err
	}
	return nil
}

func (c *ClientInfo) AbleToComm() bool {
	c.RLock()
	defer c.RUnlock()
	if c.netState != NetStateConnected {
		return false
	}
	if c.fsm.Current() == cp.StateActive {
		return true
	} else {
		return false
	}
}

func (c *ClientInfo) CurrentState() string {
	c.RLock()
	defer c.RUnlock()
	return c.fsm.Current()
}

func (c *ClientInfo) ToCloudProviderInfo() *cp.CloudProviderInfo {
	c.RLock()
	defer c.RUnlock()
	return &cp.CloudProviderInfo{
		CloudProviderId:   c.cloudProviderId,
		Version:           c.version,
		ComponentVersion:  c.componentVersion,
		TokenTTLInSeconds: c.tokenTTLInSeconds,
		Status:            c.fsm.Current(),
		CreatedAt:         c.createdAt,
		LastHeartbeat:     c.lastHeartbeat,
	}
}

func (c *ClientInfo) ToJson() ([]byte, error) {
	c.RLock()
	defer c.RUnlock()
	info := c.ToCloudProviderInfo()
	return json.Marshal(info)
}
