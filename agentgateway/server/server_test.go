package server

import (
	"context"
	"fmt"
	"net"
	"testing"
	"time"

	b64 "encoding/base64"
	"encoding/json"

	"github.com/RichardKnop/machinery/v1"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	cagConf "github.com/tigergraph/cloud-universe/clusteragent/config"
	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	pb "github.com/tigergraph/cloud-universe/proto"
	cloudprovider "github.com/tigergraph/cloud-universe/resource-manager/cloud_provider"
	awsCP "github.com/tigergraph/cloud-universe/resource-manager/cloud_provider/aws"
	helmcharts "github.com/tigergraph/cloud-universe/resource-manager/cloud_provider/helm_charts"
	conf "github.com/tigergraph/cloud-universe/resource-manager/config"
	resConf "github.com/tigergraph/cloud-universe/resource-manager/config"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	yaml "gopkg.in/yaml.v3"
)

const (
	addr = ":0"
	id   = "cloudprovider1"
)

var ExportGetMetadata = (*Server).GetMetadata
var redisServer *miniredis.Miniredis
var machineryServer *machinery.Server

func loadConfig() (Configuration, error) {
	config := Configuration{
		HostingAddr:            "localhost:50055",
		ServerExporterAddr:     "localhost:9954",
		ServerCertName:         "server.pem",
		ServerKeyName:          "server-key.pem",
		CACertName:             "ca.pem",
		EnableGRPCLogging:      true,
		UpdateTimeout:          20,
		InsecureSkipAuthServer: true,
		Region:                 "us-east-1",
		PingInterval:           100,
		QueueConfig: taskqueue.QueueConfig{
			RedisURL:       fmt.Sprintf("redis://%v", redisServer.Addr()),
			RedisRdTimeout: 5,
			RedisWtTimeout: 5,
			WorkerCount:    1000,
		},
		TGIAMConfig: conf.TGIAMConfig{
			IAMService:    "https://api.tgcloud-dev.com/iam",
			OrgService:    "https://apx7zkpgw3.execute-api.us-east-1.amazonaws.com/dev",
			ClientID:      "TbaIi568sw50vN26QntoMNTgP2McaGxK",
			ClientSecret:  "{{ AUTH0_SECRET }}",
			Auth0HostName: "tgcloud-dev.auth0.com",
		},
		SkipInitialLoading: true,
	}

	return config, nil
}

func createTestTaskQueueServer() (*machinery.Server, error) {
	log := logger.L()
	config, err := loadConfig()
	if err != nil {
		return nil, err
	}

	machineryServer, err = taskqueue.NewMachineryServer(&config.QueueConfig)
	if err != nil {
		log.Errorf("Failed to create new machinery server: %v", err)
		return nil, err
	}
	return machineryServer, nil
}

func sendAndVerifyGeneralTask(t *testing.T, server *machinery.Server, initiator string) error {
	producer, err := taskqueue.NewMachineryTaskProducer(server)
	if err != nil {
		t.Fatalf("Failed to create Machinery task producer: %s", err)
	}

	task := &taskqueue.TGTask{
		Id:        "123",
		Initiator: initiator,
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Params:    map[string][]byte{"cmd": []byte("ListWorkspace"), "workspaceid": []byte("wid1")},
		Timestamp: time.Now(),
	}

	res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 20)
	if err != nil {
		t.Fatalf("Failed to send task: %s", err)
	}

	// assert the result
	require.Equal(t, res.Id, task.Id)
	require.Equal(t, task.Id, res.ReplyToId)
	require.Equal(t, task.Initiator, res.Target)
	require.Equal(t, task.Target, res.Initiator)
	require.Equal(t, task.Type, res.Type)

	return err
}

func createServer(t *testing.T) (net.Listener, *Server, pb.TGControlPlaneServiceClient, error) {
	config, err := loadConfig()
	require.NoError(t, err)

	l, err := net.Listen("tcp", config.HostingAddr)
	require.NoError(t, err)

	s, myServer, err := New(config)
	myServer.ProvisionerFactory = cloudprovider.NewMockCloudProvisionerFactory()
	require.NoError(t, err)

	go func() {
		_ = s.Serve(l)
	}()

	clientOptions := []grpc.DialOption{grpc.WithInsecure()}
	cc, err := grpc.Dial(l.Addr().String(), clientOptions...)
	require.NoError(t, err)

	client := pb.NewTGControlPlaneServiceClient(cc)

	return l, myServer, client, nil

}

func TestServer(t *testing.T) {
	for scenario, fn := range map[string]func(
		t *testing.T,
		server *Server,
		client pb.TGControlPlaneServiceClient,
	){
		"test missing metadata":   testMissingMetadata,
		"test register":           testRegister,
		"test task queue handler": testQueueHandler,
		"test provision":          testProvision,
		"test upgrade":            testUpgrade,
		"test delete":             testDelete,
	} {
		redisServer = miniredis.RunT(t)
		defer redisServer.Close()
		s, err := createTestTaskQueueServer()
		require.NoError(t, err)
		machineryServer = s

		l, server, client, err := createServer(t)

		iam := tgIAM.NewPseudoTGIAM()
		server.TGIAM = iam
		require.NoError(t, err)
		t.Run(scenario, func(t *testing.T) {
			fn(t, server, client)
		})

		// clean up
		l.Close()

	}
}

func setupTest(t *testing.T) (
	server *Server,
	client pb.TGControlPlaneServiceClient,
	teardown func(),
) {
	t.Helper()

	config, err := loadConfig()
	require.NoError(t, err)

	l, err := net.Listen("tcp", config.HostingAddr)
	require.NoError(t, err)

	s, myServer, err := New(config)
	myServer.ProvisionerFactory = cloudprovider.NewMockCloudProvisionerFactory()
	require.NoError(t, err)

	go func() {
		_ = s.Serve(l)
	}()

	clientOptions := []grpc.DialOption{grpc.WithInsecure()}
	cc, err := grpc.Dial(l.Addr().String(), clientOptions...)
	require.NoError(t, err)

	client = pb.NewTGControlPlaneServiceClient(cc)

	return myServer, client, func() {
		s.Stop()
		_ = cc.Close()
		_ = l.Close()
	}
}

func testMissingMetadata(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	md := metadata.Pairs(
		"id", id,
		"token", "",
		"ismaint", "0",
	)

	ctx := context.Background()
	_, err := ExportGetMetadata(server, ctx, md)
	require.NotNil(t, err)

	md = metadata.Pairs(
		"version", "1.0",
		"token", "",
		"ismaint", "0",
	)

	_, err = ExportGetMetadata(server, ctx, md)
	require.NotNil(t, err)

	md = metadata.Pairs(
		"id", id,
		"version", "1.0",
		"ismaint", "0",
	)

	_, err = ExportGetMetadata(server, ctx, md)
	require.NotNil(t, err)

	md = metadata.Pairs(
		"version", "1.0",
		"id", id,
		"token", "",
	)

	_, err = ExportGetMetadata(server, ctx, md)
	require.NotNil(t, err)

	md = metadata.Pairs()
	ctx = metadata.NewOutgoingContext(context.Background(), md)
	stream, err := client.ControlAgentCommands(ctx)
	require.NoError(t, err)
	require.NotNil(t, stream)
	err = stream.Send(&pb.AgentMessage{
		Id:        "testid1",
		Type:      taskqueue.AgentTaskTypeHeartBeat,
		Initiator: "testproviderid1",
		Target:    "testtargetid1",
		Params:    map[string][]byte{"param1": []byte("value1"), "param2": []byte("value2")},
		Timestamp: timestamppb.New(time.Now()),
	})
	require.NoError(t, err)

	time.Sleep(300 * time.Millisecond)
}

// since Send() is nonblocking, we need some time delay to
// make sure the service receives the registration request
func testRegister(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	log := logger.L()
	// register with a new agent
	log.Debugf("id is %v", id)
	version := "1.0"
	md := metadata.Pairs(
		"version", version,
		"id", id,
		"token", "Pseudo-key",
		"ismaint", "0",
	)
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	clientStream, err := client.ControlAgentCommands(ctx)
	require.NoError(t, err)

	err = clientStream.Send(&pb.AgentMessage{
		Id:        "testid1",
		Type:      taskqueue.AgentTaskTypeHeartBeat,
		Initiator: "testproviderid1",
		Target:    "testtargetid1",
		Params:    map[string][]byte{"param1": []byte("value1"), "param2": []byte("value2")},
		Timestamp: timestamppb.New(time.Now()),
	})
	require.NoError(t, err)

	time.Sleep(3000 * time.Millisecond)

	// register with new version agent
	md["version"] = []string{"1.1"}
	ctx = metadata.NewOutgoingContext(context.Background(), md)

	clientStream, err = client.ControlAgentCommands(ctx)
	require.NoError(t, err)

	err = clientStream.Send(&pb.AgentMessage{
		Id:        "testid2",
		Type:      taskqueue.AgentTaskTypeHeartBeat,
		Initiator: "testproviderid1",
		Target:    "testtargetid1",
		Params:    map[string][]byte{"param1": []byte("value1"), "param2": []byte("value2")},
		Timestamp: timestamppb.New(time.Now()),
	})
	require.NoError(t, err)
	time.Sleep(300 * time.Millisecond)

	//require.Equal(t, "1.1", server.MyClients[id].version)

}

func testQueueHandler(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	log := logger.L()
	version := "1.0"
	md := metadata.Pairs(
		"version", version,
		"id", id,
		"token", "Pseudo-key",
		"ismaint", "0",
	)

	ctx := metadata.NewOutgoingContext(context.Background(), md)

	clientStream, err := client.ControlAgentCommands(ctx)
	require.NoError(t, err)

	err = clientStream.Send(&pb.AgentMessage{
		Id:        "testid1",
		Type:      taskqueue.AgentTaskTypeHeartBeat,
		Initiator: "testproviderid1",
		Target:    "testtargetid1",
		Params:    map[string][]byte{"param1": []byte("value1"), "param2": []byte("value2")},
		Timestamp: timestamppb.New(time.Now()),
	})
	require.NoError(t, err)

	// create go routine to recv from clientStream
	stop := make(chan bool)

	go func() {
		for {
			select {
			case <-stop:
				log.Warn("testQueueHandler: stop go routine")
				return
			default:
				msg, err := clientStream.Recv()
				if err != nil {
					log.Errorf("Failed to receive message: %v", err)
					return
				}
				log.Infof("Received message: %v", msg)
				taskResult := pb.NewTaskResult(msg)
				taskResult.Params = map[string][]byte{"result": []byte("success")}

				// send the result back to server
				err = clientStream.Send(taskResult)
				if err != nil {
					log.Errorf("Failed to send message: %v", err)
					return
				}
				log.Info("testQueueHandler: sent result back to server")
				// stop the go routine
				return
			}
		}
	}()

	time.Sleep(1000 * time.Millisecond)

	sendAndVerifyGeneralTask(t, machineryServer, server.Id())
	log.Info("testQueueHandler: done verification")

}

func testProvision(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	log := logger.L()
	// register with a new agent
	log.Debugf("id is %v", id)

	// send a AGW provision task so the server can provision a cloud provider
	// it is already mock provisioner factory in the server

	producer, err := taskqueue.NewMachineryTaskProducer(server.MachineryServer)
	if err != nil {
		t.Fatalf("Failed to create Machinery task producer: %s", err)
	}

	clientId := "cloudprovider1"

	factory, err := cloudprovider.NewRemoteCloudProviderProvisionerFactory(server.MachineryServer, id)
	require.NoError(t, err)

	remoteProvisioner, err := factory.GetProvisioner("aws")
	require.NoError(t, err)

	// use remote prosioner to create provision task

	workDir := "/tmp/workDir"
	config := &map[string][]byte{}
	tfConfig := &awsCP.AWSTemplateData{
		VPCCIDR:         "10.0.0.0/16",
		Region:          "us-east-1",
		VPCOwnedByTG:    false,
		ShortID:         "tg-483077524",
		RoleARN:         "arn:aws:iam::123456789012:role/eksctl-eksname1-nodegroup-ng-NodeInstanceRole-1GZJ5ZQZQZQZQ",
		ImagePullSecret: "secret1",
		BaseDomain:      "tgcloud-dev.com",
	}

	helmInput := &helmcharts.HelmChartsTemplateData{
		EKSName:                         "EksName1",
		Region:                          "us-east-1",
		RoleARN:                         "arn:aws:iam::123456789012:role/eksctl-eksname1-nodegroup-ng-NodeInstanceRole-1GZJ5ZQZQZQZQ",
		ClusterAgentReplicaCount:        1,
		ClusterAgentImagePullPolicy:     "always",
		ClusterAgentImageTag:            "v1.1",
		AutoStartHandlerReplicaCount:    1,
		AutoStartHandlerImagePullPolicy: "always",
		AutoStartHandlerImageTag:        "v1.2",
		ImagePullSecret:                 "secret2",
		AutoStartToken:                  "token1",
		ControllerURL:                   "controllerurl1",
		ClusterAgentConfigB64:           "",
		TeleportToken:                   "teleporttoken1",
		Env:                             "",
		TGOperatorImageTag:              "v1.3",
		CloudProviderVersionNumber:      "1",
	}

	clusterAgentConfig := cagConf.Config{
		CloudProviderId: clientId,
		OrgId:           "org1",
		Token:           "AGWtoken1",
		AuthServerAddr:  "",
		GRPCServerAddr:  "AGWaddr1",
		ExporterAddr:    "",
		MaxMsgSize:      65536000,

		AuthServerTimeOut:    5,
		RetryCount:           5,
		RetryInterval:        5,
		HeartbeatInterval:    60,
		NonSecure:            false,
		UpdateTimeOut:        5,
		UpdateTicker:         5,
		MaxTransactionNum:    99,
		TransactionRateLimit: 99,
		MaintenanceLiveSpan:  99,
		K8SConfig: resConf.K8SConfig{
			Region:    "us-east-1",
			Endpoint:  "https://kubernetes.default.svc",
			Subdomain: "us-east-1.i.tgcloud-dev.com",
			CertARN:   "",
		},

		ClusterAgentImageTag:     "v1.1",
		AutoStartHandlerImageTag: "v1.2",
		TGOperatorImageTag:       "v1.3",
	}

	clusterAgentConfig.K8SConfig.Token = ""
	clusterAgentConfigYamlBytes, err := yaml.Marshal(clusterAgentConfig)
	if err != nil {
		log.Errorf("failed to marshal cluster agent config: %v", err)
	}
	require.NoError(t, err)

	clusterAgentConfigB64 := b64.StdEncoding.EncodeToString([]byte(clusterAgentConfigYamlBytes))
	helmInput.ClusterAgentConfigB64 = clusterAgentConfigB64

	bytes, err := json.Marshal(tfConfig)
	require.NoError(t, err)
	(*config)["tf"] = bytes

	bytes, err = json.Marshal(helmInput)
	require.NoError(t, err)
	(*config)["helm"] = bytes

	configBytes, err := json.Marshal(config)
	require.NoError(t, err)

	task := &taskqueue.TGTask{
		Id:        "123",
		Initiator: id,
		Target:    "AGW",
		Type:      taskqueue.ControllerTaskTypeAGW,
		Command:   taskqueue.ControllerAGWCmdProvisionProvider,
		Params: map[string][]byte{
			"providerType": []byte("aws"),
			"resourceId":   []byte(clientId),
			"config":       configBytes,
			"workDir":      []byte("/tmp/workDir"),
		},
		Timestamp: time.Now(),
	}

	input := cloudprovider.ProvisionerInput{
		Err:       nil,
		SleepTime: 1 * time.Second,
		DoneErr:   nil,
	}

	log.Infof("ProvisionerFactory.GetProvisioner: %v", server.ProvisionerFactory)
	p, err := server.ProvisionerFactory.GetProvisioner("aws")
	log.Infof("ProvisionerFactory.GetProvisioner: %v", p)
	require.NoError(t, err)

	mp := p.(*cloudprovider.MockCloudProviderProvisioner)

	// test when provisioner returns error directly. in this case, the state does not change.
	input.Err = fmt.Errorf("provisioner error")
	action := mp.On("Provision", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock provision err test", func(t *testing.T) {
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		//res, err := producer.SendAndWaitResult(taskqueue.TGTaskQueueName, task, 8)

		err := remoteProvisioner.Provision(context.Background(), clientId, workDir, config, func(resourceId string, doneErr error, output string) {
			log.Infof("Done callback: resourceId: %v, err: %v, output: %v", resourceId, err, output)
		})
		require.Error(t, err)
		require.Equal(t, "provisioner error", err.Error())

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateStart, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when doneError is returned. in this case, the state changes to provisionfailed
	input.Err = nil
	input.SleepTime = 0
	input.DoneErr = fmt.Errorf("tf error")
	action = mp.On("Provision", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock provision done error test", func(t *testing.T) {
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		// res, err := producer.SendAndWaitResult(taskqueue.TGTaskQueueName, task, 8)
		err := remoteProvisioner.Provision(context.Background(), clientId, "/tmp/workDir", config, func(resourceId string, doneErr error, output string) {
			log.Infof("Done callback: resourceId: %v, err: %v, output: %v", resourceId, doneErr, output)
			//require.Error(t, doneErr)
			//require.Equal(t, "tf error", doneErr.Error())
		})

		require.NoError(t, err)

		time.Sleep(350 * time.Millisecond)

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateProvisionFailed, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when provisioner are successful, send message to taskqueue directly so we can assert more fields
	input.Err = nil
	input.DoneErr = nil
	input.SleepTime = 500 * time.Microsecond
	action = mp.On("Provision", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock provision test", func(t *testing.T) {
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 8)
		if err != nil {
			t.Fatalf("Failed to send task: %s", err)
		}
		// assert the result
		require.Equal(t, task.Id, res.ReplyToId)
		require.Equal(t, task.Initiator, res.Target)
		//require.Equal(t, task.Target, res.Initiator)
		require.Equal(t, task.Type, res.Type)
		require.Equal(t, task.Command, res.Command)

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateProvisioning, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})

	action.Unset()

}

func testDelete(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	// send a AGW provision task so the server can provision a cloud provider
	// it is already mock provisioner factory in the server

	producer, err := taskqueue.NewMachineryTaskProducer(server.MachineryServer)
	if err != nil {
		t.Fatalf("Failed to create Machinery task producer: %s", err)
	}

	clientId := "cloudprovider1"

	task := &taskqueue.TGTask{
		Id:        "123",
		Initiator: id,
		Target:    "AGW",
		Type:      taskqueue.ControllerTaskTypeAGW,
		Command:   taskqueue.ControllerAGWCmdDeleteProvider,
		Params: map[string][]byte{
			"providerType":     []byte("aws"),
			"resourceId":       []byte(clientId),
			"workDir":          []byte("/tmp/workDir"),
			"secureConnection": []byte("false"),
		},
		Timestamp: time.Now(),
	}

	input := cloudprovider.ProvisionerInput{
		Err:       nil,
		SleepTime: 1 * time.Second,
		DoneErr:   nil,
	}

	p, err := server.ProvisionerFactory.GetProvisioner("aws")
	require.NoError(t, err)

	mp := p.(*cloudprovider.MockCloudProviderProvisioner)

	// test when provisioner returns error directly. in this case, the state does not change.
	input.Err = fmt.Errorf("delete error")
	action := mp.On("Delete", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock delete err test", func(t *testing.T) {
		log := logger.L()
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 30)

		require.Error(t, err)
		require.Nil(t, res)
		require.Equal(t, "delete error", err.Error())

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateStart, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when doneError is returned. in this case, the state changes to deletefailed
	input.Err = nil
	input.SleepTime = 0
	input.DoneErr = fmt.Errorf("tf error")
	action = mp.On("Delete", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock delete done error test", func(t *testing.T) {
		log := logger.L()
		log.Warnf("Sending task to trigger delete now..., task: %v", task)
		res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 30)
		if err != nil {
			t.Fatalf("Failed to send task: %s", err)
		}
		// assert the result
		require.Equal(t, task.Id, res.ReplyToId)
		require.Equal(t, task.Initiator, res.Target)
		//require.Equal(t, task.Target, res.Initiator)
		require.Equal(t, task.Type, res.Type)
		require.Equal(t, task.Command, res.Command)

		time.Sleep(300 * time.Millisecond)

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateDeleteFailed, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when provisioner are successful
	input.Err = nil
	input.DoneErr = nil
	input.SleepTime = 500 * time.Microsecond
	action = mp.On("Delete", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock delete test", func(t *testing.T) {
		log := logger.L()
		log.Warnf("Sending task to trigger delete now..., task: %v", task)
		res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 30)
		if err != nil {
			t.Fatalf("Failed to send task: %s", err)
		}
		// assert the result
		require.Equal(t, task.Id, res.ReplyToId)
		require.Equal(t, task.Initiator, res.Target)
		//require.Equal(t, task.Target, res.Initiator)
		require.Equal(t, task.Type, res.Type)
		require.Equal(t, task.Command, res.Command)

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateDeleting, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})

	action.Unset()

}

func testUpgrade(t *testing.T, server *Server, client pb.TGControlPlaneServiceClient) {
	log := logger.L()
	// register with a new agent
	log.Debugf("id is %v", id)

	// send a AGW provision task so the server can provision a cloud provider
	// it is already mock provisioner factory in the server

	producer, err := taskqueue.NewMachineryTaskProducer(server.MachineryServer)
	if err != nil {
		t.Fatalf("Failed to create Machinery task producer: %s", err)
	}

	clientId := "cloudprovider1"

	factory, err := cloudprovider.NewRemoteCloudProviderProvisionerFactory(server.MachineryServer, id)
	require.NoError(t, err)

	remoteProvisioner, err := factory.GetProvisioner("aws")
	remoteProvisioner.SetTimeout(30)

	require.NoError(t, err)

	// use remote prosioner to create provision task

	workDir := "/tmp"
	config := &map[string][]byte{}
	tfConfig := &awsCP.AWSTemplateData{
		VPCCIDR:         "10.0.0.0/16",
		Region:          "us-east-1",
		EKSName:         "EksName1",
		VPCOwnedByTG:    false,
		RoleARN:         "arn:aws:iam::123456789012:role/eksctl-eksname1-nodegroup-ng-NodeInstanceRole-1GZJ5ZQZQZQZQ",
		ImagePullSecret: "secret1",
		// Domain    : "",
	}

	helmInput := &helmcharts.HelmChartsTemplateData{
		EKSName:                         "EksName1",
		Region:                          "us-east-1",
		RoleARN:                         "arn:aws:iam::123456789012:role/eksctl-eksname1-nodegroup-ng-NodeInstanceRole-1GZJ5ZQZQZQZQ",
		ClusterAgentReplicaCount:        1,
		ClusterAgentImagePullPolicy:     "always",
		ClusterAgentImageTag:            "v2.1",
		AutoStartHandlerReplicaCount:    1,
		AutoStartHandlerImagePullPolicy: "always",
		AutoStartHandlerImageTag:        "v2.2",
		ImagePullSecret:                 "secret2",
		AutoStartToken:                  "token1",
		ControllerURL:                   "controllerurl1",
		ClusterAgentConfigB64:           "",
		TeleportToken:                   "teleporttoken1",
		Env:                             "",
		TGOperatorImageTag:              "v2.3",
		CloudProviderVersionNumber:      "1",
	}

	clusterAgentConfig := cagConf.Config{
		CloudProviderId: clientId,
		OrgId:           "org1",
		Token:           "AGWtoken1",
		AuthServerAddr:  "",
		GRPCServerAddr:  "AGWaddr1",
		ExporterAddr:    "",
		MaxMsgSize:      65536000,

		AuthServerTimeOut:    5,
		RetryCount:           5,
		RetryInterval:        5,
		HeartbeatInterval:    60,
		NonSecure:            false,
		UpdateTimeOut:        5,
		UpdateTicker:         5,
		MaxTransactionNum:    99,
		TransactionRateLimit: 99,
		MaintenanceLiveSpan:  99,
		K8SConfig: resConf.K8SConfig{
			Region:    "us-east-1",
			Endpoint:  "https://kubernetes.default.svc",
			Subdomain: "us-east-1.i.tgcloud-dev.com",
			CertARN:   "",
		},

		ClusterAgentImageTag:     "v2.1",
		AutoStartHandlerImageTag: "v2.2",
		TGOperatorImageTag:       "v2.3",
	}

	clusterAgentConfig.K8SConfig.Token = ""
	clusterAgentConfigYamlBytes, err := yaml.Marshal(clusterAgentConfig)
	if err != nil {
		log.Errorf("failed to marshal cluster agent config: %v", err)
	}
	require.NoError(t, err)

	clusterAgentConfigB64 := b64.StdEncoding.EncodeToString([]byte(clusterAgentConfigYamlBytes))
	helmInput.ClusterAgentConfigB64 = clusterAgentConfigB64

	bytes, err := json.Marshal(tfConfig)
	require.NoError(t, err)
	(*config)["tf"] = bytes

	bytes, err = json.Marshal(helmInput)
	require.NoError(t, err)
	(*config)["helm"] = bytes

	configBytes, err := json.Marshal(config)
	require.NoError(t, err)

	task := &taskqueue.TGTask{
		Id:        "123",
		Initiator: id,
		Target:    "AGW",
		Type:      taskqueue.ControllerTaskTypeAGW,
		Command:   taskqueue.ControllerAGWCmdUpgradeProvider,
		Params: map[string][]byte{
			"providerType": []byte("aws"),
			"resourceId":   []byte(clientId),
			"config":       configBytes,
			"workDir":      []byte("/tmp"),
		},
		Timestamp: time.Now(),
	}

	input := cloudprovider.ProvisionerInput{
		Err:       nil,
		SleepTime: 1 * time.Second,
		DoneErr:   nil,
	}

	log.Infof("ProvisionerFactory.GetProvisioner: %v", server.ProvisionerFactory)
	p, err := server.ProvisionerFactory.GetProvisioner("aws")
	log.Infof("ProvisionerFactory.GetProvisioner: %v", p)
	require.NoError(t, err)

	mp := p.(*cloudprovider.MockCloudProviderProvisioner)

	// test when provisioner returns error directly. in this case, the state does not change.
	input.Err = fmt.Errorf("upgrade error")
	action := mp.On("Upgrade", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock upgrade err test", func(t *testing.T) {
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		err := remoteProvisioner.Upgrade(context.Background(), clientId, workDir, config, func(resourceId string, doneErr error, output string) {
			log.Infof("Done callback: resourceId: %v, err: %v, output: %v", resourceId, err, output)
		})
		require.Error(t, err)
		require.Equal(t, "upgrade error", err.Error())

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateStart, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when doneError is returned. in this case, the state changes to provisionfailed
	input.Err = nil
	input.SleepTime = 0
	input.DoneErr = fmt.Errorf("tf error")
	action = mp.On("Upgrade", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock provision done error test", func(t *testing.T) {
		log := logger.L()
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		err := remoteProvisioner.Upgrade(context.Background(), clientId, "/tmp", config, func(resourceId string, doneErr error, output string) {
			log.Infof("Done callback: resourceId: %v, err: %v, output: %v", resourceId, doneErr, output)
		})

		require.NoError(t, err)

		time.Sleep(350 * time.Millisecond)

		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateUpgradeFailed, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})
	action.Unset()

	// test when upgrade are successful, send message to taskqueue directly so we can assert more fields
	input.Err = nil
	input.DoneErr = nil
	input.SleepTime = 500 * time.Microsecond
	action = mp.On("Upgrade", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&input)
	t.Run("Mock provision test", func(t *testing.T) {
		log := logger.L()
		log.Warnf("Sending task to trigger provision now..., task: %v", task)
		res, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 30)
		if err != nil {
			t.Fatalf("Failed to send task: %s", err)
		}
		// assert the result
		require.Equal(t, task.Id, res.ReplyToId)
		require.Equal(t, task.Initiator, res.Target)
		//require.Equal(t, task.Target, res.Initiator)
		require.Equal(t, task.Type, res.Type)
		require.Equal(t, task.Command, res.Command)

		time.Sleep(350 * time.Millisecond)
		// verify clientinfo state
		ci, ok := server.MyClients.Load(ClientMapKey{clientId: id, isMaint: false})
		require.True(t, ok)
		clientInfo := ci.(*ClientInfo)
		require.Equal(t, cp.StateUpgradeComplete, clientInfo.GetFSM().Current())

		mp.AssertExpectations(t)
	})

	action.Unset()
	// test cp is in StateDeleteFailed, upgrade should fail
	input.Err = nil
	input.DoneErr = nil
	input.SleepTime = 500 * time.Microsecond

	clienInfo := NewClientInfo(clientId, nil, "0.0", false, server.MachineryServer, "{}", "")
	clienInfo.GetFSM().SetState(cp.StateDeleteFailed)
	server.MyClients.Store(ClientMapKey{clientId: id, isMaint: false}, clienInfo)

	t.Run("Test upgrade StateDeleteFailed cp", func(t *testing.T) {
		log.Warnf("Sending task to trigger upgrade now..., task: %v", task)
		_, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 30)
		require.ErrorContains(t, err, "resource is in delete failed")
	})

}
