package server

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"

	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	pb "github.com/tigergraph/cloud-universe/proto"
	provisioner "github.com/tigergraph/cloud-universe/resource-manager/cloud_provider"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	"github.com/tigergraph/cloud-universe/utils/os/network"
	"github.com/tigergraph/cloud-universe/utils/safemap"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
	"google.golang.org/grpc"
)

// New creates a new server struct with the given credentials and config. It returns a gRPC server and a server object
func New(config Configuration) (*grpc.Server, *Server, error) {
	log := logger.L()
	var s *grpc.Server
	myServer := &Server{}

	// get host IP address and make it the ID to represent the server
	ips, err := network.GetHostIPAddresses()
	if err != nil {
		log.Fatal("new: unable to get host IP addresses: %v", err)
		return nil, nil, fmt.Errorf("new: unable to get host IP addresses: %w", err)
	}
	if len(ips) == 0 {
		log.Fatal("new: no IP addresses found")
		return nil, nil, fmt.Errorf("new: no IP addresses found")
	}
	myServer.Addr = ips[0]

	myServer.Config = config

	// initialize pending task map
	myServer.PendingTaskMap = safemap.NewSafeMap()
	if myServer.PendingTaskMap == nil {
		log.Fatal("new: unable to create pending task map")
	}

	// initialize provisioner factory
	myServer.ProvisionerFactory = provisioner.NewCloudProviderProvisionerFactory()

	myServer.NewTaskQueue()
	myServer.NewRdsCli()

	tgIAM, err := tgIAM.NewTGIAMService(
		config.TGIAMConfig.IAMService,
		config.TGIAMConfig.OrgService,
		&tgIAM.Auth0M2M{
			ClientID:      config.TGIAMConfig.ClientID,
			ClientSecret:  config.TGIAMConfig.ClientSecret,
			Auth0HostName: config.TGIAMConfig.Auth0HostName,
		})
	if err != nil {
		return nil, nil, err
	}
	myServer.TGIAM = tgIAM

	var opts []grpc.ServerOption

	if config.EnableGRPCLogging {
		//opts = zaplg.AddLogging(opts)
	}

	s = grpc.NewServer(opts...)

	pb.RegisterTGControlPlaneServiceServer(s, myServer)
	myServer.grpcServer = s
	// get from the controller initial list of cloud providers
	if !myServer.Config.SkipInitialLoading {
		cloudProviders, err := myServer.RetrieveInitialCloudProviderFromController(lmri.NewCtxWithReqId(context.Background()))
		if err != nil {
			log.Errorf("new: failed to retrieve initial cloud provider list: %v", err)
			panic("new: failed to retrieve initial cloud provider list")
		}
		log.Infof("new: initial cloud providers loading: %v", len(*cloudProviders))

		myServer.LoadClientInfos(cloudProviders)

	} else {
		log.Warn("AGW: skip initial loading of cloud providers from controller")
	}

	go func() {
		_ = http.ListenAndServe(config.ServerExporterAddr, nil)
	}()

	// start go routine to check stale cloud providers
	myServer.startStaleChecker()

	log.Infof("new: server created at %v", myServer.Addr)
	return s, myServer, nil
}

func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.grpcServer.Stop()
	}

	if (s.AgentGatewayConsumer != nil) && (s.MachineryServer != nil) {
		s.AgentGatewayConsumer.StopWorker()
	}
}

func (s *Server) RetrieveInitialCloudProviderFromController(ctx context.Context) (*[]cp.CloudProviderInfo, error) {
	log := logger.L().WithContext(ctx)
	cloudProviders := &[]cp.CloudProviderInfo{}

	task := &taskqueue.TGTask{
		Id:        uuid.New().String(),
		Initiator: s.Id(),
		Target:    "controller",
		Type:      taskqueue.AGWTaskTypeGeneral,
		Command:   taskqueue.AGWTaskGetCloudProviderList,
		Timestamp: time.Now(),
	}

	var response *taskqueue.TGTask

	const MaxRetry = 20

	for i := 0; i < MaxRetry; i++ {
		producer, err := taskqueue.NewMachineryTaskProducer(s.MachineryServer)
		if err != nil {
			log.Errorf("AGWTaskHandler: failed to create task producer %v. Unable to update CloudProvider meta", err)
			continue
		} else {
			response, err = producer.SendAndWaitResult(ctx, taskqueue.TGProviderStatusQueueName, task, AGWTaskTimeout)
			if err != nil {
				log.Errorf("AGWTaskHandler: failed to send task %v. Unable to retrieve CloudProvider meta", err)
			} else {
				if response != nil && response.Status != taskqueue.TaskStatusOk {
					log.Errorf("AGWTaskHandler: failed to retrieve CloudProvider meta: %v", response.Error)
				}
				if response.Status == taskqueue.TaskStatusOk {
					log.Infof("AGWTaskHandler: successfully retrieve CloudProvider list from controller")
					break

				}
			}
		}
		// sleep for a while before retry
		time.Sleep(time.Second * 3)
		log.Infof("AGWTaskHandler: retrying to retrieve CloudProvider meta from controller: %v", i)
	}

	// unmarshal the response and extract to cloud provider info
	if response != nil {
		err := json.Unmarshal(response.Params["CloudProviderInfo"], cloudProviders)
		if err != nil {
			log.Errorf("AGWTaskHandler: failed to unmarshal cloud provider resources: %v", err)
			return nil, err
		}
		return cloudProviders, nil
	} else {
		return nil, fmt.Errorf("failed to retrieve CloudProvider meta")
	}
}

func (s *Server) LoadClientInfos(infos *[]cp.CloudProviderInfo) int {
	log := logger.L()
	total := 0
	if infos == nil {
		log.Errorf("LoadClientInfos: nil cloud provider info")
		return total
	}
	for _, info := range *infos {
		clientId := info.CloudProviderId
		k := ClientMapKey{clientId: clientId, isMaint: false}
		_, ok := s.MyClients.Load(k)
		if !ok {
			// for deleting/provisioning/upgrading status, we reset it to active to indicate we have lost the actual status
			if info.Status == cp.StateDeleting || info.Status == cp.StateProvisioning || info.Status == cp.StateUpgrading {
				info.Status = cp.StateStart
			}

			// add the client to the store
			clientInfo := NewClientInfoFromCloudProviderInfo(&info, s.MachineryServer)
			s.MyClients.Store(k, clientInfo)
			total++
		} else {
			log.Errorf("LoadClientInfos: client %v already exists", clientId)
		}
	}
	log.Infof("LoadClientInfos: %v clients loaded", total)
	return total
}
