// Package server implements the functionalities of establishing a gRPC bi-directional stream,
// handling agent registrations, and updating agents.
package server

import (
	"context"
	"io"
	"os"
	"sync"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/aws/aws-sdk-go/service/ssm"
	"go.uber.org/atomic"

	"google.golang.org/grpc"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"

	"github.com/RichardKnop/machinery/v1"
	pb "github.com/tigergraph/cloud-universe/proto"
	config "github.com/tigergraph/cloud-universe/resource-manager/config"
	"github.com/tigergraph/cloud-universe/resource-manager/interfaces"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	tgredis "github.com/tigergraph/cloud-universe/utils/redis"
	"github.com/tigergraph/cloud-universe/utils/safemap"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

type ClientMetadata struct {
	id            string
	token         string
	version       string
	isMaintenance bool
}

type Configuration struct {
	HostingAddr        string
	ServerExporterAddr string
	QueueConfig        taskqueue.QueueConfig
	TGIAMConfig        config.TGIAMConfig
	ServerCertName     string
	ServerKeyName      string
	CACertName         string
	CAPrivateKeyName   string

	S3BucketName    string
	S3UploadTimeout int64 // seconds

	EnableGRPCLogging       bool
	UpdateTimeout           int
	InsecureSkipAuthServer  bool
	Region                  string
	PingInterval            int
	ProcessInterval         int
	MaxHeartbeatLostSeconds int

	// for testing
	SkipInitialLoading bool
	ValidateToken      bool
}

type AwsClients struct {
	SqsSvc       *sqs.SQS
	S3Svc        *s3.S3
	S3Downloader *s3manager.Downloader
	S3Uploader   *s3manager.Uploader
	SsmSvc       *ssm.SSM
	RdsSvc       tgredis.RedisClientInterface
}

type ClientMapKey struct {
	clientId string
	isMaint  bool
}

func PB2Task(pb *pb.AgentMessage) *taskqueue.TGTask {
	return &taskqueue.TGTask{
		Id:        pb.Id,
		Type:      pb.Type,
		Command:   pb.Command,
		Initiator: pb.Initiator,
		Target:    pb.Target,
		Params:    pb.Params,
		Timestamp: pb.GetTimestamp().AsTime(),
		ReplyToId: pb.ReplyToId,
		Status:    int(pb.Status),
		Error:     pb.Error,
	}
}

type Server struct {
	pb.UnimplementedTGControlPlaneServiceServer
	Addr string

	// two-way mapping of ClientMapKey struct and stream.
	MyClients           sync.Map
	MyClientsReflection sync.Map

	MyCert        []byte
	MyKey         []byte
	MySQSQueueUrl string
	Config        Configuration
	AwsClients    AwsClients

	// TaskQueue server
	MachineryServer        *machinery.Server
	AgentGatewayConsumer   *taskqueue.MachineryTaskWorker
	ProviderStatusProducer *taskqueue.MachineryTaskProducer

	// provisioner factory
	ProvisionerFactory interfaces.ProvisionerFactory

	// pending tasks map
	PendingTaskMap *safemap.SafeMap
	// grpc server reference
	grpcServer *grpc.Server

	TGIAM tgIAM.TGIAM
}

func NewClientMapKey(clientId string, isMaint bool) *ClientMapKey {
	return &ClientMapKey{clientId: clientId, isMaint: isMaint}
}

func init() {
	log := logger.L()
	rootDir := os.Getenv("PROJROOT")
	if rootDir == "" {
		log.Warn("client main: environment Variable PROJROOT not set.")
	}
}

func (s *Server) Id() string {
	return s.Addr
}
func (s *Server) PrivateQueueId() string {
	return "AG_" + s.Addr
}

// Comm implements the bidirectional streaming logic to receive messages from clients
func (s *Server) ControlAgentCommands(stream pb.TGControlPlaneService_ControlAgentCommandsServer) error {
	var wg sync.WaitGroup
	wg.Add(1)
	state := make(chan error, 1)
	heartbeatFailure := atomic.NewBool(false)

	ctx := stream.Context()
	log := logger.L()

	// Get IP address of client
	clientIPAddr := ""
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if xfwd := md.Get("x-forwarded-for"); len(xfwd) > 0 {
			clientIPAddr = xfwd[0]
			log.Infof("AgentGateway: [%v] client IP address from HTTP header: %v", s.Id(), clientIPAddr)
		}
	}

	if clientIPAddr == "" {
		p, _ := peer.FromContext(ctx)
		if p != nil {
			clientIPAddr = p.Addr.String()
			log.Infof("AgentGateway: [%v] failed to get client IP address from HTTP header, use IP address from peer: %v", s.Id(), clientIPAddr)
		}
	}

	go func() {
		defer wg.Done()
		for {
			if heartbeatFailure.Load() {
				state <- status.Errorf(codes.Unavailable, "AgentGateway: [%v] heartbeat failure", s.Id())
				break
			}

			req, err := stream.Recv()
			ctx := lmri.NewContextFromMessage(context.Background(), req)
			log := logger.L().WithContext(ctx)
			if err == io.EOF {
				log.Infof("AgentGateway: [%v] received EOF. No more agent connection request: [%v]", s.Id(), clientIPAddr)
				state <- nil
				break
			}

			if err != nil {
				state <- err
				break
			}

			// Read metadata from client
			md, ok := metadata.FromIncomingContext(stream.Context())
			if !ok {
				log.Warnf("AgentGateway: [%v] failed to get metadata from stream context: [%v]", s.Id(), clientIPAddr)
				state <- status.Errorf(codes.DataLoss, "AgentGateway: failed to get metadata")
				break
			}

			// Parse the metadata into ClientMetadata struct
			clientMetadata, err := s.GetMetadata(ctx, md)
			if err != nil {
				log.Warnf("AgentGateway: [%v] failed to parse metadata from [%v]. %v", s.Id(), clientIPAddr, err)
				state <- err
				break
			}

			log.Infof("AgentGateway: [%v] received message from client: [%v] with taskType:%s, replyToId:%s", s.Id(), clientIPAddr, req.GetType(), req.GetReplyToId())
			go func() {
				// req is the message sent from the client side.
				taskType := req.GetType()
				replyToId := req.GetReplyToId()

				switch taskType {
				case taskqueue.AgentTaskTypeHeartBeat:
					err := s.HandleHeartbeat(ctx, stream, clientMetadata, req)
					if err != nil {
						state <- err
						heartbeatFailure.Store(true)
						return
					}

				case taskqueue.ControllerTaskTypeGeneral:
					// if the task is a general task, it should be relayed to the agent, and now we got the response
					// try finding the task channel from PendingTaskMap
					taskChan, ok := s.PendingTaskMap.Get(replyToId)
					// log.Infof("AgentGateway: received [%v/%v], client id [%v], transaction id [%v], reply to id: [%v]", req.GetType(), req.GetCommand(), clientId, req.Id(), replyToId)
					if !ok {
						log.Errorf("AgentGateway: [%v] task reply to: %v not found in pending task map", s.Id(), replyToId)
						log.Info(req)
					} else {
						taskChan.(chan *pb.AgentMessage) <- req
					}

				default: // Switch case for additional endpoints
					log.Infof("AgentGateway: [%v] received unsupported task type: %v", s.Id(), taskType)
				}
			}()
		}
	}()

	// mechanism of server understanding client disconnected. If that happens, clean up cache and decrease client count
	go func() {
		log := logger.L()
		select {
		case <-ctx.Done():
			key, ok := s.MyClientsReflection.LoadAndDelete(stream)
			if ok {
				keyS := key.(ClientMapKey)
				ci, loaded := s.MyClients.Load(keyS)
				if loaded {
					log.Infof("agent %v [maintenance %v] has disconnected, mark as disconnected", keyS.clientId, keyS.isMaint)
					ci.(*ClientInfo).Disconnect()
				} else {
					log.Infof("on disconnecting: agent %v [maintenance %v] is not in map", keyS.clientId, keyS.isMaint)
				}

				// clean up cache
				s.removeAgentFromStore(keyS.clientId, keyS.isMaint)
				DecNumClient()
			}
			return
		}
	}()

	wg.Wait()

	return <-state
}

// PingClients sends ping message to all agents. If there is error when sending,
// it would consider the agent being unreachable so remove it from the map and shared cache.
func (s *Server) PingClients() {
	log := logger.L()
	logger.L().Infof("AgentGateway: [%v] PingClients: start pinging all agents", s.Id())

	s.MyClients.Range(func(key, value interface{}) bool {
		clientInfo := value.(*ClientInfo)
		clientId := key.(ClientMapKey)
		ctx := lmri.NewCtxWithReqId(context.Background())
		log := logger.L().WithContext(ctx)
		if clientInfo.GetNetState() != NetStateConnected {
			log.Infof("AgentGateway: [%v] PingClients: agent [%v] is not connected, skip it.", s.Id(), clientId)
		} else {
			stream := clientInfo.stream

			// check if the stream is nil
			if stream != nil {
				err := stream.Send(lmri.ProtoMessageReqIdSetter(ctx, &pb.ControlMessage{Type: taskqueue.ControllerTaskTypePing}).(*pb.ControlMessage))
				if err != nil {
					log.Warnf("AgentGateway: [%v] PingClients: can't ping agent [%v]. Consider it as disconnected.", s.Id(), clientId)
				} else {
					log.Infof("AgentGateway: [%v] PingClients: agent [%v] is still alive.", s.Id(), clientId)
				}
			}
		}
		return true
	})

	log.Infof("AgentGateway: [%v] PingClients: finish pinging all agents", s.Id())
}

// GetMetadata takes a metadata map and return a ClientMetadata struct with all metadata.
// Note:
// The value in the map is of type []string and this function expects to have the slice with exactly one element.
// Per gRPC, keys in a metadata map can only consist lower case letters.
func (s *Server) GetMetadata(ctx context.Context, md metadata.MD) (ClientMetadata, error) {
	log := logger.L().WithContext(ctx)
	id, ok := md["id"]
	if !ok || len(id) != 1 {
		log.Infof("AgentGateway: [%v] failed to get id", s.Id())
		return ClientMetadata{}, status.Errorf(codes.DataLoss, "AgentGateway: failed to get id")
	}

	token, ok := md["token"]
	if !ok || len(token) != 1 {
		log.Infof("AgentGateway: [%v] failed to get token", s.Id())
		return ClientMetadata{}, status.Errorf(codes.DataLoss, "AgentGateway: failed to get token")
	}
	version, ok := md["version"]
	if !ok || len(version) != 1 {
		log.Infof("AgentGateway: [%v] failed to get version", s.Id())
		return ClientMetadata{}, status.Errorf(codes.DataLoss, "AgentGateway: failed to get version")
	}
	tmp, ok := md["ismaint"]
	if !ok || len(tmp) != 1 {
		log.Infof("AgentGateway: [%v] failed to get isMaintenance", s.Id())
		return ClientMetadata{}, status.Errorf(codes.DataLoss, "AgentGateway: failed to get isMaintenance")
	}

	// parse boolean string to boolean type.
	isMaintenance := false
	if tmp[0] == "1" {
		isMaintenance = true
	}
	return ClientMetadata{id: id[0], token: token[0], version: version[0], isMaintenance: isMaintenance}, nil

}
