package server

import (
	"net/http"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc/codes"
)

var (
	clientCount              prometheus.Gauge
	registrationResponseTime prometheus.Gauge
	successCount             *prometheus.CounterVec
	failureCount             *prometheus.CounterVec
	requestCount             prometheus.Counter
	totalTasksCount          prometheus.Counter
	errorTasksCount          prometheus.Counter
	pendingTasksCount        prometheus.Gauge
	staleCheckCount          prometheus.Counter

	clientStateGauge *prometheus.GaugeVec

	stateGaugeMutex = &sync.Mutex{}
)

func init() {

	// Create the client count metric and set to zero initially
	pendingTasksCount = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "AgentGateway_num_pending_tasks",
		Help: "number of pending cp commands on this AgentGateway Service",
	})
	totalTasksCount = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "AgentGateway_num_total_tasks",
		Help: "number of total incoming cp commands on this AgentGateway Service",
	})

	errorTasksCount = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "AgentGateway_num_error_tasks",
		Help: "number of tasks that failed for all reasons on this AgentGateway Service",
	})

	clientCount = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "AgentGateway_num_clients",
		Help: "number of connected clients on this AgentGateway Service",
	})

	successCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "AgentGateway_success_count",
		Help: "count of successful registration requests from solutions (partitioned by instance id and IP Addresses)",
	}, []string{"clientID", "IPAddress"})

	failureCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "AgentGateway_failure_count",
		Help: "count of failed registration requests from solutions (partitioned by IP Addresses and reasons)",
	}, []string{"IPAddress", "reason"})

	requestCount = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "AgentGateway_request_count",
		Help: "number of requests through an edge",
	})

	// metrics to record registration response time
	registrationResponseTime = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "AgentGateway_registration_response_time",
		Help: "response time for registration time for each new client (in milliseconds)",
	})

	staleCheckCount = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "AgentGateway_num_stale_check_count",
		Help: "number of stale checker runs on this AgentGateway Service",
	})

	clientStateGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "AgentGateway_client_state",
		Help: "state of the client",
	}, []string{"client_id"})

	// Register the metrics
	prometheus.MustRegister(clientCount)
	prometheus.MustRegister(successCount)
	prometheus.MustRegister(failureCount)
	prometheus.MustRegister(registrationResponseTime)
	prometheus.MustRegister(requestCount)
	prometheus.MustRegister(pendingTasksCount)
	prometheus.MustRegister(totalTasksCount)
	prometheus.MustRegister(errorTasksCount)
	prometheus.MustRegister(staleCheckCount)
	prometheus.MustRegister(clientStateGauge)

	http.Handle("/metrics/agent_gateway", promhttp.Handler())

}

func IncPendingTasksCount() {
	pendingTasksCount.Inc()
}

func DecPendingTasksCount() {
	pendingTasksCount.Dec()
}

func IncTotalTasksCount() {
	totalTasksCount.Inc()
}
func IncErrorTasksCount() {
	errorTasksCount.Inc()
}

func IncNumClient() {
	clientCount.Inc()
}

func DecNumClient() {
	clientCount.Dec()
}

func IncSuccessCount(clientID string, IPAddr string) {
	successCount.With(prometheus.Labels{"clientID": clientID, "IPAddress": IPAddr}).Inc()
}

func IncNumRequest() {
	requestCount.Inc()
}

func IncFailureCount(IPAddr string, code codes.Code) {

	reason := "Unknown"

	// Set the error according to error codes
	switch code {
	case codes.InvalidArgument:
		reason = "Invalid instance id"
	case codes.Unauthenticated:
		reason = "Bad Token"
	case codes.AlreadyExists:
		reason = "Repeated Registration"
	case codes.Internal:
		reason = "Internal Error"
	}

	failureCount.With(prometheus.Labels{"IPAddress": IPAddr, "reason": reason}).Inc()
}

// ReportRegistrationResponseTime reports the time (in milliseconds) the server takes to register a client
func ReportRegistrationResponseTime(duration time.Duration) {
	registrationResponseTime.Set(float64(duration.Milliseconds()))
}

func IncStaleCheckCount() {
	staleCheckCount.Inc()
}

func SetClientStateGauge(clientID string, active bool) {
	stateGaugeMutex.Lock()
	defer stateGaugeMutex.Unlock()

	if active {
		clientStateGauge.With(prometheus.Labels{"client_id": clientID}).Set(1)
	} else {
		clientStateGauge.With(prometheus.Labels{"client_id": clientID}).Set(0)
	}
}

func DeleteClientStateMetrics(clientID string) {
	stateGaugeMutex.Lock()
	defer stateGaugeMutex.Unlock()

	clientStateGauge.Delete(prometheus.Labels{"client_id": clientID})
}
