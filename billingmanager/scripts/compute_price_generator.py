import argparse
import csv

# Parse command-line arguments
parser = argparse.ArgumentParser(description="Generate workspace pricing CSV.")
parser.add_argument('--dev_mode', action='store_true', help="Enable development mode")
args = parser.parse_args()

# Use the passed dev_mode flag
dev_mode = args.dev_mode

# Workspace types
workspace_types = [
    "TG-00", "TG-0", "TG-1", "TG-2", "TG-4", "TG-6", "TG-8", "TG-16",
    "TG-24", "TG-28", "TG-32", "TG-36", "TG-40", "TG-48", "TG-56", "TG-64",
    "TG-72", "TG-80", "TG-96", "TG-112", "TG-128", "TG-144", "TG-160", "TG-176", 
    "TG-192", "TG-224", "TG-256", "TG-288", "TG-320", "TG-352", "TG-384"
]

# Platforms and regions
platforms = ["aws"]
# Remove eu-west-3, eu-south-2, ca-central-1 and ap-northeast-2 from the list for now since the list is too long.
regions = [
    "us-east-1", "us-east-2", "us-west-2", "eu-central-1", "eu-west-1", "eu-west-2", 
    "eu-north-1", "sa-east-1", "ap-southeast-3", "ap-south-1",
    "ap-southeast-1", "ap-southeast-2", "ap-northeast-1"
]
# regions = [
#     "us-east-1", "us-east-2", "us-west-2", "eu-central-1", "eu-west-1", "eu-west-2", 
#     "eu-west-3", "eu-south-2", "eu-north-1", "sa-east-1", "ca-central-1", "ap-southeast-3",
#     "ap-south-1", "ap-northeast-2", "ap-southeast-1", "ap-southeast-2", "ap-northeast-1"
# ]

# Cloud providers and HA
cloud_providers = ["TGCloud", "BYOC"]
ha_options = ["enabled", "disabled"]

# Tier mapping for regions
region_tiers = {
    "us-east-1": 1, "us-east-2": 1, "us-west-2": 1,
    "eu-west-1": 2, "eu-west-2": 2, "eu-west-3": 2, "eu-south-2": 2, "eu-north-1": 2, "ca-central-1": 2, "ap-south-1": 2,
    "eu-central-1": 3, "ap-southeast-3": 3, "ap-northeast-2": 3, "ap-southeast-1": 3, "ap-southeast-2": 3, "ap-northeast-1": 3,
    "sa-east-1": 4
}

# Tier multipliers
tier_multipliers = {1: 1.0, 2: 1.125, 3: 1.25, 4: 1.375}

# Base price for TG-1
base_price_tg1 = 4

# Free units
free_units_included = 0

# Define data for the add-ons
addon_types = {
    "Insights": 0.10,  # 10% of the final price
}

# Region-specific costs
region_costs = {
    "us-east-1": 0.5292, "us-east-2": 0.5292, "us-west-2": 0.5292,
    "eu-central-1": 0.6384, "eu-west-1": 0.5922, "eu-west-2": 0.6216, "eu-west-3": 0.6216,
    "eu-south-2": 0.5922, "eu-north-1": 0.5628, "sa-east-1": 0.8442, "ca-central-1": 0.5796,
    "ap-southeast-3": 0.6384, "ap-south-1": 0.5460, "ap-northeast-2": 0.6384,
    "ap-southeast-1": 0.6384, "ap-southeast-2": 0.6342, "ap-northeast-1": 0.6384
}

# Storage cost per month for each region
storage_costs_monthly = {
    "us-east-1": 0.08, "us-east-2": 0.08, "us-west-2": 0.08,
    "eu-central-1": 0.0952, "eu-west-1": 0.088, "eu-west-2": 0.0928, "eu-west-3": 0.0928,
    "eu-south-2": 0.088, "eu-north-1": 0.0836, "sa-east-1": 0.1520, "ca-central-1": 0.088,
    "ap-southeast-3": 0.096, "ap-south-1": 0.0912, "ap-northeast-2": 0.0912,
    "ap-southeast-1": 0.096, "ap-southeast-2": 0.096, "ap-northeast-1": 0.096
}

# Add custom workspace types only in dev mode
if dev_mode:
    workspace_types.extend([
        "TG-0x2", "TG-0x3", 
        "TG-4x2"
    ])

# Function to calculate the rate
def calculate_rate(workspace_type, cloud_provider, ha, region):
    # Determine base price for custom workspace types
    if workspace_type == "TG-00":
        base_price = base_price_tg1 * 0.25
    elif workspace_type == "TG-0":
        base_price = base_price_tg1 * 0.5
    elif "TG-0x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        base_price = base_price_tg1 * 0.5 * multiplier
    elif "TG-4x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        base_price = base_price_tg1 * 4 * multiplier
    else:
        multiplier = int(workspace_type.split("-")[1]) if "-" in workspace_type else 1
        base_price = base_price_tg1 * multiplier

    # Cloud provider multiplier
    cloud_provider_multiplier = 1.0 if cloud_provider == "TGCloud" else 0.9

    # HA multiplier
    workspace_number = int(workspace_type.split("-")[1]) if workspace_type not in ["TG-00", "TG-0"] and "x" not in workspace_type else 0
    if ha == "enabled":
        ha_multiplier = 2.8 if workspace_number <= 8 else 1.95
    else:
        ha_multiplier = 1.0

    # Region tier multiplier (BYOC is always Tier 1)
    if cloud_provider == "BYOC":
        tier_multiplier = tier_multipliers[1]
    else:
        region_tier = region_tiers.get(region, 1)
        tier_multiplier = tier_multipliers[region_tier]

    # Final rate calculation
    rate = base_price * cloud_provider_multiplier * ha_multiplier * tier_multiplier
    return round(rate, 4)

# Convert monthly storage cost to hourly
def get_storage_cost(region, storage_size):
    storage_cost_per_month = storage_costs_monthly.get(region, 0)
    storage_cost_per_hour = (storage_cost_per_month / 720) * storage_size  # Assuming 720 hours in a month
    return round(storage_cost_per_hour, 4)

# Function to calculate the cost considering cloud provider, HA, and storage
def calculate_cost(workspace_type, region, cloud_provider, ha):
    if cloud_provider == "BYOC":
        return 0.0  # No infra cost for BYOC

    if workspace_type == "TG-00":
        base_price = region_costs[region] * 0.25
    elif workspace_type == "TG-0":
        base_price = region_costs[region] * 0.5
    elif "TG-0x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        base_price = region_costs[region] * 0.5 * multiplier
    elif "TG-4x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        base_price = region_costs[region] * 4 * multiplier
    else:
        multiplier = int(workspace_type.split("-")[1]) if "-" in workspace_type else 1
        base_price = region_costs[region] * multiplier

    # Region tier multiplier
    region_tier = region_tiers.get(region, 1)
    tier_multiplier = tier_multipliers[region_tier]

    # HA cost multiplier
    workspace_number = int(workspace_type.split("-")[1]) if workspace_type not in ["TG-00", "TG-0"] and "x" not in workspace_type else 0
    if ha == "enabled":
        ha_multiplier = 3.0 if workspace_number <= 8 else 2.0
    else:
        ha_multiplier = 1.0

    # Calculate storage cost
    storage_cost = get_storage_cost(region, 20)
    if "TG-0x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        storage_cost = storage_cost * multiplier
    elif "TG-4x" in workspace_type:
        multiplier = int(workspace_type.split("x")[1])
        storage_cost = storage_cost * multiplier
    else:
        size = int(workspace_type.split("-")[1]) if "-" in workspace_type else 1
        multiplier = round(size / 8)
        storage_cost = storage_cost * multiplier

    # Final cost calculation (compute cost + storage cost)
    cost = (base_price * tier_multiplier * ha_multiplier) + storage_cost
    return round(cost, 4)

# Calculate rate with add-ons using base Tier 1 compute rate
def calculate_rate_with_addons(workspace_type, addon):
    base_rate = calculate_rate(workspace_type, "TGCloud", "disabled", "us-east-1")
    addon_percentage = addon_types.get(addon, 0)
    addon_price = base_rate * addon_percentage
    return round(addon_price, 4)

# Generate CSV for compute usage
with open("compute_usage.csv", mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["WorkspaceType", "Platform", "Region", "CloudProvider", "HA", "Rate Per Unit", "Free Units Included"])

    for workspace_type in workspace_types:
        for platform in platforms:
            for region in regions:
                for cloud_provider in cloud_providers:
                    for ha in ha_options:
                        rate_per_unit = calculate_rate(workspace_type, cloud_provider, ha, region)
                        writer.writerow([workspace_type, platform, region, cloud_provider, ha, rate_per_unit, free_units_included])

# Generate CSV for compute usage cost
with open("compute_usage_cost.csv", mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["WorkspaceType", "Platform", "Region", "CloudProvider", "HA", "Rate Per Unit", "Free Units Included"])

    for workspace_type in workspace_types:
        for platform in platforms:
            for region in regions:
                for cloud_provider in cloud_providers:
                    for ha in ha_options:
                        cost_per_unit = calculate_cost(workspace_type, region, cloud_provider, ha)
                        writer.writerow([workspace_type, platform, region, cloud_provider, ha, cost_per_unit, free_units_included])

# Generate CSV for add-ons pricing
with open("add_on.csv", mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["Type", "WorkspaceType", "Platform", "Region", "CloudProvider", "Rate Per Unit", "Free Units Included"])

    for workspace_type in workspace_types:
        for platform in platforms:
            for region in regions:
                for cloud_provider in cloud_providers:
                    for addon in addon_types:
                        rate_per_unit = calculate_rate_with_addons(workspace_type, addon)
                        writer.writerow([addon, workspace_type, platform, region, cloud_provider, rate_per_unit, free_units_included])

print("CSV files 'compute_usage.csv', 'compute_usage_cost.csv', and 'add_on.csv' generated successfully.")
