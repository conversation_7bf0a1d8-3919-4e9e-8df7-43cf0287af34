import csv

# Platforms and regions
platforms = ["aws"]
# Remove eu-west-3, eu-south-2, ca-central-1 and ap-northeast-2 from the list for now since the list is too long.
regions = [
    "us-east-1", "us-east-2", "us-west-2", "eu-central-1", "eu-west-1", "eu-west-2", 
    "eu-north-1", "sa-east-1", "ap-southeast-3", "ap-south-1",
    "ap-southeast-1", "ap-southeast-2", "ap-northeast-1"
]
# regions = [
#     "us-east-1", "us-east-2", "us-west-2", "eu-central-1", "eu-west-1", "eu-west-2", 
#     "eu-west-3", "eu-south-2", "eu-north-1", "sa-east-1", "ca-central-1", "ap-southeast-3",
#     "ap-south-1", "ap-northeast-2", "ap-southeast-1", "ap-southeast-2", "ap-northeast-1"
# ]

# Region-specific costs
region_costs = {
    "us-east-1": 0.023, "us-east-2": 0.023, "us-west-2": 0.023,
    "eu-central-1": 0.245, "eu-west-1": 0.023, "eu-west-2": 0.024, "eu-west-3": 0.024,
    "eu-south-2": 0.023, "eu-north-1": 0.023, "sa-east-1": 0.0405, "ca-central-1": 0.025,
    "ap-southeast-3": 0.025, "ap-south-1": 0.025, "ap-northeast-2": 0.025,
    "ap-southeast-1": 0.025, "ap-southeast-2": 0.025, "ap-northeast-1": 0.025
}

# Tier mapping for regions
region_tiers = {
    "us-east-1": 1, "us-east-2": 1, "us-west-2": 1,
    "eu-west-1": 2, "eu-west-2": 2, "eu-west-3": 2, "eu-south-2": 2, "eu-north-1": 2, "ca-central-1": 2, "ap-south-1": 2,
    "eu-central-1": 3, "ap-southeast-3": 3, "ap-northeast-2": 3, "ap-southeast-1": 3, "ap-southeast-2": 3, "ap-northeast-1": 3,
    "sa-east-1": 4
}

# Tier multipliers
tier_multipliers = {1: 1.0, 2: 1.085, 3: 1.13, 4: 1.75}

# Base price set to 0.025
base_price = 0.025

# Free units
free_units_included = 0

# Function to calculate the rate
def calculate_rate(region):
    # Region tier multiplier
    region_tier = region_tiers.get(region, 1)
    tier_multiplier = tier_multipliers[region_tier]

    # Final rate calculation
    rate = base_price * tier_multiplier
    return round(rate, 4)

# Function to calculate the cost
def calculate_cost(region):
    # Lookup cost based on region
    cost = region_costs.get(region, 0.025)  # Default to base price if region is not found
    return round(cost, 4)

# Generate CSV
with open("data_storage.csv", mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["Platform", "Region", "Rate Per Unit", "Free Units Included"])

    for platform in platforms:
        for region in regions:
            rate_per_unit = calculate_rate(region)
            writer.writerow([platform, region, rate_per_unit, free_units_included])

# Generate CSV for costs
with open("data_storage_cost.csv", mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["Platform", "Region", "Cost Per Unit", "Free Units Included"])

    for platform in platforms:
        for region in regions:
            cost_per_unit = calculate_cost(region)
            writer.writerow([platform, region, cost_per_unit, free_units_included])

print("CSV file 'data_storage.csv' and 'data_storage_cost.csv' generated successfully.")
