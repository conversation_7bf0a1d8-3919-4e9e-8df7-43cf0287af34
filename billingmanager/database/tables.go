package database

import (
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"gorm.io/gorm"
)

type UUIDModel struct {
	ID        *uuid.UUID `gorm:"column:id;primarykey;type:uuid;default:uuid_generate_v4()"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

type CustomerInfo struct {
	UUIDModel
	// CustomerID is OrgID from IAM.
	CustomerID           string `gorm:"uniqueIndex:unique_customer_info_customer_id,where:deleted_at IS NULL"`
	CustomerName         string
	Emails               string
	StripeID             string
	SuspendGracePeriod   int
	TerminateGracePeriod int `gorm:"default:7"`
	HasValidCard         bool
	// Indicates the time customer last received a warning email regarding resource suspension or termination.
	ReceivedWarningEmail *time.Time
	// Marks when customer was first determined to be delinquent based on invalid card, unpaid invoice, or depleted credit balance.
	DelinquentSince *time.Time
}

type AWSMarketplaceInfo struct {
	UUIDModel
	// CustomerID is OrgID from IAM.
	CustomerID string `gorm:"uniqueIndex:unique_marketplace_id_customer_id,where:deleted_at IS NULL"`
	// MarketplaceID is AWS Customer Identifier.
	MarketplaceID    string `gorm:"uniqueIndex:unique_marketplace_id_customer_id,where:deleted_at IS NULL"`
	ProductCode      string
	Status           constants.MarketplaceStatus
	StatusUpdateTime *time.Time
}
