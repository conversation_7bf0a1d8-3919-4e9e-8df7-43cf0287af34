package database

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	tgLogger "github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/prometheus"
)

type Database struct {
	db tggorm.IGorm
}

func NewBillingManagerDatabase(dsn string) (BillingManagerDB, error) {
	sqlDB, err := sql.Open("pgx", dsn)
	if err != nil {
		return nil, err
	}

	db, err := gorm.Open(postgres.New(postgres.Config{
		Conn: sqlDB,
	}), &gorm.Config{
		Logger: tgLogger.L().GormWrapper().LogMode(logger.Info),
	})

	if err != nil {
		return nil, err
	}

	gormiface := tggorm.NewDB(db)

	gormiface.Session(&gorm.Session{Logger: logger.Discard}).Use(prometheus.New(prometheus.Config{
		DBName: "billing_manager",
		MetricsCollector: []prometheus.MetricsCollector{
			&prometheus.Postgres{},
		},
	}))
	if err = gormiface.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error(); err != nil {
		return nil, err
	}

	err = gormiface.AutoMigrate(
		&CustomerInfo{},
		&AWSMarketplaceInfo{},
	)
	if err != nil {
		return nil, err
	}

	return &Database{db: gormiface}, nil
}

func (DB *Database) CreateCustomerInfo(customerID string, email string, name string, stripeID string) error {
	db := DB.db

	CustomerInfoTable := &CustomerInfo{
		CustomerID:   customerID,
		CustomerName: name,
		Emails:       email,
		StripeID:     stripeID,
	}

	if err := db.Create(CustomerInfoTable).Error(); err != nil {
		return err
	}

	return nil
}

func (DB *Database) UpdateCustomerInfoName(customerID string, newCustomerName string) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	customerInfo.CustomerName = newCustomerName
	return db.Save(&customerInfo).Error()
}

// MarkCustomerDelinquent marks a customer as delinquent, by  setting 'DelinquentSince' field to current time.
// If field is already set, it will return the existing value.
func (DB *Database) MarkCustomerDelinquent(customerID string) (*time.Time, error) {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return nil, err
	}

	// If customer has already been marked delinquent, keep the older value and return.
	if customerInfo.DelinquentSince != nil {
		return customerInfo.DelinquentSince, nil
	}

	now := time.Now()
	customerInfo.DelinquentSince = &now
	err = db.Save(&customerInfo).Error()
	if err != nil {
		return nil, err
	}

	return customerInfo.DelinquentSince, nil
}

func (DB *Database) UnmarkCustomerDelinquent(customerID string) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	customerInfo.DelinquentSince = nil
	return db.Save(&customerInfo).Error()
}

func (DB *Database) MarkReceivedWarningEmail(customerID string) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	now := time.Now()
	customerInfo.ReceivedWarningEmail = &now
	return db.Save(&customerInfo).Error()
}

func (DB *Database) UpdateCustomerSuspendGracePeriod(customerID string, gracePeriod int) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	customerInfo.SuspendGracePeriod = gracePeriod
	return db.Save(&customerInfo).Error()
}

func (DB *Database) UpdateCustomerTerminateGracePeriod(customerID string, gracePeriod int) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	customerInfo.TerminateGracePeriod = gracePeriod
	return db.Save(&customerInfo).Error()
}

func (DB *Database) GetCustomerInfo(customerID string) (*CustomerInfo, error) {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if err != nil {
		return nil, err
	}

	return &customerInfo, nil
}

// GetAllCustomerInfos returns all existing CustomerInfo records.
func (DB *Database) GetAllCustomerInfos() ([]CustomerInfo, error) {
	db := DB.db
	var customerInfos []CustomerInfo
	if err := db.Find(&customerInfos).Error(); err != nil {
		return nil, err
	}

	return customerInfos, nil
}

func (DB *Database) UpdateHasValidCard(customerID string, hasValidCard bool) error {
	db := DB.db
	var customerInfo CustomerInfo

	err := db.First(&customerInfo, CustomerInfo{CustomerID: customerID}).Error()
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("the customer [id=%v] does not exist: %w", customerID, err)
	} else if err != nil {
		return err
	}

	customerInfo.HasValidCard = hasValidCard
	return db.Save(&customerInfo).Error()
}

func (DB *Database) GetAWSMarketplaceInfoByCustomerID(customerID string) (*AWSMarketplaceInfo, error) {
	db := DB.db
	var marketplaceInfo AWSMarketplaceInfo

	err := db.First(&marketplaceInfo, AWSMarketplaceInfo{CustomerID: customerID}).Error()
	if err != nil {
		return nil, err
	}

	return &marketplaceInfo, nil
}

func (DB *Database) GetAWSMarketplaceInfoByMarketplaceID(marketplaceID string) (*AWSMarketplaceInfo, error) {
	db := DB.db
	var marketplaceInfo AWSMarketplaceInfo

	err := db.First(&marketplaceInfo, AWSMarketplaceInfo{MarketplaceID: marketplaceID}).Error()
	if err != nil {
		return nil, err
	}

	return &marketplaceInfo, nil
}

func (DB *Database) CreateAWSMarketplaceInfo(customerID string, marketplaceID string, productCode string) error {
	db := DB.db

	marketplaceInfo := &AWSMarketplaceInfo{
		CustomerID:    customerID,
		MarketplaceID: marketplaceID,
		ProductCode:   productCode,
	}

	if err := db.Create(marketplaceInfo).Error(); err != nil {
		return err
	}

	return nil
}

func (DB *Database) UpdateAWSMarketplaceInfo(info *AWSMarketplaceInfo) error {
	db := DB.db

	return db.Save(info).Error()
}

func (DB *Database) DeleteAWSMarketplaceInfo(id *uuid.UUID) error {
	db := DB.db
	if err := db.Delete(&AWSMarketplaceInfo{}, id).Error(); err != nil {
		return err
	}

	return nil
}
