package database

import (
	"time"

	"github.com/google/uuid"
)

type BillingManagerDB interface {
	CreateCustomerInfo(customerID string, email string, name string, stripeID string) error
	GetCustomerInfo(customerID string) (*CustomerInfo, error)
	GetAllCustomerInfos() ([]CustomerInfo, error)
	UpdateCustomerInfoName(customerID string, newCustomerName string) error
	MarkCustomerDelinquent(customerID string) (*time.Time, error)
	MarkReceivedWarningEmail(customerID string) error
	UnmarkCustomerDelinquent(customerID string) error
	UpdateCustomerSuspendGracePeriod(customerID string, gracePeriod int) error
	UpdateCustomerTerminateGracePeriod(customerID string, gracePeriod int) error
	UpdateHasValidCard(customerID string, hasValidCard bool) error

	// AWS Marketplace
	GetAWSMarketplaceInfoByCustomerID(customerID string) (*AWSMarketplaceInfo, error)
	GetAWSMarketplaceInfoByMarketplaceID(marketplaceID string) (*AWSMarketplaceInfo, error)
	CreateAWSMarketplaceInfo(customerID string, marketplaceID string, productCode string) error
	UpdateAWSMarketplaceInfo(info *AWSMarketplaceInfo) error
	DeleteAWSMarketplaceInfo(id *uuid.UUID) error
}
