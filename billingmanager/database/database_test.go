package database

import (
	"fmt"
	"os/exec"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
)

const (
	dsnTmpl = "postgres://cloud:cloud@localhost:%v/solution?sslmode=disable"
	// A random dynamic port to launch postgres
	port = 42387
)

type BillingManagerDBTestSuite struct {
	suite.Suite
	DB BillingManagerDB
}

func (suite *BillingManagerDBTestSuite) SetupTest() {
	// Run docker to provision postgres.
	startPGCMD := exec.Command(
		"docker", "run",
		"--rm",
		"--name", "resourcedb",
		"-e", "POSTGRES_PASSWORD=cloud",
		"-e", "POSTGRES_USER=cloud",
		"-e", "POSTGRES_DB=solution",
		"-p", "42387:5432",
		"-d",
		"postgres:11.22-bookworm",
	)
	err := startPGCMD.Run()
	if err != nil {
		out, _ := startPGCMD.CombinedOutput()
		fmt.Printf("err: %v, output: %v, command: %s", err, string(out), startPGCMD.String())
		return
	}

	// Wait for postgres to be ready.
	retry := 0
	maxRetry := 3
	for ; retry < maxRetry; retry++ {
		dsn := fmt.Sprintf(dsnTmpl, port)
		_, err := NewBillingManagerDatabase(dsn)
		if err == nil {
			break
		}
		time.Sleep(5 * time.Second)
	}

	if retry >= maxRetry {
		fmt.Printf("expect postgres to be ready within %v retries, tried: %v", maxRetry, retry)
		return
	}
}

func (suite *BillingManagerDBTestSuite) TearDownTest() {
	stopPGCMD := exec.Command("docker", "stop", "resourcedb")
	stopPGCMD.Run()
}

func (suite *BillingManagerDBTestSuite) ConnectDB(dsn string) {
	resourceDBConn, err := NewBillingManagerDatabase(dsn)
	if err != nil {
		panic(err.Error())
	}
	suite.DB = resourceDBConn
}

func TestResourceDatabaseSuite(t *testing.T) {
	dsnWithPort := fmt.Sprintf(dsnTmpl, port)

	dbSuite := &BillingManagerDBTestSuite{}
	dbSuite.SetupTest()
	dbSuite.ConnectDB(dsnWithPort)

	suite.Run(t, dbSuite)
}

func (suite *BillingManagerDBTestSuite) TestCreateGetCustomerInfo() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.CreateCustomerInfo("test-id-2", "<EMAIL>", "test-name-2", "test-stripe-id-2")
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.Equal("test-id", customerInfo.CustomerID)
	require.Equal(0, customerInfo.SuspendGracePeriod)
	require.Equal(7, customerInfo.TerminateGracePeriod)
	require.NoError(err)

	customerInfos, err := suite.DB.GetAllCustomerInfos()
	require.NoError(err)
	require.Equal(2, len(customerInfos))
}

func (suite *BillingManagerDBTestSuite) TestUpdateCustomerInfoName() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.UpdateCustomerInfoName("test-id", "new-name")
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.Equal("new-name", customerInfo.CustomerName)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestMarkUnmarkCustomerDelinquent() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	_, err = suite.DB.MarkCustomerDelinquent("test-id")
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.NoError(err)
	require.NotNil(customerInfo.DelinquentSince)

	err = suite.DB.UnmarkCustomerDelinquent("test-id")
	require.NoError(err)

	customerInfo, err = suite.DB.GetCustomerInfo("test-id")
	require.NoError(err)
	require.Nil(customerInfo.DelinquentSince)
}

func (suite *BillingManagerDBTestSuite) TestMarkReceivedWarningEmail() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.MarkReceivedWarningEmail("test-id")
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.NoError(err)
	require.NotNil(customerInfo.ReceivedWarningEmail)
}

func (suite *BillingManagerDBTestSuite) TestUpdateCustomerSuspendedGracePeriod() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.UpdateCustomerSuspendGracePeriod("test-id", 30)
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.Equal(30, customerInfo.SuspendGracePeriod)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestUpdateCustomerTerminateGracePeriod() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.UpdateCustomerTerminateGracePeriod("test-id", 30)
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.Equal(30, customerInfo.TerminateGracePeriod)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestUpdateHasValidCard() {
	require := suite.Require()

	err := suite.DB.CreateCustomerInfo("test-id", "<EMAIL>", "test-name", "test-stripe-id")
	require.NoError(err)

	err = suite.DB.UpdateHasValidCard("test-id", true)
	require.NoError(err)

	customerInfo, err := suite.DB.GetCustomerInfo("test-id")
	require.Equal(true, customerInfo.HasValidCard)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestCreateGetAWSMarketplaceInfo() {
	require := suite.Require()

	err := suite.DB.CreateAWSMarketplaceInfo("test-id", "marketplace-id", "product-code")
	require.NoError(err)

	err = suite.DB.CreateAWSMarketplaceInfo("test-id-2", "marketplace-id-2", "product-code-2")
	require.NoError(err)

	marketplaceInfo, err := suite.DB.GetAWSMarketplaceInfoByCustomerID("test-id")
	require.Equal("test-id", marketplaceInfo.CustomerID)
	require.Equal("marketplace-id", marketplaceInfo.MarketplaceID)
	require.Equal("product-code", marketplaceInfo.ProductCode)
	require.NoError(err)

	marketplaceInfo, err = suite.DB.GetAWSMarketplaceInfoByMarketplaceID("marketplace-id-2")
	require.Equal("test-id-2", marketplaceInfo.CustomerID)
	require.Equal("marketplace-id-2", marketplaceInfo.MarketplaceID)
	require.Equal("product-code-2", marketplaceInfo.ProductCode)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestUpdateAWSMarketplaceInfo() {
	require := suite.Require()
	err := suite.DB.CreateAWSMarketplaceInfo("test-id", "marketplace-id", "product-code")
	require.NoError(err)

	marketplaceInfo, err := suite.DB.GetAWSMarketplaceInfoByCustomerID("test-id")
	require.NoError(err)

	marketplaceInfo.MarketplaceID = "marketplace-id-2"
	err = suite.DB.UpdateAWSMarketplaceInfo(marketplaceInfo)
	require.NoError(err)

	marketplaceInfo, err = suite.DB.GetAWSMarketplaceInfoByCustomerID("test-id")
	require.Equal("test-id", marketplaceInfo.CustomerID)
	require.Equal("marketplace-id-2", marketplaceInfo.MarketplaceID)
	require.Equal("product-code", marketplaceInfo.ProductCode)
	require.NoError(err)
}

func (suite *BillingManagerDBTestSuite) TestDeleteAWSMarketPlaceInfo() {
	require := suite.Require()
	err := suite.DB.CreateAWSMarketplaceInfo("test-id", "marketplace-id", "product-code")
	require.NoError(err)

	marketplaceInfo, err := suite.DB.GetAWSMarketplaceInfoByCustomerID("test-id")
	require.NoError(err)

	err = suite.DB.DeleteAWSMarketplaceInfo(marketplaceInfo.ID)
	require.NoError(err)

	_, err = suite.DB.GetAWSMarketplaceInfoByCustomerID("test-id")
	require.Error(err)
}
