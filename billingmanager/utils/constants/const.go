package constants

// Payment Method API types.
type PaymentMethod string

const (
	PaymentMethodStripe         PaymentMethod = "STRIPE"
	PaymentMethodAWSMarketplace PaymentMethod = "AWS_MARKETPLACE"
)

func ValidatePaymentMethodFromString(paymentMethod string) (p PaymentMethod, ok bool) {
	switch paymentMethod {
	case string(PaymentMethodStripe):
		return PaymentMethodStripe, true
	case string(PaymentMethodAWSMarketplace):
		return PaymentMethodAWSMarketplace, true
	default:
		return "", false
	}
}

const AuditServiceName = "Billing Management"

// Audit Log Actions.
const (
	ActionAddMarketplaceSubscription    = "AddMarketplaceSubscription"
	ActionRemoveMarketplaceSubscription = "RemoveMarketplaceSubscription"
)
