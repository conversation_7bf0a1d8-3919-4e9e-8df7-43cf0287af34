package main

import (
	"flag"
	"log"

	svc "github.com/tigergraph/cloud-universe/billingmanager/utils/scripts/discountrefreshfix/service"
	"github.com/tigergraph/cloud-universe/controller"
	"github.com/tigergraph/cloud-universe/utils/os/file"
)

type flags struct {
	LogOutputDir   string
	ConfigPath     string
	Amberflo<PERSON>I<PERSON>ey string
}

// Usage: go run main.go -logdir="path" -apikey="key"
// Script to fix current Amberflo discount refresh issue.
// See ticket TCE-6291 for more  details.
// Script steps:
// 1. Iterate and remove all existing discounts from customers.
// 2. Retrieve list of `completed_tasks` record from the DB.
// 3. Apply new onboarding tasks to customers based on the `completed_tasks`.
func main() {
	flg := flags{}
	flag.StringVar(&flg.ConfigPath, "config", "../../../../config", "the path of config file")
	flag.StringVar(&flg.LogOutputDir, "logdir", "", "the directory to save log output")
	flag.StringVar(&flg.<PERSON>lo<PERSON>ey, "apikey", "", "the Amberflo API key")
	flag.Parse()

	if flg.LogOutputDir == "" {
		log.Fatal("You have to provide logdir in order to continue.")
	}

	if flg.AmberfloAPIKey == "" {
		log.Fatal("You have to provide Amberflo API key in order to continue.")
	}

	if !file.Exists(flg.ConfigPath) {
		log.Fatal("Config file does not exist.")
	}

	cfg := &controller.Config{}
	if err := cfg.Load(flg.ConfigPath); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	svc, err := svc.New(flg.LogOutputDir, flg.AmberfloAPIKey, cfg)
	if err != nil {
		log.Fatalf("Failed to start service: %v", err)
	}

	svc.ReapplyDiscounts()
}
