package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tigergraph/cloud-universe/billingmanager/service"
	"github.com/tigergraph/cloud-universe/controller"
	"github.com/tigergraph/cloud-universe/controller/database"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const amberfloURL = "https://app.amberflo.io/"

type Service struct {
	LogOutputDir   string
	db             *database.Database
	AmberfloAPIKey string
}

func New(logOutputDir, amberfloAPIKey string, cfg *controller.Config) (*Service, error) {
	dbCfg := fmt.Sprintf(
		"host=%v port=%v user=%v password=%v dbname=%v sslmode=%v",
		cfg.Controller.DB.Host,
		cfg.Controller.DB.Port,
		cfg.Controller.DB.Username,
		cfg.Controller.DB.Password,
		cfg.Controller.DB.Name,
		cfg.Controller.DB.SSLMode,
	)

	db, err := database.OpenDB("postgres", dbCfg, false)
	if err != nil {
		return nil, fmt.Errorf("Failed to connect to db: %v", err)
	}

	if err = os.MkdirAll(logOutputDir, 0755); err != nil {
		return nil, fmt.Errorf("Failed to create log output dir: %v", err)
	}

	return &Service{
		LogOutputDir:   logOutputDir,
		db:             db,
		AmberfloAPIKey: amberfloAPIKey,
	}, nil
}

func (svc *Service) GetLatestInvoice(ctx context.Context, customerID string, cache bool) (*service.Invoice, error) {
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice"),
		Headers: map[string]string{"x-api-key": svc.AmberfloAPIKey},
		QueryParams: map[string]string{
			"customerId": customerID,
			"productId":  "1",
			"fromCache":  strconv.FormatBool(cache),
			"latest":     "true",
		},
	}

	body, err := rest.Get(&reqInput)
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve customer(%s) latest invoice: %v", customerID, err)
	}

	invoice := &service.Invoice{}
	if err = json.Unmarshal(body, invoice); err != nil {
		return nil, err
	}

	return invoice, nil
}

func (svc *Service) ReapplyDiscounts() {
	logger := NewEventsLogger()
	amberfloAPIKey := svc.AmberfloAPIKey

	// Retrieve list of customers from Amberflo.
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "customers"),
		Headers: map[string]string{"x-api-key": amberfloAPIKey},
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		logger.AddError(fmt.Sprintf("Failed to retrieve Amberflo customers: %v", err))
		return
	}

	customerIDs := gjson.Get(string(resp), "#.id").Array()
	logger.AddEvent(fmt.Sprintf("Customers found: %d", len(customerIDs)))

	// Retrieve list of discounts from Amberflo.
	reqInput = rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/promotions/list"),
		Headers: map[string]string{"x-api-key": amberfloAPIKey},
	}

	resp, err = rest.Get(&reqInput)
	if err != nil {
		logger.AddError(fmt.Sprintf("failed to get discount list: %v", err))
		return
	}

	discountList := gjson.Get(string(resp), "#.{id,description,lockingStatus}")

	// Iterate through existing customers.
	for _, customerID := range customerIDs {
		fmt.Printf("Updating customer: %s\n", customerID.String())

		// Retrieve list of applied promotions for the customer.
		reqInput := rest.RequestInput{
			URL:         fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-promotions/list"),
			Headers:     map[string]string{"x-api-key": amberfloAPIKey},
			QueryParams: map[string]string{"CustomerId": customerID.String()},
		}

		resp, err := rest.Get(&reqInput)
		if err != nil {
			logger.AddError(fmt.Sprintf("Failed to retrieve list of discounts for customer %s: %f\n", customerID.String(), err))
			continue
		}

		promotionIDs := gjson.Get(string(resp), "#.{id,appliedTimeRange.endTimeInSeconds}")
		// Iterate and remove all existing discounts from the customer.
		for _, promotionID := range promotionIDs.Array() {
			// Don't remove discount with expiration date.
			if gjson.Get(promotionID.String(), "endTimeInSeconds").Value() != nil {
				continue
			}

			reqInput := rest.RequestInput{
				URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-promotions"),
				Headers: map[string]string{"x-api-key": amberfloAPIKey},
				QueryParams: map[string]string{
					"Id":         gjson.Get(promotionID.String(), "id").String(),
					"CustomerId": customerID.String(),
				},
			}

			_, err := rest.Delete(&reqInput)
			if err != nil {
				logger.AddError(fmt.Sprintf("Failed to delete discount %s for customer %s: %f\n", promotionID.String(), customerID.String(), err))
				continue
			}
		}

		// Retrieve list of current completed onboarding tasks for the customer.
		onboardingTasks, err := svc.db.GetTasksWithCompletedStatus(context.Background(), customerID.String())
		if err != nil {
			logger.AddError(fmt.Sprintf("Failed to retrieve onboarding tasks for customer %s: %v", customerID.String(), err))
			continue
		}

		for _, task := range onboardingTasks {
			// Apply new onboarding task to the customer.
			if task.Completed {
				// Find corresponding discount ID for the onboarding task.
				var discountID string
				for _, id := range discountList.Array() {
					if gjson.Get(id.String(), "lockingStatus").String() == "deprecated" {
						continue
					}

					if gjson.Get(id.String(), "description").String() == task.ID.String() {
						discountID = gjson.Get(id.String(), "id").String()
					}
				}

				if discountID == "" {
					logger.AddError(fmt.Sprintf("Discount not found for onboarding task ID: %s", task.ID.String()))
					continue
				}

				// Retrieve latest invoice for the customer.
				invoice, err := svc.GetLatestInvoice(context.Background(), customerID.String(), true)
				if err != nil {
					logger.AddError(fmt.Sprintf("Failed to get latest invoice for customer %s: %v", customerID.String(), err))
					continue
				}

				// Apply the new discount.
				body := &service.ApplyDiscountRequest{
					CustomerID:  customerID.String(),
					ProductID:   "1",
					PromotionID: discountID,
					AppliedTimeRange: service.AppliedTimeRange{
						StartTimeInSeconds: invoice.InvoiceStartTimeInSeconds,
						// Credit are valid for 1 month.
						EndTimeInSeconds: time.Now().AddDate(0, 1, 0).Unix(),
					},
				}

				reqInput = rest.RequestInput{
					URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-promotions"),
					Headers: map[string]string{"x-api-key": svc.AmberfloAPIKey},
					Body:    body,
				}

				_, err = rest.Post(&reqInput)
				if err != nil {
					// We can ignore this error because it only occurs on the following cases:
					// 1. Same promotion is deleted and applied during a short time window.
					//		- If a promotion is deleted, we should not apply the same one, since we are
					//		  deleting old promotions and applying new ones.
					// 2. The same promotion is being applied again.
					//		- We can ignore this case, because the new promotion is already applied.
					if !strings.Contains(err.Error(), "Cannot apply the same promotion multiple times on a single customer") {
						logger.AddError(fmt.Sprintf("Failed to apply discount for customer %s: %v", customerID.String(), err))
					}
				}
			}
		}
	}

	path, err := svc.DumpLogsToFile(logger)
	if err != nil {
		fmt.Printf("Failed to save logs to file: %v", err)
		return
	}

	fmt.Printf("Logs were saved to: %s", path)
}
