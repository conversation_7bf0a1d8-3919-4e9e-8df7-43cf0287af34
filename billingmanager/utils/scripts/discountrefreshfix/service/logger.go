package service

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v2"
)

type Event struct {
	TimeStamp time.Time
	Details   string
	IsError   bool
}

type EventsLogger struct {
	Events []Event
}

func NewEventsLogger() *EventsLogger {
	events := []Event{}
	return &EventsLogger{
		Events: events,
	}
}

func (e *EventsLogger) AddEvent(details string) {
	e.Events = append(e.Events, Event{
		TimeStamp: time.Now(),
		Details:   details,
		IsError:   false,
	})
}

func (e *EventsLogger) AddError(details string) {
	e.Events = append(e.Events, Event{
		TimeStamp: time.Now(),
		Details:   details,
		IsError:   true,
	})
}

func (svc *Service) DumpLogsToFile(logs *EventsLogger) (path string, err error) {
	yml, err := yaml.Marshal(logs)
	if err != nil {
		return "", fmt.Errorf("failed to dump to yaml: %w", err)
	}

	now := time.Now()
	logFileName := fmt.Sprintf("patch-%v-%v-%v_%v-%v-%v.out", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second())
	logFileFullPath := filepath.Join(svc.LogOutputDir, logFileName)

	defer func() {
		if err != nil {
			// Print out the contents in case they fail to be wrote to disk.
			fmt.Println(string(yml))
		}
	}()

	return logFileFullPath, os.WriteFile(logFileFullPath, yml, 0600)
}
