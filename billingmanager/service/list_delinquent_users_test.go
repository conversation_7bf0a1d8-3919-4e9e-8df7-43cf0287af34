package service

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/email"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

func (m *mockDB) GetAllCustomerInfos() ([]database.CustomerInfo, error) {
	args := m.Called()
	customerInfos := args.Get(0)
	if customerInfos == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).([]database.CustomerInfo), args.Error(1)
}

func (m *mockDB) MarkReceivedWarningEmail(customerID string) error {
	args := m.Called(customerID)
	return args.Error(0)
}

func (m *mockDB) MarkCustomerDelinquent(customerID string) (*time.Time, error) {
	args := m.Called(customerID)
	return args.Get(0).(*time.Time), args.Error(1)
}

func (m *mockDB) UnmarkCustomerDelinquent(customerID string) error {
	args := m.Called(customerID)
	return args.Error(0)
}

func TestListDelinquentUsers(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Get all invoices.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice/all")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			queryParams := req.URL.Query()
			customerID := queryParams.Get("customerId")
			var resp string

			if customerID == "org-id1" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "failed",
						"voided": false
					}
				]`
			} else if customerID == "org-id2" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "settled",
						"voided": false
					}
				]`
			} else if customerID == "org-id3" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "require_action",
						"voided": true
					}
				]`

			}
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Get customer credits.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			var resp string
			queryParams := req.URL.Query()
			customerID := queryParams.Get("customerId")
			if customerID == "org-id1" {
				resp = `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 3
				}`
			} else if customerID == "org-id2" {
				resp = `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 0
				}`
			} else if customerID == "org-id3" {
				resp = `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 3
				}`
			}
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	mockDB := &mockDB{}

	// Delinquent customer.
	customerInfo1 := database.CustomerInfo{
		CustomerID:           "org-id1",
		CustomerName:         "customer1",
		Emails:               "<EMAIL>",
		SuspendGracePeriod:   1,
		TerminateGracePeriod: 7,
		HasValidCard:         false,
	}

	time := timeNow()
	mockDB.On("MarkCustomerDelinquent", "org-id1").Return(&time, nil)

	// Delinquent customer.
	customerInfo2 := database.CustomerInfo{
		CustomerID:           "org-id2",
		CustomerName:         "customer2",
		Emails:               "<EMAIL>",
		SuspendGracePeriod:   1,
		TerminateGracePeriod: 3,
		HasValidCard:         false,
	}
	time2 := time.AddDate(0, 0, -2)
	mockDB.On("MarkCustomerDelinquent", "org-id2").Return(&time2, nil)

	// Previously delinquent customer, but is no longer delinquent.
	customerInfo3 := database.CustomerInfo{
		CustomerID:         "org-id3",
		CustomerName:       "customer3",
		Emails:             "<EMAIL>",
		SuspendGracePeriod: 1,
		HasValidCard:       true,
		DelinquentSince:    &time,
	}
	mockDB.On("UnmarkCustomerDelinquent", "org-id3").Return(nil)

	mockDB.On("GetAllCustomerInfos").Return([]database.CustomerInfo{customerInfo1, customerInfo2, customerInfo3}, nil)
	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
		db:     mockDB,
	}

	delinquentUsers, err := svc.ListDelinquentUsers(context.Background())
	assert.Nil(t, err)
	expectedResponse := []pb.ListDelinquentUsersResponse{
		{
			CustomerID:           "org-id1",
			DelinquentSince:      &time,
			SuspendGracePeriod:   1,
			TerminateGracePeriod: 7,
		},
		{
			CustomerID:           "org-id2",
			DelinquentSince:      &time2,
			SuspendGracePeriod:   1,
			TerminateGracePeriod: 3,
		},
	}
	assert.Equal(t, expectedResponse, delinquentUsers)
	mockDB.AssertExpectations(t)
}

func TestSendResourceCleanupNotificationEmail(t *testing.T) {
	mockDB := &mockDB{}
	mockEmailSvc := &email.MockEmailService{}

	svc := &BillingManagerService{
		db:       mockDB,
		emailSvc: mockEmailSvc,
	}

	// Delinquent customer did not receive notification email.
	time := timeNow().AddDate(0, 0, -2)
	customerInfo1 := database.CustomerInfo{
		CustomerID:           "org-id1",
		CustomerName:         "customer1",
		Emails:               "<EMAIL>",
		DelinquentSince:      &time,
		SuspendGracePeriod:   1,
		TerminateGracePeriod: 7,
		HasValidCard:         false,
	}

	mockEmailSvc.On("SendResourceCleanupNotificationEmail", "customer1", "org-id1", mock.Anything, mock.Anything).Return(nil)
	mockDB.On("GetCustomerInfo", "org-id1").Return(&customerInfo1, nil)
	mockDB.On("MarkReceivedWarningEmail", "org-id1").Return(nil)

	err := svc.SendResourceCleanupNotificationEmail(context.Background(), "org-id1")
	assert.NoError(t, err)

	// Delinquent customer already received notification email.
	time2 := timeNow()
	customerInfo2 := database.CustomerInfo{
		CustomerID:           "org-id2",
		CustomerName:         "customer2",
		Emails:               "<EMAIL>",
		SuspendGracePeriod:   3,
		TerminateGracePeriod: 7,
		DelinquentSince:      &time,
		HasValidCard:         false,
		ReceivedWarningEmail: &time2,
	}

	mockDB.On("GetCustomerInfo", "org-id2").Return(&customerInfo2, nil)

	err = svc.SendResourceCleanupNotificationEmail(context.Background(), "org-id2")
	assert.NoError(t, err)

	mockEmailSvc.AssertExpectations(t)
	mockDB.AssertExpectations(t)
}
