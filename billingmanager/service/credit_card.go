package service

import (
	"context"
	"fmt"

	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/customer"
	"github.com/stripe/stripe-go/paymentmethod"
	"github.com/tigergraph/cloud-universe/common/pb"
)

func (svc *BillingManagerService) setCustomerHasValidCard(customerID string) error {
	return svc.db.UpdateHasValidCard(customerID, true)
}

func (svc *BillingManagerService) getCardsFromStripe(customerID string, stripeID string) *[]pb.CustomerCreditCard {
	stripeParams := &stripe.PaymentMethodListParams{
		Customer: stripe.String(stripeID),
		Type:     stripe.String("card"),
	}

	var customerCreditCards []pb.CustomerCreditCard
	iter := paymentmethod.List(stripeParams)
	for iter.Next() {
		paymentMethod := iter.PaymentMethod()
		customerCreditCards = append(customerCreditCards, pb.CustomerCreditCard{
			OrgID:       customerID,
			CardID:      paymentMethod.ID,
			Last4Digits: paymentMethod.Card.Last4,
			ExpireMonth: paymentMethod.Card.ExpMonth,
			ExpireYear:  paymentMethod.Card.ExpYear,
			NameOnCard:  paymentMethod.BillingDetails.Name,
			Brand:       string(paymentMethod.Card.Brand),
		})
	}

	return &customerCreditCards
}

// GetCustomerCreditCards retrieves all of the customer's cards in Stripe and their default card.
func (svc *BillingManagerService) GetCustomerCreditCards(ctx context.Context, customerID string) (*pb.GetCustomerCreditCardsResponse, error) {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return nil, fmt.Errorf("Unable to retrieve customerInfo: %v", err)
	}

	customerCreditCards := svc.getCardsFromStripe(customerID, customerInfo.StripeID)

	// Retrieve customer's default credit card.
	stripeCustomer, err := customer.Get(customerInfo.StripeID, nil)
	if err != nil {
		return nil, fmt.Errorf("Unable to retrieve customer's default card from Stripe: %v", err)
	}

	defaultCard := ""
	if stripeCustomer.InvoiceSettings.DefaultPaymentMethod != nil {
		defaultCard = stripeCustomer.InvoiceSettings.DefaultPaymentMethod.ID
	}

	customerCardsResp := &pb.GetCustomerCreditCardsResponse{
		DefaultCard: defaultCard,
		CreditCards: *customerCreditCards,
	}

	return customerCardsResp, nil
}

func (svc *BillingManagerService) updateDefaultCreditCard(stripeID string, cardID string) error {
	stripeParams := &stripe.CustomerParams{
		InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
			DefaultPaymentMethod: stripe.String(cardID),
		},
	}

	_, err := customer.Update(stripeID, stripeParams)
	return err
}

func (svc *BillingManagerService) UpdateCustomerDefaultCreditCard(ctx context.Context, cardID string, customerID string) error {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return fmt.Errorf("Unable to retrieve customerInfo: %v", err)
	}

	return svc.updateDefaultCreditCard(customerInfo.StripeID, cardID)
}

// DeleteCustomerCreditCard removes customer's Stripe card and assigns a new default card if applicable.
func (svc *BillingManagerService) DeleteCustomerCreditCard(ctx context.Context, cardID string, customerID string) error {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return fmt.Errorf("Unable to retrieve customerInfo: %v", err)
	}

	creditCards := svc.getCardsFromStripe(customerID, customerInfo.StripeID)

	if len(*creditCards) == 1 {
		return fmt.Errorf("Card cannot be deleted, since at least one card must be associated with the account.")
	}

	// If default card was removed, assign another card as default.
	// Retrieve Stripe customer info before card deletion o/w L109 panics.
	stripeCustomer, err := customer.Get(customerInfo.StripeID, nil)
	if err != nil {
		return fmt.Errorf("Unable to retrieve Stripe customer: %v", err)
	}

	_, err = paymentmethod.Detach(cardID, nil)
	if err != nil {
		return fmt.Errorf("Unable to delete the card: %v", err)
	}

	if stripeCustomer.InvoiceSettings.DefaultPaymentMethod.ID == cardID {
		cards := svc.getCardsFromStripe(customerID, customerInfo.StripeID)
		svc.updateDefaultCreditCard(customerInfo.StripeID, (*cards)[0].CardID)
	}

	return nil
}

// AddCustomerCreditCard adds provided card and sets it as default.
func (svc *BillingManagerService) AddCustomerCreditCard(ctx context.Context, cardID string, customerID string) error {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return fmt.Errorf("Unable to retrieve customerInfo: %v", err)
	}

	stripeParams := &stripe.PaymentMethodAttachParams{
		Customer: stripe.String(customerInfo.StripeID),
	}

	paymentMethod, err := paymentmethod.Attach(cardID, stripeParams)
	if err != nil {
		return fmt.Errorf("Unable to add card: %v", err)
	}

	if err = svc.setCustomerHasValidCard(customerID); err != nil {
		return fmt.Errorf("Failed to mark customer as having valid card: %v", err)
	}

	if err = svc.updateDefaultCreditCard(customerInfo.StripeID, paymentMethod.ID); err != nil {
		return fmt.Errorf("Failed to mark card as default: %v", err)
	}

	return nil
}

// GetCustomerCreditCard retrieves customer credit card information from the stripe PaymentMethod object.
func (svc *BillingManagerService) GetCustomerCreditCard(ctx context.Context, paymentMethodID string) (*pb.CustomerCreditCard, error) {
	params := &stripe.PaymentMethodParams{}
	pm, err := paymentmethod.Get(paymentMethodID, params)
	if err != nil || pm.Card == nil {
		return nil, fmt.Errorf("Invalid payment method provided: %v", err)

	}

	card := &pb.CustomerCreditCard{
		CardID:      pm.ID,
		Last4Digits: pm.Card.Last4,
		ExpireMonth: pm.Card.ExpMonth,
		ExpireYear:  pm.Card.ExpYear,
		NameOnCard:  pm.BillingDetails.Name,
		Brand:       string(pm.Card.Brand),
	}

	return card, err
}
