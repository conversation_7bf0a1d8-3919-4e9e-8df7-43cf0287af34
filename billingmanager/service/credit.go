package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/tidwall/gjson"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

type AddCreditRequest struct {
	CustomerID          string `json:"customerId"`
	ID                  string `json:"id"`
	PrepaidOfferVersion int    `json:"prepaidOfferVersion"`
	PrepaidPrice        int    `json:"prepaidPrice"`
	ProductID           string `json:"productId"`
	StartTimeInSeconds  int64  `json:"startTimeInSeconds"`
	ExternalPayment     bool   `json:"externalPayment"`
}

type UpdateCreditRequest struct {
	PaymentID     string `json:"paymentId"`
	PaymentStatus string `json:"paymentStatus"`
	PrepaidURI    string `json:"prepaidUri"`
	SystemName    string `json:"systemName"`
}

func (svc *BillingManagerService) AddCredit(ctx context.Context, in *pb.AddCreditRequest) error {
	body := AddCreditRequest{
		CustomerID:          in.CustomerID,
		ID:                  in.ID,
		PrepaidOfferVersion: -1,
		PrepaidPrice:        in.PrepaidPrice,
		ProductID:           "1",
		StartTimeInSeconds:  time.Now().Unix(),
		ExternalPayment:     true,
	}

	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-prepaid"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    body,
		Client:  svc.client,
	}

	resp, err := rest.Post(&reqInput)
	if err != nil {
		return fmt.Errorf("unable to add credit: %v", err)
	}

	prepaidURI := gjson.GetBytes(resp, "firstInvoiceUri").String()
	updateCreditBody := UpdateCreditRequest{
		PaymentID:     in.ID,
		PaymentStatus: "SETTLED",
		PrepaidURI:    prepaidURI,
		SystemName:    "external_credit",
	}

	reqInput = rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/external/prepaid-payment-status"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    updateCreditBody,
		Client:  svc.client,
	}

	_, err = rest.Post(&reqInput)
	if err != nil {
		return fmt.Errorf("unable to activate credit: %v", err)
	}

	return nil
}

type ApplyDiscountRequest struct {
	CustomerID       string           `json:"customerId"`
	ProductID        string           `json:"productId"`
	PromotionID      string           `json:"promotionId"`
	AppliedTimeRange AppliedTimeRange `json:"appliedTimeRange"`
}

type AppliedTimeRange struct {
	StartTimeInSeconds int64 `json:"startTimeInSeconds"`
	EndTimeInSeconds   int64 `json:"endTimeInSeconds"`
}

// ApplyOnboardingTaskDiscount applies the discount to the customer upon onboarding task completion.
// Onboarding Task ID is the value of the corresponding discount item's description in Amberflo, so that we can retrieve the correct discount.
// Changes in the onboarding task need to be reflected in the Amberflo discount as well, and will
// most likely require the creation of a new onboarding task record and discount item.
func (svc *BillingManagerService) ApplyOnboardingTaskDiscount(ctx context.Context, CustomerID string, onboardingTaskID string) error {
	_, err := uuid.Parse(onboardingTaskID)
	if err != nil {
		return fmt.Errorf("invalid onboarding task ID: %v", err)
	}

	// Retrieve list of discounts.
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/promotions/list"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Client:  svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return fmt.Errorf("failed to get discount list: %v", err)
	}

	// Retrieve the discount id by the value of its description which is the onboarding task id.
	var discountID string
	idList := gjson.Get(string(resp), "#.{id,description,lockingStatus}")
	for _, id := range idList.Array() {
		if gjson.Get(id.String(), "lockingStatus").String() == "deprecated" {
			continue
		}

		if gjson.Get(id.String(), "description").String() == onboardingTaskID {
			discountID = gjson.Get(id.String(), "id").String()
		}
	}

	if discountID == "" {
		return fmt.Errorf("discount not found for onboarding task ID: %s", onboardingTaskID)
	}

	invoice, err := svc.GetLatestInvoice(ctx, CustomerID, true)
	if err != nil {
		return fmt.Errorf("failed to get latest invoice: %v", err)
	}

	// Apply the discount to the customer.
	body := &ApplyDiscountRequest{
		CustomerID:  CustomerID,
		ProductID:   "1",
		PromotionID: discountID,
		AppliedTimeRange: AppliedTimeRange{
			StartTimeInSeconds: invoice.InvoiceStartTimeInSeconds,
			// Credit are valid for 1 month.
			EndTimeInSeconds: time.Now().AddDate(0, 1, 0).Unix(),
		},
	}

	reqInput = rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-promotions"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Client:  svc.client,
		Body:    body,
	}

	_, err = rest.Post(&reqInput)
	if err != nil {
		return fmt.Errorf("failed to apply discount: %v", err)
	}

	return nil
}

func (svc *BillingManagerService) GetCredit(ctx context.Context, customerID string, fromCache bool) (float64, error) {
	invoice, err := svc.GetLatestInvoice(ctx, customerID, fromCache)
	if err != nil {
		return 0, err
	}

	return invoice.AvailablePayAsYouGoMoneyInBaseCurrency, nil
}
