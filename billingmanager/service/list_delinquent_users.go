package service

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"gorm.io/gorm"
)

// ListDelinquentUsers returns a list of users we consider "delinquent" in our billing service.
func (svc *BillingManagerService) ListDelinquentUsers(ctx context.Context) ([]pb.ListDelinquentUsersResponse, error) {
	log := logger.L().WithContext(ctx)
	customerInfos, err := svc.db.GetAllCustomerInfos()
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve customer infos: %v", err)
	}

	var delinquentUsers []pb.ListDelinquentUsersResponse
	for _, customerInfo := range customerInfos {
		unpaidInvoice, err := svc.CheckUnpaidInvoices(ctx, customerInfo.CustomerID, true, 0)
		if err != nil {
			log.Errorf("Failed to check customer [id=%s] invoices: %v", customerInfo.CustomerID, err)
			continue
		}

		credit, err := svc.GetCredit(ctx, customerInfo.CustomerID, true)
		if err != nil {
			log.Errorf("Failed to check customer [id=%s] credits: %v", customerInfo.CustomerID, err)
			continue
		}

		activeMarketplaceSubscription := false
		marketplaceCustomer, err := svc.db.GetAWSMarketplaceInfoByCustomerID(customerInfo.CustomerID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Errorf("Failed to check customer [id=%s] marketplace status: %v", customerInfo.CustomerID, err)
				continue
			}
		}

		if marketplaceCustomer != nil && marketplaceCustomer.Status == constants.MarketplaceStatusActive {
			activeMarketplaceSubscription = true
		}

		// We don't call PaymentValidation() here because in case of error from Amberflo,
		// we will incorrectly unmark the customer.
		if !unpaidInvoice && (customerInfo.HasValidCard || credit > 0 || activeMarketplaceSubscription) {
			// Customer is not delinquent.
			if customerInfo.DelinquentSince != nil {
				svc.db.UnmarkCustomerDelinquent(customerInfo.CustomerID)
			}

			continue
		}

		// If above check fails, then customer is delinquent.
		timeSinceDelinquent, err := svc.db.MarkCustomerDelinquent(customerInfo.CustomerID)
		if err != nil {
			log.Errorf("Failed to mark customer [id=%s] delinquent: %v", customerInfo.CustomerID, err)
			continue
		}

		delinquentUsers = append(delinquentUsers, pb.ListDelinquentUsersResponse{
			CustomerID:           customerInfo.CustomerID,
			DelinquentSince:      timeSinceDelinquent,
			SuspendGracePeriod:   customerInfo.SuspendGracePeriod,
			TerminateGracePeriod: customerInfo.TerminateGracePeriod,
		})
	}

	return delinquentUsers, nil
}

func (svc *BillingManagerService) SendResourceCleanupNotificationEmail(ctx context.Context, customerID string) error {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return fmt.Errorf("Failed to retrieve customer info [id=%s]: %v", customerID, err)
	}

	if customerInfo.DelinquentSince == nil {
		return fmt.Errorf("Customer [id=%s] has not been marked delinquent", customerInfo.CustomerID)
	}

	now := timeNow()
	gracePeriod := customerInfo.SuspendGracePeriod
	// If time has passed customer's suspend grace period, we are now in the terminate grace period.
	// if now.After(customerInfo.DelinquentSince.AddDate(0, 0, gracePeriod)) {
	// 	gracePeriod = customerInfo.TerminateGracePeriod
	// }

	// Email notifications are sent every 7 days.
	emailDays := []int{customerInfo.SuspendGracePeriod, customerInfo.TerminateGracePeriod}
	for i := 0; i <= gracePeriod; i += 7 {
		emailDays = append(emailDays, i)
	}
	sort.Ints(emailDays)

	// Find the latest email date before the current date.
	emailDate := customerInfo.DelinquentSince.AddDate(0, 0, emailDays[0])
	for _, emailDay := range emailDays {
		nextEmailDate := customerInfo.DelinquentSince.AddDate(0, 0, emailDay)
		if now.After(nextEmailDate) {
			break
		}

		emailDate = nextEmailDate
	}

	if customerInfo.ReceivedWarningEmail == nil || customerInfo.ReceivedWarningEmail.Before(emailDate) {
		// Send email notification to customer.
		recipients := strings.Split(customerInfo.Emails, ",")

		err := svc.emailSvc.SendResourceCleanupNotificationEmail(customerInfo.CustomerName, customerInfo.CustomerID, recipients, customerInfo.DelinquentSince.AddDate(0, 0, gracePeriod))
		if err != nil {
			return fmt.Errorf("Failed to send notification email to customer [id=%s]: %v", customerInfo.CustomerID, err)
		}

		err = svc.db.MarkReceivedWarningEmail(customerInfo.CustomerID)
		if err != nil {
			return fmt.Errorf("Failed to mark customer [id=%s] received warning email: %v", customerInfo.CustomerID, err)
		}
	}

	return nil
}
