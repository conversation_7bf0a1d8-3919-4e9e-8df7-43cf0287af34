package service

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/quota-manager/data"
	"github.com/tigergraph/cloud-universe/utils/email"
	"github.com/tigergraph/cloud-universe/utils/logger/loggeriface"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

func (m *mockDB) GetAWSMarketplaceInfoByMarketplaceID(marketplaceID string) (*database.AWSMarketplaceInfo, error) {
	args := m.Called(marketplaceID)
	return args.Get(0).(*database.AWSMarketplaceInfo), args.Error(1)
}

func (m *mockDB) UpdateAWSMarketplaceInfo(info *database.AWSMarketplaceInfo) error {
	args := m.Called(info)
	return args.Error(0)
}

type MockSQSClient struct {
	mock.Mock
	SQSClient
}

func (m *MockSQSClient) DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error) {
	args := m.Called(ctx, params, optFns)
	return args.Get(0).(*sqs.DeleteMessageOutput), args.Error(1)
}

func (m *MockSQSClient) ReceiveMessage(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error) {
	args := m.Called(ctx, params, optFns)
	return args.Get(0).(*sqs.ReceiveMessageOutput), args.Error(1)
}

func TestPollSQSMessage(t *testing.T) {
	// Test successful subscription message from SQS.
	t.Run("Subscribe success", func(t *testing.T) {
		mockLogger := new(loggeriface.MockLogger)
		mockLogger.On("Infof", "AWS marketplace customer %v has %v", []interface{}{"4CK9ZKwL12a", "subscribe-success"}).Once()

		mockDB := &mockDB{}
		mockDB.On("GetAWSMarketplaceInfoByMarketplaceID", mock.Anything).Return(&database.AWSMarketplaceInfo{CustomerID: "test-id"}, nil)
		mockDB.On("UpdateAWSMarketplaceInfo", mock.Anything).Return(nil)

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}
		mockQuotaManager.On("UpdateOrgQuotaTier", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		msgBody := `
	{  "Type" : "Notification",  "MessageId" : "41574cf1-ff4f-53dc-832a-136ec27f75f6",  "TopicArn" : "arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z",  "Message" : "{\"action\":\"subscribe-success\",\"offer-identifier\":\"offer-2cifebbndyqus\",\"customer-identifier\":\"4CK9ZKwL12a\",\"product-code\":\"2nk49unupll3yw2sclo7xwt6z\"}",  "Timestamp" : "2024-11-01T20:32:48.353Z",     "SignatureVersion" : "1",  "Signature" : "PzNlM87pr6S834rCv6SZK273u04bGAAwRzlOeVDlIWZssEIP49UQ6jMbwGjsw1xC++/M6ggmNqUZQbljPPM+1CX2N66UTKD1EFOQwxAbZ6lUDoFdtwW4IKDZCsn20Om4sxdBPW0EeLTZ99OYT8Or8U2ui0wYSFbxOzZzYuTEhh5B6HrxziVphHHGEvDJHSh1MPzzcra3R8LZschMRTGKvCOoNfvD2lRvBB+xh9W7O0a2T6nzt3ZFnYEcmgj0kBwZSIFzkqLoxBqpPm5VP0NgOjZb4VzGqDehUd/LpBOP2/1vh87BrDi34p05XKwHN2X+UMvJBK+HeK3BaP/j1123SQ==",  "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",        "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z:de96b885-6538-4c85-be90-ec9502d128b0"}
	`
		sqsOutput := &sqs.ReceiveMessageOutput{
			Messages: []types.Message{
				{
					Attributes: map[string]string{
						"ApproximateReceiveCount": "1",
						"SentTimestamp":           strconv.FormatInt(time.Now().UnixMilli(), 10),
					},
					Body: &msgBody,
				},
			},
		}
		mockSQSClient.On("ReceiveMessage", mock.Anything, mock.Anything, mock.Anything).Return(sqsOutput, nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				Marketplace: config.MarketplaceConfig{
					SQSURL:      "test-url",
					ProductCode: "2nk49unupll3yw2sclo7xwt6z",
				},
			},
			db: mockDB,
		}
		svc.pollSQSMessage(context.Background(), mockSQSClient, mockQuotaManager, mockLogger)

		mockLogger.AssertExpectations(t)
	})

	// Test message has been in SQS queue for over 13 days.
	t.Run("Message has been in queue for over 13 days", func(t *testing.T) {
		mockLogger := new(loggeriface.MockLogger)
		mockLogger.On("Warnf", "AWS sqs message has been in queue for over 13 days: %v, msg: %s", mock.Anything, mock.Anything).Once()

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		msgBody := `
	{  "Type" : "Notification",  "MessageId" : "41574cf1-ff4f-53dc-832a-136ec27f75f6",  "TopicArn" : "arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z",  "Message" : "{\"action\":\"subscribe-success\",\"offer-identifier\":\"offer-2cifebbndyqus\",\"customer-identifier\":\"4CK9ZKwL12a\",\"product-code\":\"2nk49unupll3yw2sclo7xwt6z\"}",  "Timestamp" : "2024-11-01T20:32:48.353Z",     "SignatureVersion" : "1",  "Signature" : "PzNlM87pr6S834rCv6SZK273u04bGAAwRzlOeVDlIWZssEIP49UQ6jMbwGjsw1xC++/M6ggmNqUZQbljPPM+1CX2N66UTKD1EFOQwxAbZ6lUDoFdtwW4IKDZCsn20Om4sxdBPW0EeLTZ99OYT8Or8U2ui0wYSFbxOzZzYuTEhh5B6HrxziVphHHGEvDJHSh1MPzzcra3R8LZschMRTGKvCOoNfvD2lRvBB+xh9W7O0a2T6nzt3ZFnYEcmgj0kBwZSIFzkqLoxBqpPm5VP0NgOjZb4VzGqDehUd/LpBOP2/1vh87BrDi34p05XKwHN2X+UMvJBK+HeK3BaP/j1123SQ==",  "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",        "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z:de96b885-6538-4c85-be90-ec9502d128b0"}
	`
		sqsOutput := &sqs.ReceiveMessageOutput{
			Messages: []types.Message{
				{
					Attributes: map[string]string{
						"ApproximateReceiveCount": "1",
						"SentTimestamp":           strconv.FormatInt(time.Now().AddDate(0, 0, -14).UnixMilli(), 10),
					},
					Body: &msgBody,
				},
			},
		}
		mockSQSClient.On("ReceiveMessage", mock.Anything, mock.Anything, mock.Anything).Return(sqsOutput, nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				Marketplace: config.MarketplaceConfig{
					SQSURL:      "test-url",
					ProductCode: "test-code",
				},
			},
		}
		svc.pollSQSMessage(context.Background(), mockSQSClient, mockQuotaManager, mockLogger)

		mockLogger.AssertExpectations(t)
	})

	// Test empty customer ID.
	t.Run("Empty customer ID", func(t *testing.T) {
		mockLogger := new(loggeriface.MockLogger)
		mockLogger.On("Errorf", "AWS marketplace customerID in SQS message is empty: %v, msg: %s", mock.Anything, mock.Anything).Once()

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		msgBody := `
	{  "Type" : "Notification",  "MessageId" : "41574cf1-ff4f-53dc-832a-136ec27f75f6",  "TopicArn" : "arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z",  "Message" : "{\"action\":\"subscribe-success\",\"offer-identifier\":\"offer-2cifebbndyqus\",\"customer-identifier\":\"\",\"product-code\":\"2nk49unupll3yw2sclo7xwt6z\"}",  "Timestamp" : "2024-11-01T20:32:48.353Z",     "SignatureVersion" : "1",  "Signature" : "PzNlM87pr6S834rCv6SZK273u04bGAAwRzlOeVDlIWZssEIP49UQ6jMbwGjsw1xC++/M6ggmNqUZQbljPPM+1CX2N66UTKD1EFOQwxAbZ6lUDoFdtwW4IKDZCsn20Om4sxdBPW0EeLTZ99OYT8Or8U2ui0wYSFbxOzZzYuTEhh5B6HrxziVphHHGEvDJHSh1MPzzcra3R8LZschMRTGKvCOoNfvD2lRvBB+xh9W7O0a2T6nzt3ZFnYEcmgj0kBwZSIFzkqLoxBqpPm5VP0NgOjZb4VzGqDehUd/LpBOP2/1vh87BrDi34p05XKwHN2X+UMvJBK+HeK3BaP/j1123SQ==",  "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",        "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z:de96b885-6538-4c85-be90-ec9502d128b0"}
	`
		sqsOutput := &sqs.ReceiveMessageOutput{
			Messages: []types.Message{
				{
					Attributes: map[string]string{
						"ApproximateReceiveCount": "1",
						"SentTimestamp":           strconv.FormatInt(time.Now().UnixMilli(), 10),
					},
					Body: &msgBody,
				},
			},
		}
		mockSQSClient.On("ReceiveMessage", mock.Anything, mock.Anything, mock.Anything).Return(sqsOutput, nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				Marketplace: config.MarketplaceConfig{
					SQSURL:      "test-url",
					ProductCode: "2nk49unupll3yw2sclo7xwt6z",
				},
			},
		}
		svc.pollSQSMessage(context.Background(), mockSQSClient, mockQuotaManager, mockLogger)

		mockLogger.AssertExpectations(t)
	})

	// Test invalid timestamp.
	t.Run("Invalid timestamp", func(t *testing.T) {
		mockLogger := new(loggeriface.MockLogger)
		mockLogger.On("Errorf", "failed to convert AWS marketplace SQS message time stamp: %v, msg: %s", mock.Anything, mock.Anything).Once()

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		msgBody := `
	{  "Type" : "Notification",  "MessageId" : "41574cf1-ff4f-53dc-832a-136ec27f75f6",  "TopicArn" : "arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z",  "Message" : "{\"action\":\"subscribe-success\",\"offer-identifier\":\"offer-2cifebbndyqus\",\"customer-identifier\":\"4CK9ZKwL12a\",\"product-code\":\"2nk49unupll3yw2sclo7xwt6z\"}",  "Timestamp" : "2024-11-01T20:32:48.353Z",     "SignatureVersion" : "1",  "Signature" : "PzNlM87pr6S834rCv6SZK273u04bGAAwRzlOeVDlIWZssEIP49UQ6jMbwGjsw1xC++/M6ggmNqUZQbljPPM+1CX2N66UTKD1EFOQwxAbZ6lUDoFdtwW4IKDZCsn20Om4sxdBPW0EeLTZ99OYT8Or8U2ui0wYSFbxOzZzYuTEhh5B6HrxziVphHHGEvDJHSh1MPzzcra3R8LZschMRTGKvCOoNfvD2lRvBB+xh9W7O0a2T6nzt3ZFnYEcmgj0kBwZSIFzkqLoxBqpPm5VP0NgOjZb4VzGqDehUd/LpBOP2/1vh87BrDi34p05XKwHN2X+UMvJBK+HeK3BaP/j1123SQ==",  "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",        "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z:de96b885-6538-4c85-be90-ec9502d128b0"}
	`
		sqsOutput := &sqs.ReceiveMessageOutput{
			Messages: []types.Message{
				{
					Attributes: map[string]string{
						"ApproximateReceiveCount": "1",
						"SentTimestamp":           "abc",
					},
					Body: &msgBody,
				},
			},
		}
		mockSQSClient.On("ReceiveMessage", mock.Anything, mock.Anything, mock.Anything).Return(sqsOutput, nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				Marketplace: config.MarketplaceConfig{
					SQSURL:      "test-url",
					ProductCode: "test-code",
				},
			},
		}
		svc.pollSQSMessage(context.Background(), mockSQSClient, mockQuotaManager, mockLogger)

		mockLogger.AssertExpectations(t)
	})

	// Test invalid product code.
	t.Run("Invalid product code", func(t *testing.T) {
		mockLogger := new(loggeriface.MockLogger)
		mockLogger.On("Errorf", "provided product code in SQS message does not match with our SaaS listing: %v, msg: %s", mock.Anything, mock.Anything).Once()

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		msgBody := `
	{  "Type" : "Notification",  "MessageId" : "41574cf1-ff4f-53dc-832a-136ec27f75f6",  "TopicArn" : "arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z",  "Message" : "{\"action\":\"subscribe-success\",\"offer-identifier\":\"offer-2cifebbndyqus\",\"customer-identifier\":\"4CK9ZKwL12a\",\"product-code\":\"2nk49unupll3yw2sclo7xwt6z\"}",  "Timestamp" : "2024-11-01T20:32:48.353Z",     "SignatureVersion" : "1",  "Signature" : "PzNlM87pr6S834rCv6SZK273u04bGAAwRzlOeVDlIWZssEIP49UQ6jMbwGjsw1xC++/M6ggmNqUZQbljPPM+1CX2N66UTKD1EFOQwxAbZ6lUDoFdtwW4IKDZCsn20Om4sxdBPW0EeLTZ99OYT8Or8U2ui0wYSFbxOzZzYuTEhh5B6HrxziVphHHGEvDJHSh1MPzzcra3R8LZschMRTGKvCOoNfvD2lRvBB+xh9W7O0a2T6nzt3ZFnYEcmgj0kBwZSIFzkqLoxBqpPm5VP0NgOjZb4VzGqDehUd/LpBOP2/1vh87BrDi34p05XKwHN2X+UMvJBK+HeK3BaP/j1123SQ==",  "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-60eadc530605d63b8e62a523676ef735.pem",        "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:287250355862:aws-mp-subscription-notification-2nk49unupll3yw2sclo7xwt6z:de96b885-6538-4c85-be90-ec9502d128b0"}
	`
		sqsOutput := &sqs.ReceiveMessageOutput{
			Messages: []types.Message{
				{
					Attributes: map[string]string{
						"ApproximateReceiveCount": "1",
						"SentTimestamp":           strconv.FormatInt(time.Now().UnixMilli(), 10),
					},
					Body: &msgBody,
				},
			},
		}
		mockSQSClient.On("ReceiveMessage", mock.Anything, mock.Anything, mock.Anything).Return(sqsOutput, nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				Marketplace: config.MarketplaceConfig{
					SQSURL:      "test-url",
					ProductCode: "test-code",
				},
			},
		}
		svc.pollSQSMessage(context.Background(), mockSQSClient, mockQuotaManager, mockLogger)

		mockLogger.AssertExpectations(t)
	})
}

func TestProcessUnsubPending(t *testing.T) {
	// getCustomer() endpoint
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			resp := `
				{
					"customerId": "test-org",
					"traits": {
						"paymentProviderName": "STRIPE",
						"stripeId": "cus_I65eFupvE77rFP"
					}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	t.Run("Unsubscribe after payment method switch has taken effect", func(t *testing.T) {
		// GetCredit() endpoint
		urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
		httpmock.RegisterResponder("GET", urlPath,
			func(req *http.Request) (*http.Response, error) {
				resp := `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 0
				}`
				return httpmock.NewBytesResponse(200, []byte(resp)), nil
			},
		)

		now := time.Now()

		mockDB := &mockDB{}
		mockDB.On("GetAWSMarketplaceInfoByMarketplaceID", "test-marketplace-id").Return(
			&database.AWSMarketplaceInfo{
				CustomerID: "test-org",
				Status:     constants.MarketplaceStatusActive,
			}, nil)
		mockDB.On("UpdateAWSMarketplaceInfo", &database.AWSMarketplaceInfo{
			CustomerID:       "test-org",
			Status:           constants.MarketplaceStatusPendingCancellation,
			StatusUpdateTime: &now,
		}).Return(nil)
		mockDB.On("GetCustomerInfo", "test-org").Return(&database.CustomerInfo{
			HasValidCard: true,
		}, nil)

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		svc := &BillingManagerService{
			cfg: &config.Config{
				AmberfloAPIKey: testAPIKey,
			},
			db:     mockDB,
			client: client,
		}

		svc.processUnsubPending(context.Background(), mockSQSClient, &types.Message{}, "test-marketplace-id", &now, mockQuotaManager)

		mockDB.AssertExpectations(t)
	})

	t.Run("Unsubscribe mid-billing cycle with valid payment method", func(t *testing.T) {
		// GetCredit() endpoint
		urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
		httpmock.RegisterResponder("GET", urlPath,
			func(req *http.Request) (*http.Response, error) {
				resp := `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 1
				}`
				return httpmock.NewBytesResponse(200, []byte(resp)), nil
			},
		)

		now := time.Now()

		mockDB := &mockDB{}
		mockDB.On("GetAWSMarketplaceInfoByMarketplaceID", "test-marketplace-id").Return(
			&database.AWSMarketplaceInfo{
				CustomerID: "test-org",
				Status:     constants.MarketplaceStatusActive,
			}, nil)
		mockDB.On("UpdateAWSMarketplaceInfo", &database.AWSMarketplaceInfo{
			CustomerID:       "test-org",
			Status:           constants.MarketplaceStatusPendingCancellation,
			StatusUpdateTime: &now,
		}).Return(nil)
		mockDB.On("GetCustomerInfo", "test-org").Return(&database.CustomerInfo{}, nil)

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockEmailSvc := &email.MockEmailService{}
		mockEmailSvc.On("Notify", "Customer unsubscribed from AWS marketplace mid-billing cycle", mock.Anything).Return(nil)

		mockQuotaManager := &pb.MockQuotaManager{}

		svc := &BillingManagerService{
			cfg: &config.Config{
				AmberfloAPIKey: testAPIKey,
			},
			emailSvc: mockEmailSvc,
			db:       mockDB,
			client:   client,
		}

		svc.processUnsubPending(context.Background(), mockSQSClient, &types.Message{}, "test-marketplace-id", &now, mockQuotaManager)

		mockDB.AssertExpectations(t)
		mockEmailSvc.AssertExpectations(t)
	})

	t.Run("Unsubscribe mid-billing cycle without valid payment method", func(t *testing.T) {
		// GetCredit() endpoint
		urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
		httpmock.RegisterResponder("GET", urlPath,
			func(req *http.Request) (*http.Response, error) {
				resp := `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 0
				}`
				return httpmock.NewBytesResponse(200, []byte(resp)), nil
			},
		)

		now := time.Now()

		mockDB := &mockDB{}
		mockDB.On("GetAWSMarketplaceInfoByMarketplaceID", "test-marketplace-id").Return(
			&database.AWSMarketplaceInfo{
				CustomerID: "test-org",
				Status:     constants.MarketplaceStatusActive,
			}, nil)
		mockDB.On("UpdateAWSMarketplaceInfo", &database.AWSMarketplaceInfo{
			CustomerID:       "test-org",
			Status:           constants.MarketplaceStatusPendingCancellation,
			StatusUpdateTime: &now,
		}).Return(nil)
		mockDB.On("GetCustomerInfo", "test-org").Return(&database.CustomerInfo{}, nil)
		mockDB.On("MarkCustomerDelinquent", "test-org").Return(&now, nil)

		mockSQSClient := new(MockSQSClient)
		mockSQSClient.On("DeleteMessage", mock.Anything, mock.Anything, mock.Anything).Return(&sqs.DeleteMessageOutput{}, nil)

		mockQuotaManager := &pb.MockQuotaManager{}
		mockQuotaManager.On("UpdateOrgQuotaTier", mock.Anything, "test-org", data.InitialCustomerTier).Return(nil)

		svc := &BillingManagerService{
			cfg: &config.Config{
				AmberfloAPIKey: testAPIKey,
			},
			db:     mockDB,
			client: client,
		}

		svc.processUnsubPending(context.Background(), mockSQSClient, &types.Message{}, "test-marketplace-id", &now, mockQuotaManager)

		mockDB.AssertExpectations(t)
		mockQuotaManager.AssertExpectations(t)
	})
}

func TestResolveAWSCustomer(t *testing.T) {
	svc := &BillingManagerService{
		cfg: &config.Config{
			Marketplace: config.MarketplaceConfig{
				SQSURL:      "test-url",
				ProductCode: "test-code",
				Region:      "us-east-1",
			},
		},
	}

	_, _, err := svc.ResolveAWSCustomer(context.Background(), "test-token")
	require.NotNil(t, err)
}
