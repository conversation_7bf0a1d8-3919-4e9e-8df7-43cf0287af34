package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const testAPIKey = "test-api-key"

func TestGetCredit(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", test<PERSON><PERSON><PERSON><PERSON>, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"invoiceUri": "payments/invoices/account_id=32306/customer_id=org_ZBVh66RyLFwjjVAZ/product_id=1/product_plan_id=9131fc0f-e211-4df9-b4df-1382adc12506/year=2024/month=08/day=30",
					"uuid": "f4191dc1-85c9-32c3-b9c3-dedeb4b2f778",
					"invoiceKey": {
						"accountId": "32306",
						"customerId": "org_ZBVh66RyLFwjjVAZ",
						"productId": "1",
						"productPlanId": "9131fc0f-e211-4df9-b4df-1382adc12506",
						"year": 2024,
						"month": 8,
						"day": 30
					},
					"planBillingPeriod": {
						"interval": "month",
						"intervalsCount": 1
					},
					"planName": "Default-Pricing-Plan",
					"invoiceStartTimeInSeconds": **********,
					"invoiceEndTimeInSeconds": **********,
					"gracePeriodInHours": 72,
					"productItemInvoices": [],
					"appliedPromotions": [],
					"appliedPrepaids": [],
					"productPlanFees": [],
					"appliedInvoiceBasedFees": [],
					"appliedCommitments": [],
					"itemSubscriptionDetails": null,
					"totalBill": {
						"itemPrice": 0,
						"fixPrice": 0,
						"invoiceBasedFees": 0,
						"commitmentsFees": 0,
						"prepaid": 0,
						"totalDiscount": 0,
						"totalPriceBeforeDiscount": 0,
						"totalPriceBeforePrepaid": 0,
						"totalPriceBeforeCommitments": 0,
						"totalPriceBeforeSalesTax": 0,
						"discountableFixPrice": 0,
						"discountableBasePrice": 0,
						"prepayableFixPrice": 0,
						"prepayableNotDiscountableFees": 0,
						"prepayableCommitmentsFees": 0,
						"totalPrepayablePrice": 0,
						"startTimeInSeconds": **********,
						"endTimeInSeconds": **********,
						"totalPrice": 0
					},
					"invoicePriceStatus": "open",
					"creditUnit": null,
					"paymentStatus": "pre_payment",
					"paymentCreatedInSeconds": null,
					"externalSystemStatus": null,
					"invoiceBillInBaseCurrency": {
						"itemPrice": 0,
						"fixPrice": 0,
						"invoiceBasedFees": 0,
						"commitmentsFees": 0,
						"prepaid": 0,
						"totalDiscount": 0,
						"totalPriceBeforeDiscount": 0,
						"totalPriceBeforePrepaid": 0,
						"totalPriceBeforeCommitments": 0,
						"totalPriceBeforeSalesTax": 0,
						"discountableFixPrice": 0,
						"discountableBasePrice": 0,
						"prepayableFixPrice": 0,
						"prepayableNotDiscountableFees": 0,
						"prepayableCommitmentsFees": 0,
						"totalPrepayablePrice": 0,
						"startTimeInSeconds": **********,
						"endTimeInSeconds": **********,
						"totalPrice": 0
					},
					"invoiceBillInCredits": null,
					"prepaidPriceUsedBaseCurrency": 0,
					"prepaidPriceUsed": 0,
					"availablePrepaidLeft": 297,
					"availablePrepaidLeftBaseCurrency": 298,
					"prepaidUsageUsed": 0,
					"availablePrepaidUsageLeft": 0,
					"availablePrepaidLeftInCredits": null,
					"invoiceSalesTax": null,
					"availablePayAsYouGoMoney": 299,
					"availablePayAsYouGoMoneyInBaseCurrency": 363.*************,
					"availablePayAsYouGoMoneyInCredits": null,
					"paymentCurrencyInfo": {
						"basePlanCurrency": "USD",
						"paymentCurrency": "USD",
						"exchangeRate": 1
					},
					"paymentMethod": null,
					"messages": null,
					"invoiceStartTime": "2024-08-30T00:00:00Z",
					"invoiceEndTime": "2024-09-30T00:00:00Z",
					"voided": false
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	credit, err := svc.GetCredit(context.Background(), "test-id", false)
	assert.Nil(t, err)
	assert.Equal(t, credit, 363.*************)

	// Test invalid response body.
	httpmock.RegisterResponder("GET", urlPath, httpmock.NewStringResponder(200, "Invalid Response"))
	credit, err = svc.GetCredit(context.Background(), "test-id", false)
	assert.NotNil(t, err)
	assert.Equal(t, credit, 0.0)

	// Test missing 'availablePayAsYouGoMoneyInBaseCurrency' field.
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"invoiceUri": "payments/invoices/account_id=32306/customer_id=org_ZBVh66RyLFwjjVAZ/product_id=1/product_plan_id=9131fc0f-e211-4df9-b4df-1382adc12506/year=2024/month=08/day=30",
					"uuid": "f4191dc1-85c9-32c3-b9c3-dedeb4b2f778",
					"invoiceKey": {
						"accountId": "32306",
						"customerId": "org_ZBVh66RyLFwjjVAZ",
						"productId": "1",
						"productPlanId": "9131fc0f-e211-4df9-b4df-1382adc12506",
						"year": 2024,
						"month": 8,
						"day": 30
					},
					"planBillingPeriod": {
						"interval": "month",
						"intervalsCount": 1
					},
					"planName": "Default-Pricing-Plan",
					"invoiceStartTimeInSeconds": **********,
					"invoiceEndTimeInSeconds": **********,
					"gracePeriodInHours": 72,
					"productItemInvoices": [],
					"appliedPromotions": [],
					"appliedPrepaids": [],
					"productPlanFees": [],
					"appliedInvoiceBasedFees": [],
					"appliedCommitments": [],
					"itemSubscriptionDetails": null,
					"totalBill": {
						"itemPrice": 0,
						"fixPrice": 0,
						"invoiceBasedFees": 0,
						"commitmentsFees": 0,
						"prepaid": 0,
						"totalDiscount": 0,
						"totalPriceBeforeDiscount": 0,
						"totalPriceBeforePrepaid": 0,
						"totalPriceBeforeCommitments": 0,
						"totalPriceBeforeSalesTax": 0,
						"discountableFixPrice": 0,
						"discountableBasePrice": 0,
						"prepayableFixPrice": 0,
						"prepayableNotDiscountableFees": 0,
						"prepayableCommitmentsFees": 0,
						"totalPrepayablePrice": 0,
						"startTimeInSeconds": **********,
						"endTimeInSeconds": **********,
						"totalPrice": 0
					},
					"invoicePriceStatus": "open",
					"creditUnit": null,
					"paymentStatus": "pre_payment",
					"paymentCreatedInSeconds": null,
					"externalSystemStatus": null,
					"invoiceBillInBaseCurrency": {
						"itemPrice": 0,
						"fixPrice": 0,
						"invoiceBasedFees": 0,
						"commitmentsFees": 0,
						"prepaid": 0,
						"totalDiscount": 0,
						"totalPriceBeforeDiscount": 0,
						"totalPriceBeforePrepaid": 0,
						"totalPriceBeforeCommitments": 0,
						"totalPriceBeforeSalesTax": 0,
						"discountableFixPrice": 0,
						"discountableBasePrice": 0,
						"prepayableFixPrice": 0,
						"prepayableNotDiscountableFees": 0,
						"prepayableCommitmentsFees": 0,
						"totalPrepayablePrice": 0,
						"startTimeInSeconds": **********,
						"endTimeInSeconds": **********,
						"totalPrice": 0
					},
					"invoiceBillInCredits": null,
					"prepaidPriceUsedBaseCurrency": 297,
					"prepaidPriceUsed": 0,
					"availablePrepaidLeft": 298,
					"prepaidUsageUsed": 0,
					"availablePrepaidUsageLeft": 0,
					"availablePrepaidLeftInCredits": null,
					"invoiceSalesTax": null,
					"availablePayAsYouGoMoney": 299,
					"availablePayAsYouGoMoneyInCredits": null,
					"paymentCurrencyInfo": {
						"basePlanCurrency": "USD",
						"paymentCurrency": "USD",
						"exchangeRate": 1
					},
					"paymentMethod": null,
					"messages": null,
					"invoiceStartTime": "2024-08-30T00:00:00Z",
					"invoiceEndTime": "2024-09-30T00:00:00Z",
					"voided": false
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	credit, err = svc.GetCredit(context.Background(), "test-id", false)
	assert.Nil(t, err)
	assert.Equal(t, credit, 0.0)
}

func TestAddCredit(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-prepaid")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				{
					"type": "detailed_customer_prepaid_by_price",
					"purchaseType": "prepaid_by_price",
					"internalStatus": "Prepaid card was ordered but not paid for yet. - invoiced - waits for external payment confirmation",
					"prepaidPaymentTimeInSeconds": null,
					"paymentId": null,
					"paymentStatus": "requires_action",
					"firstInvoiceUri": "payments/invoices/prepaid/account_id=32068/customer_id=org_Y39lPCviUJi30p90/product_id=1/prepaid_id=test-id/time_in_seconds=**********",
					"modifiedTimeInSeconds": **********,
					"id": "test-id",
					"customerId": "org_Y39lPCviUJi30p90",
					"startTimeInSeconds": **********,
					"endTimeInSeconds": **********,
					"firstCardEndTimeSeconds": **********,
					"productId": "1",
					"prepaidOfferVersion": *************,
					"prepaidPrice": 30,
					"originalWorth": 30,
					"originalCurrency": "USD",
					"recurrenceFrequency": null,
					"externalPayment": true,
					"relationId": "eyJpZCI6InRlc3QtaWQiLCJjdXN0b21lcklkIjoib3JnX1kzOWxQQ3ZpVUppMzBwOTAiLCJwcm9kdWN0SWQiOiIxIn0=",
					"createdTimeInSeconds": **********,
					"label": null,
					"prepaidConditions": null,
					"commitmentId": null,
					"orderId": null,
					"orderItemId": null,
					"senderPrepaidUri": null,
					"customPriority": 6,
					"prepaidPriority": null,
					"startAtMidCycle": null
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/external/prepaid-payment-status")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			body, err := io.ReadAll(req.Body)
			if err != nil {
				t.Errorf("Error reading request body: %v", err)
			}

			reqInput := &UpdateCreditRequest{}
			err = json.Unmarshal(body, reqInput)
			if err != nil {
				t.Errorf("Error unmarshaling: %v", err)
			}

			if reqInput.PrepaidURI != "payments/invoices/prepaid/account_id=32068/customer_id=org_Y39lPCviUJi30p90/product_id=1/prepaid_id=test-id/time_in_seconds=**********" {
				t.Errorf("Invalid PrepaidURI received: %s", reqInput.PaymentID)
			}

			resp := `
				{
					"type": "prepaid_card_by_price",
					"purchaseType": "prepaid_by_price",
					"prepaidUri": "payments/invoices/prepaid/account_id=32068/customer_id=org_Y39lPCviUJi30p90/product_id=1/prepaid_id=test-id/time_in_seconds=**********",
					"paymentStatus": "settled",
					"price": 30,
					"worth": 30,
					"sourceUri": "payments/pricing/customers/account_id=32068/customer_id=org_Y39lPCviUJi30p90",
					"paymentSystemName": "external_credit",
					"paymentId": "test-id",
					"startTimeInSeconds": **********,
					"expirationTimeInSeconds": **********,
					"productInvoices": [],
					"transferInvoices": [],
					"usedAmount": 0,
					"amountLeft": 30,
					"cardType": "external_one_time",
					"paymentCurrencyInfo": {
						"basePlanCurrency": "USD",
						"paymentCurrency": "USD",
						"exchangeRate": 1
					},
					"prepaidConditions": null,
					"label": null,
					"commitmentId": null,
					"customPriority": 6,
					"startAtMidCycle": null,
					"orderId": null,
					"orderItemId": null
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	AddCreditRequest := &pb.AddCreditRequest{
		CustomerID:   "org_Y39lPCviUJi30p90",
		ID:           "test-id",
		PrepaidPrice: 10,
	}
	err := svc.AddCredit(context.Background(), AddCreditRequest)
	assert.Nil(t, err)
}

func TestApplyOnboardingTaskDiscount(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-org-id") {
				t.Errorf("Missing query param 'customerId=test-org-id")
			}
			resp := `
				{
					"invoiceStartTimeInSeconds": **********,
					"invoiceEndTimeInSeconds": **********,
					"invoicePriceStatus": "open",
					"paymentStatus": "pre_payment",
					"availablePayAsYouGoMoneyInBaseCurrency": 363.*************,
					"voided": false
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/promotions/list")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				[
					{
						"targetProductId": "1",
						"promotionTimeLimit": {
							"cycles": 0,
							"months": 0
						},
						"discount": 50,
						"totalMaxDiscount": 50,
						"id": "d98021d1-e553-4d69-9761-a8b97ec57600",
						"type": "time_limited_absolute_product_discount",
						"promotionName": "Sign up for TigerGraph Savanna",
						"description": "d720b0c8-359c-443e-8adf-6e6db1b7a23b",
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": *************
					},
					{
						"targetProductId": "1",
						"promotionTimeLimit": {
							"cycles": 0,
							"months": 0
						},
						"discount": 5,
						"totalMaxDiscount": 5,
						"id": "db7626d6-d28a-45a0-a9aa-90d4205bffba",
						"type": "time_limited_absolute_product_discount",
						"promotionName": "Load data",
						"description": "3b72e05a-be10-4518-9143-93530f8ebbb1",
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": 1728509631289
					}
				]`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-promotions")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			body, err := io.ReadAll(req.Body)
			if err != nil {
				t.Errorf("Error reading request body: %v", err)
			}

			reqInput := &ApplyDiscountRequest{}
			err = json.Unmarshal(body, reqInput)
			if err != nil {
				t.Errorf("Error unmarshaling: %v", err)
			}

			if reqInput.PromotionID != "db7626d6-d28a-45a0-a9aa-90d4205bffba" {
				t.Errorf("Incorrect PromotionID received: %s", reqInput.PromotionID)
			}

			return httpmock.NewBytesResponse(200, []byte{}), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	err := svc.ApplyOnboardingTaskDiscount(context.Background(), "test-org-id", "3b72e05a-be10-4518-9143-93530f8ebbb1")
	assert.Nil(t, err)

	// Discount not found for onboarding task ID.
	err = svc.ApplyOnboardingTaskDiscount(context.Background(), "test-org-id", "3b72e05a-be10-4518-9143-93530f8ebbb2")
	assert.NotNil(t, err)
	assert.Equal(t, err.Error(), "discount not found for onboarding task ID: 3b72e05a-be10-4518-9143-93530f8ebbb2")
}
