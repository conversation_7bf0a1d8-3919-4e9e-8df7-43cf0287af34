package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
	"gorm.io/gorm"
)

// Package variable so we can stub for testing.
var timeNow = time.Now

type Invoice struct {
	InvoicePriceStatus                     string  `json:"invoicePriceStatus"`
	PaymentStatus                          string  `json:"paymentStatus"`
	Voided                                 bool    `json:"voided"`
	InvoiceStartTimeInSeconds              int64   `json:"invoiceStartTimeInSeconds"`
	InvoiceEndTimeSeconds                  int64   `json:"invoiceEndTimeInSeconds"`
	AvailablePayAsYouGoMoneyInBaseCurrency float64 `json:"availablePayAsYouGoMoneyInBaseCurrency"`
}

func (svc *BillingManagerService) PaymentValidation(ctx context.Context, customerID string, fromCache bool) (canPay bool, err error) {
	log := logger.L().WithContext(ctx)
	if svc.cfg.DeveloperMode {
		return true, nil
	}

	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return false, fmt.Errorf("Failed to retrieve customer information: %v", err)
	}

	unpaidInvoice, err := svc.CheckUnpaidInvoices(ctx, customerID, fromCache, 0)
	if err != nil {
		// We only log the error if there is an issue checking customer invoices, so that the user
		// will not be blocked, if all other payment validation checks pass. When Amberflo error
		// resolves itself, our delinquent resource cleaner will auto-stop resources if they are delinquent.
		log.Errorf("Failed to check customer invoices: %v", err)
	}

	if unpaidInvoice {
		log.Info("Customer [id=%s] has unpaid invoices", customerID)
		return false, nil
	}

	activeMarketplaceSubscription := false
	marketplaceCustomer, err := svc.db.GetAWSMarketplaceInfoByCustomerID(customerID)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("Failed to retrieve customer maretplace information: %v", err)
		}

	}

	if marketplaceCustomer != nil && marketplaceCustomer.Status == constants.MarketplaceStatusActive {
		activeMarketplaceSubscription = true
	}

	if customerInfo.HasValidCard || activeMarketplaceSubscription {
		return true, nil
	}

	credits, err := svc.GetCredit(ctx, customerID, false)
	if err != nil {
		// We log the error and return true if there is an issue checking customer credits, so that the user
		// will not be blocked. When Amberflo error resolves itself, our delinquent resource cleaner
		// will auto-stop resources if they are delinquent.
		log.Errorf("Failed to retrieve customer [id=%s] credits: %v", customerID, err)
		return true, nil
	}

	if credits > 0 {
		return true, nil
	}

	log.Info("Customer [id=%s] has no valid credit card or credits left", customerID)
	return false, nil
}

// GetLatestInvoice retrieves the latest invoice for a specified customer.
// 'cache' flag is used to specify realtime (Amberflo Usage API cost) or cached result(2-3 hour delay).
func (svc *BillingManagerService) GetLatestInvoice(ctx context.Context, customerID string, cache bool) (*Invoice, error) {
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{
			"customerId": customerID,
			"productId":  "1",
			"fromCache":  strconv.FormatBool(cache),
			"latest":     "true",
		},
		Client: svc.client,
	}

	body, err := rest.Get(&reqInput)
	if err != nil {
		return nil, fmt.Errorf("Failed to retrieve customer(%s) latest invoice: %v", customerID, err)
	}

	invoice := &Invoice{}
	if err = json.Unmarshal(body, invoice); err != nil {
		return nil, err
	}

	return invoice, nil
}

// checkUnpaidInvoices retrieves a list of invoices for a specified customer and checks for unpaid invoices.
// 'cache' flag is used to specify realtime (Amberflo Usage API cost) or cached result(2-3 hour delay).
// 'gracePeriod' is the number of days since the closing of an unpaid invoice before we consider it delinquent.
func (svc *BillingManagerService) CheckUnpaidInvoices(ctx context.Context, customerID string, cache bool, gracePeriod int) (unpaidInvoice bool, err error) {
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice/all"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{
			"customerId": customerID,
			"productId":  "1",
			"fromCache":  strconv.FormatBool(cache),
		},
		Client: svc.client,
	}

	body, err := rest.Get(&reqInput)
	if err != nil {
		return false, fmt.Errorf("Failed to retrieve customer(%s) invoices: %v", customerID, err)
	}

	invoices := []Invoice{}
	if err = json.Unmarshal(body, &invoices); err != nil {
		return false, err
	}

	for _, invoice := range invoices {
		if svc.isUnpaidInvoice(invoice) {
			if gracePeriod == 0 {
				return true, nil
			} else {
				epochTime := time.Unix(invoice.InvoiceEndTimeSeconds, 0)
				if timeNow().After(epochTime.AddDate(0, 0, gracePeriod)) {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (svc *BillingManagerService) isUnpaidInvoice(invoice Invoice) bool {
	//  Conditions for an unpaid invoice:
	//  1. Invoice is not voided.
	//  2. Invoice price is locked.
	//  3. Payment status is requires_action or failed.
	return !invoice.Voided &&
		strings.EqualFold(invoice.InvoicePriceStatus, "price_locked") &&
		(strings.EqualFold(invoice.PaymentStatus, "requires_action") || strings.EqualFold(invoice.PaymentStatus, "failed"))
}
