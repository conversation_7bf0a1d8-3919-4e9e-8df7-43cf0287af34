package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/controller/middleware"
	"github.com/tigergraph/cloud-universe/utils/rest"
	"github.com/tigergraph/cloud-universe/utils/typeconv"
	"gorm.io/gorm"
)

type mockDB struct {
	mock.Mock
	database.BillingManagerDB
}

func (m *mockDB) CreateCustomerInfo(customerID string, email string, name string, stripeID string) error {
	args := m.Called(customerID, email, name, stripeID)
	return args.Error(0)
}

func (m *mockDB) GetCustomerInfo(customerID string) (*database.CustomerInfo, error) {
	args := m.Called(customerID)

	customerInfo := args.Get(0)
	if customerInfo == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*database.CustomerInfo), args.Error(1)
}

func (m *mockDB) UpdateHasValidCard(customerID string, hasValidCard bool) error {
	args := m.Called(customerID, hasValidCard)
	return args.Error(0)
}

func (m *mockDB) GetAWSMarketplaceInfoByCustomerID(customerID string) (*database.AWSMarketplaceInfo, error) {
	args := m.Called(customerID)

	awsMarketplaceInfo := args.Get(0)
	if awsMarketplaceInfo == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*database.AWSMarketplaceInfo), args.Error(1)
}

func (m *mockDB) UpdateCustomerSuspendGracePeriod(customerID string, gracePeriod int) error {
	args := m.Called(customerID, gracePeriod)
	return args.Error(0)
}

func (m *mockDB) UpdateCustomerTerminateGracePeriod(customerID string, gracePeriod int) error {
	args := m.Called(customerID, gracePeriod)
	return args.Error(0)
}

func TestCreateCustomer(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Create customer.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				{
					"id": "org_alNmd7BDhpdrZRPc",
					"createTime": 1714427897355,
					"updateTime": 1714427897355,
					"customerId": "org_alNmd7BDhpdrZRPc",
					"customerName": "steven-test",
					"customerEmail": "<EMAIL>",
					"enabled": true,
					"test": false,
					"lifecycleStage": "active",
					"traits": {
						"stripeId": "cus_FMBwESk0sPiO9h",
						"paymentProviderName": "STRIPE"
					}
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// No existing Amberflo customer response.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=org_alNmd7BDhpdrZRPc") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := "{}"
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// List pricing plans.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans/list")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				[
					{
					    "id": "4103f207-d037-45f4-bad8-580c72236994",
						"productId": "1",
						"productItemPriceIdsMap": {
						"4880d440-1955-49c5-999f-eb60cfcb3ea2": "a74eab78-9ca6-40be-902b-8962dcabde15",
						"1005b6f2-85fc-4460-9f53-926f6160ee3e": "5d687a48-4d9d-4c71-a3d0-aa5ea08f2525"
						},
						"billingPeriod": {
						"interval": "month",
						"intervalsCount": 1
						},
						"planLevelFreeTier": null,
						"invoiceBasedFeeIds": null,
						"planMinimumPayment": null,
						"minimumPaymentGranularity": null,
						"productPlanName": "Pricing-Plan-Placeholder",
						"description": "",
						"lastUpdateTimeInMillis": *************,
						"feeMap": {},
						"entitlements": null,
						"lockingStatus": "close_to_changes",
						"isDefault": true,
						"successorPlanId": null,
						"transitionStrategy": null,
						"type": "pure_usage_template",
						"prepaidBuyingRules": null,
						"planGenerator": null
					}
				]`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Assign customer a pricing plan.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-pricing")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			body, err := io.ReadAll(req.Body)
			if err != nil {
				t.Errorf("Error reading request body: %v", err)
			}

			reqInput := &assignCustomerPricingPlanRequest{}
			err = json.Unmarshal(body, reqInput)
			if err != nil {
				t.Errorf("Error unmarshaling: %v", err)
			}

			if reqInput.ProductPlanID != "4103f207-d037-45f4-bad8-580c72236994" {
				t.Errorf("Invalid Product Plan ID received: %s", reqInput.ProductPlanID)
			}

			if reqInput.StartTimeInSeconds != 1708012800 {
				t.Errorf("Invalid start time received: %d", reqInput.StartTimeInSeconds)
			}

			resp := `
				{
					"productId": "1",
					"productPlanId": "4103f207-d037-45f4-bad8-580c72236994",
					"customerId": "org_alNmd7BDhpdrZRPc",
					"startTimeInSeconds": 1714348800,
					"endTimeInSeconds": null,
					"relationId": "eyJwcm9kdWN0SWQiOiIxIiwicHJvZHVjdFBsYW5JZCI6IjQxMDNmMjA3LWQwMzctNDVmNC1iYWQ4LTU4MGM3MjIzNjk5NCIsImN1c3RvbWVySWQiOiJvcmdfYWxObWQ3QkRocGRyWlJQYyIsInN0YXJ0VGltZUluU2Vjb25kcyI6MTcxNDM0ODgwMH0=",
					"createdTimeInSeconds": 1714428447
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	mockDB := &mockDB{}
	mockDB.On("CreateCustomerInfo", "org_alNmd7BDhpdrZRPc", "<EMAIL>", "steven-test", "cus_FMBwESk0sPiO9h").Return(nil)
	mockDB.On("GetCustomerInfo", "org_alNmd7BDhpdrZRPc").Return(nil, gorm.ErrRecordNotFound)
	mockDB.On("UpdateHasValidCard", "org_alNmd7BDhpdrZRPc", true).Return(nil)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
			DeveloperMode:  false,
		},
		db:     mockDB,
		client: client,
	}

	createCustomerRequest := &pb.CreateCustomerRequest{
		CustomerID:    "org_alNmd7BDhpdrZRPc",
		CustomerName:  "steven-test",
		CustomerEmail: "<EMAIL>",
		HasValidCard:  true,
		Enabled:       true,
		Traits: map[string]string{
			"paymentProviderName": "STRIPE",
		},
	}

	_, err := svc.CreateCustomer(context.Background(), createCustomerRequest)
	assert.NotNil(t, err)

	createCustomerRequest.Traits["stripeId"] = "cus_FMBwESk0sPiO9h"
	_, err = svc.CreateCustomer(context.Background(), createCustomerRequest)
	assert.Nil(t, err)
}

func TestGetCustomer(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"id": "test-id",
					"createTime": 1711419335833,
					"updateTime": 1711419335833,
					"customerId": "test-id",
					"customerName": "test-name",
					"customerEmail": "<EMAIL>",
					"enabled": true,
					"test": false,
					"lifecycleStage": "active",
					"traits": {
						"paymentProviderName": "STRIPE",
						"stripeId": "cus_I65eFupvE77rFP"
					}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	resp, err := svc.getCustomer("test-id")
	assert.NoError(t, err)
	assert.Equal(t, resp.CustomerID, "test-id")
}

func TestUpdateCustomer(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"customerId": "test-id",
					"customerName": "test-name",
					"customerEmail": "<EMAIL>",
					"traits": {
						"paymentProviderName": "STRIPE",
						"stripeId": "cus_I65eFupvE77rFP"
					}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	httpmock.RegisterResponder("PUT", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				{
					"id": "test-id",
					"createTime": 1711419335833,
					"updateTime": 1711419335833,
					"customerId": "test-id",
					"customerName": "test-org",
					"customerEmail": "<EMAIL>",
					"enabled": true,
					"test": false,
					"lifecycleStage": "active",
						"traits": {
							"paymentProviderName": "STRIPE",
							"stripeId": "cus_I65eFupvE77rFP"
						}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	request := &pb.UpdateCustomerRequest{}
	request.CustomerEmail = typeconv.String("<EMAIL>")
	_, err := svc.UpdateCustomer(context.Background(), "test-id", request)
	assert.NoError(t, err)
}

func TestGetDefaultPricingPlan(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans/list")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				[
					{
						"id": "0e3a35fd-1c89-4748-a276-1b256bc1ea06",
						"productId": "1",
						"productItemPriceIdsMap": {
						"ed60325d-3261-48a6-8bf9-c9e48e56d935": "10a17b82-de33-47a6-8062-87f598f47c0c",
						"33267a11-2af9-4e89-8acb-8ffff19a6e20": "ed98a0fa-304f-4e64-9b6d-b334f1e32156",
						"4ceced58-4a93-494d-ac09-243b97b2303f": "4b377730-f119-4651-9186-b635f10544ed"
						},
						"billingPeriod": {
						"interval": "month",
						"intervalsCount": 1
						},
						"planLevelFreeTier": null,
						"invoiceBasedFeeIds": null,
						"planMinimumPayment": null,
						"minimumPaymentGranularity": null,
						"productPlanName": "TGCloud-********",
						"description": "Default pricing plan for customers",
						"lastUpdateTimeInMillis": 1714002311148,
						"feeMap": {},
						"entitlements": null,
						"lockingStatus": "close_to_changes",
						"isDefault": true,
						"successorPlanId": null,
						"transitionStrategy": null,
						"type": "pure_usage_template",
						"prepaidBuyingRules": null,
						"planGenerator": null
					}
				]`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	id, err := svc.getDefaultPricingPlan()
	assert.NoError(t, err)
	assert.Equal(t, id, "0e3a35fd-1c89-4748-a276-1b256bc1ea06")
}

func TestSwitchPaymentMethod(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Get customer.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"id": "test-id",
					"createTime": 1711419335833,
					"updateTime": 1711419335833,
					"customerId": "test-id",
					"customerName": "test-name",
					"customerEmail": "<EMAIL>",
					"enabled": true,
					"test": false,
					"lifecycleStage": "active",
					"traits": {
						"paymentProviderName": "STRIPE",
						"stripeId": "cus_I65eFupvE77rFP"
					}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Switch payment method.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "customers/payment-method/switch")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			body, err := io.ReadAll(req.Body)
			if err != nil {
				t.Errorf("Error reading request body: %v", err)
			}
			defer req.Body.Close()

			expectedBody := `{"customerId":"test-id","sourcePaymentType":"STRIPE","sourcePaymentId":"stripe-account-id","targetPaymentType":"AWS_MARKETPLACE","targetPaymentId":"aws-amberflo","targetCustomerIdentifier":"aws-sm-id","switchTimeInSeconds":**********}`

			if string(body) != expectedBody {
				t.Errorf("Expected body: %s, got: %s", expectedBody, string(body))
			}

			return httpmock.NewBytesResponse(200, []byte{}), nil
		},
	)

	mockDB := &mockDB{}

	marketplaceInfo := &database.AWSMarketplaceInfo{
		CustomerID:    "test-id",
		MarketplaceID: "aws-sm-id",
	}
	mockDB.On("GetAWSMarketplaceInfoByCustomerID", mock.Anything).Return(marketplaceInfo, nil)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey:  testAPIKey,
			StripeAccountID: "stripe-account-id",
			Marketplace: config.MarketplaceConfig{
				AmberfloID: "aws-amberflo",
			},
		},
		db:     mockDB,
		client: client,
	}

	ac := middleware.AuthContext{
		OrgID: "test-id",
	}

	ctx := context.WithValue(context.Background(), middleware.CtxKeyAuthContext, ac)

	in := &pb.SwitchPaymentMethodRequest{
		TargetPaymentMethod: constants.PaymentMethodAWSMarketplace,
	}
	err := svc.SwitchPaymentMethod(ctx, in)
	assert.NoError(t, err)
}

func TestUpdateCustomerSuspendGracePeriod(t *testing.T) {
	mockDB := &mockDB{}
	mockDB.On("UpdateCustomerSuspendGracePeriod", mock.Anything, mock.Anything).Return(nil)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		db: mockDB,
	}

	err := svc.UpdateCustomerSuspendGracePeriod(context.Background(), "test-id", 30)
	assert.NoError(t, err)
}

func TestUpdateCustomerTerminateGracePeriod(t *testing.T) {
	mockDB := &mockDB{}
	mockDB.On("UpdateCustomerTerminateGracePeriod", mock.Anything, mock.Anything).Return(nil)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		db: mockDB,
	}

	err := svc.UpdateCustomerTerminateGracePeriod(context.Background(), "test-id", 30)
	assert.NoError(t, err)
}

func TestGetBillingStatus(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "customers")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "customerId=test-id") {
				t.Errorf("Missing query param 'customerId=test-id")
			}
			resp := `
				{
					"id": "test-id",
					"createTime": 1711419335833,
					"updateTime": 1711419335833,
					"customerId": "test-id",
					"customerName": "test-name",
					"customerEmail": "<EMAIL>",
					"enabled": true,
					"test": false,
					"lifecycleStage": "active",
					"traits": {
						"paymentProviderName": "STRIPE",
						"stripeId": "cus_I65eFupvE77rFP"
					}
				}
				`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Test active marketplace status.
	mockDB := &mockDB{}
	mockDB.On("GetAWSMarketplaceInfoByCustomerID", mock.Anything).Return(&database.AWSMarketplaceInfo{
		CustomerID: "test-id",
		Status:     constants.MarketplaceStatusActive,
	}, nil).Once()

	svc := &BillingManagerService{
		db: mockDB,
	}

	status, err := svc.GetBillingStatus(context.Background(), "test-id")
	assert.NoError(t, err)
	assert.Equal(t, constants.MarketplaceStatusActive, status.AWS)
	assert.Equal(t, constants.MarketplaceStatusNoAssociation, status.Azure)
	assert.Equal(t, constants.MarketplaceStatusNoAssociation, status.GCP)
	assert.Equal(t, constants.PaymentMethodStripe, status.PaymentMethod)

	// Test no marketplace association.
	mockDB.On("GetAWSMarketplaceInfoByCustomerID", mock.Anything).Return(nil, gorm.ErrRecordNotFound).Once()
	status, err = svc.GetBillingStatus(context.Background(), "test-id")
	assert.NoError(t, err)
	assert.Equal(t, constants.MarketplaceStatusNoAssociation, status.AWS)
	assert.Equal(t, constants.MarketplaceStatusNoAssociation, status.Azure)
	assert.Equal(t, constants.MarketplaceStatusNoAssociation, status.GCP)
	assert.Equal(t, constants.PaymentMethodStripe, status.PaymentMethod)
}
