package service

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const layout = "2006-01-02 15:04:05"

//nolint:gochecknoinits // for test
func init() {
	timeNow = func() time.Time {
		t, _ := time.Parse(layout, "2024-02-15 16:13:05")
		return t
	}
}

func TestCheckUnpaidInvoices(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Get all invoices.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice/all")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			queryParams := req.URL.Query()
			customerID := queryParams.Get("customerId")
			var resp string

			if customerID == "customer1" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "failed",
						"voided": false
					}
				]`
			} else if customerID == "customer2" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "settled",
						"voided": false
					}
				]`
			} else if customerID == "customer3" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "require_action",
						"voided": true
					}
				]`

			}
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	// Customer1 has unpaid invoice.
	unpaidInvoice, err := svc.CheckUnpaidInvoices(context.Background(), "customer1", true, 0)
	assert.NoError(t, err)
	assert.True(t, unpaidInvoice)

	// Customer1 has unpaid invoice, but invoice date is within grace period.
	unpaidInvoice, err = svc.CheckUnpaidInvoices(context.Background(), "customer1", true, 15)
	assert.NoError(t, err)
	assert.False(t, unpaidInvoice)

	// Customer2 has no unpaid invoice.
	unpaidInvoice, err = svc.CheckUnpaidInvoices(context.Background(), "customer2", true, 0)
	assert.NoError(t, err)
	assert.False(t, unpaidInvoice)

	// Customer3 has unpaid invoice, but invoice is voided.
	unpaidInvoice, err = svc.CheckUnpaidInvoices(context.Background(), "customer3", true, 0)
	assert.NoError(t, err)
	assert.False(t, unpaidInvoice)
}

func TestPaymentValidation(t *testing.T) {
	mockDB := &mockDB{}
	customer1 := &database.CustomerInfo{
		CustomerID:   "customer1",
		HasValidCard: true,
	}

	customer2 := &database.CustomerInfo{
		CustomerID: "customer2",
	}

	customer3 := &database.CustomerInfo{
		CustomerID: "customer3",
	}

	mockDB.On("GetCustomerInfo", "customer1").Return(customer1, nil)
	mockDB.On("GetCustomerInfo", "customer2").Return(customer2, nil)
	mockDB.On("GetCustomerInfo", "customer3").Return(customer3, nil)

	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			queryParams := req.URL.Query()
			customerID := queryParams.Get("customerId")
			var resp string
			statusCode := 200

			if customerID == "customer1" || customerID == "customer2" {
				resp := `
				{
					"availablePayAsYouGoMoneyInBaseCurrency": 1,
				}`
				return httpmock.NewBytesResponse(200, []byte(resp)), nil
			} else if customerID == "customer3" {
				resp = ""
				statusCode = 400
			}

			return httpmock.NewBytesResponse(statusCode, []byte(resp)), nil
		},
	)

	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/billing/customer-product-invoice/all")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			queryParams := req.URL.Query()
			customerID := queryParams.Get("customerId")
			var resp string
			statusCode := 200

			if customerID == "customer1" || customerID == "customer3" {
				resp = `[
					{
						"invoiceStartTimeInSeconds": 1704931200,
						"invoiceEndTimeInSeconds": 1707609600,
						"invoicePriceStatus": "price_locked",
						"paymentStatus": "settled"
					}
				]`
			} else if customerID == "customer2" {
				resp = ""
				statusCode = 400
			}
			return httpmock.NewBytesResponse(statusCode, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg:    &config.Config{},
		client: client,
		db:     mockDB,
	}

	// Customer1 has valid card
	canPay, err := svc.PaymentValidation(context.Background(), "customer1", false)
	assert.True(t, canPay)
	assert.NoError(t, err)

	// Amberflo list invoices returns error for customer2, but customer2 has credit balance.
	canPay, err = svc.PaymentValidation(context.Background(), "customer2", false)
	assert.True(t, canPay)
	assert.NoError(t, err)

	// Amberflo credit API returns error, but we should not block customer on this error.
	canPay, err = svc.PaymentValidation(context.Background(), "customer3", false)
	assert.True(t, canPay)
	assert.NoError(t, err)
}
