package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/controller/middleware"
	"github.com/tigergraph/cloud-universe/utils/rest"
	"gorm.io/gorm"
)

var ErrCustomerNotFound = errors.New("Customer not found")

type CreateCustomerRequest struct {
	CustomerID    string            `json:"customerId"`
	CustomerName  string            `json:"customerName"`
	CustomerEmail string            `json:"customerEmail"`
	Enabled       bool              `json:"enabled"`
	Traits        map[string]string `json:"traits"`
}

func (svc *BillingManagerService) CreateCustomer(ctx context.Context, request *pb.CreateCustomerRequest) (*pb.CustomerResponse, error) {
	_, ok := request.Traits["stripeId"]
	if !ok {
		return nil, fmt.Errorf("stripeId not found in \"traits\"")
	}

	_, err := svc.getCustomer(request.CustomerID)
	if err != nil {
		if err == ErrCustomerNotFound {
			body := CreateCustomerRequest{
				CustomerID:    request.CustomerID,
				CustomerName:  request.CustomerName,
				CustomerEmail: request.CustomerEmail,
				Enabled:       request.Enabled,
				Traits:        request.Traits,
			}

			reqInput := rest.RequestInput{
				URL:     fmt.Sprintf("%s%s", amberfloURL, "customers"),
				Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
				Body:    body,
				Client:  svc.client,
			}

			_, err = rest.Post(&reqInput)
			if err != nil {
				return nil, fmt.Errorf("Failed to create customer in Amberflo: %v", err)
			}
		} else {
			return nil, err
		}
	}

	_, err = svc.db.GetCustomerInfo(request.CustomerID)
	if err == gorm.ErrRecordNotFound {
		err = svc.db.CreateCustomerInfo(request.CustomerID, request.CustomerEmail, request.CustomerName, request.Traits["stripeId"])
		if err != nil {
			return nil, fmt.Errorf("Unable to create customer info record: %v", err)
		}
	}

	if request.HasValidCard {
		err = svc.db.UpdateHasValidCard(request.CustomerID, true)
		if err != nil {
			return nil, fmt.Errorf("Failed to update customer info record: %v", err)
		}
	}

	pricingPlan, err := svc.getDefaultPricingPlan()
	if err != nil {
		return nil, err
	}

	err = svc.assignCustomerPricingPlan(request.CustomerID, pricingPlan)
	if err != nil {
		return nil, err
	}

	return &pb.CustomerResponse{CustomerID: request.CustomerID}, nil
}

type CustomerResponse struct {
	CustomerID    string            `json:"customerId"`
	CustomerName  string            `json:"customerName"`
	CustomerEmail string            `json:"customerEmail"`
	Enabled       bool              `json:"enabled"`
	Traits        map[string]string `json:"traits"`
}

// getCustomer retrieves the Amberflo customer.
func (svc *BillingManagerService) getCustomer(customerID string) (*CustomerResponse, error) {
	reqInput := rest.RequestInput{
		URL:         fmt.Sprintf("%s%s", amberfloURL, "customers"),
		QueryParams: map[string]string{"customerId": customerID},
		Headers:     map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Client:      svc.client,
	}

	body, err := rest.Get(&reqInput)
	if err != nil {
		return nil, fmt.Errorf("Unable to get customer info from Amberflo: %v", err)
	}

	custResp := &CustomerResponse{}
	err = json.Unmarshal(body, custResp)
	if err != nil {
		return nil, fmt.Errorf("Failed to unmarshal response: %v", err)
	}

	if custResp.CustomerID == "" {
		return nil, ErrCustomerNotFound
	}

	return custResp, nil
}

type UpdateCustomerRequest struct {
	CustomerID    string            `json:"customerId"`
	CustomerName  string            `json:"customerName"`
	CustomerEmail string            `json:"customerEmail"`
	Traits        map[string]string `json:"traits"`
}

// UpdateCustomer updates the Amberflo customer info.
func (svc *BillingManagerService) UpdateCustomer(ctx context.Context, customerID string, request *pb.UpdateCustomerRequest) (*pb.CustomerResponse, error) {
	customerResp, err := svc.getCustomer(customerID)
	if err != nil {
		return nil, err
	}

	reqBody := &UpdateCustomerRequest{
		CustomerID:    customerID,
		CustomerName:  customerResp.CustomerName,
		CustomerEmail: customerResp.CustomerEmail,
		Traits:        customerResp.Traits,
	}

	if request.CustomerEmail != nil {
		reqBody.CustomerEmail = *request.CustomerEmail
	}

	if request.CustomerName != nil {
		reqBody.CustomerName = *request.CustomerName
	}

	if request.Traits != nil {
		reqBody.Traits = *request.Traits
	}

	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "customers"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    reqBody,
		Client:  svc.client,
	}

	_, err = rest.Put(&reqInput)
	if err != nil {
		return nil, fmt.Errorf("Failed to update Amberflo customer: %v", err)
	}

	if request.CustomerName != nil {
		err := svc.db.UpdateCustomerInfoName(customerID, *request.CustomerName)
		if err != nil {
			return nil, fmt.Errorf("Failed to update customer info record: %v", err)
		}
	}

	return &pb.CustomerResponse{CustomerID: customerID}, nil
}

func (svc *BillingManagerService) UpdateCustomerSuspendGracePeriod(ctx context.Context, customerID string, gracePeriod int) error {
	err := svc.db.UpdateCustomerSuspendGracePeriod(customerID, gracePeriod)
	if err != nil {
		return fmt.Errorf("Failed to update customer grace period: %v", err)
	}

	return nil
}

func (svc *BillingManagerService) UpdateCustomerTerminateGracePeriod(ctx context.Context, customerID string, gracePeriod int) error {
	err := svc.db.UpdateCustomerTerminateGracePeriod(customerID, gracePeriod)
	if err != nil {
		return fmt.Errorf("Failed to update customer grace period: %v", err)
	}

	return nil
}

type assignCustomerPricingPlanRequest struct {
	ProductID          int    `json:"productId"`
	CustomerID         string `json:"customerId"`
	ProductPlanID      string `json:"productPlanId"`
	StartTimeInSeconds int64  `json:"startTimeInSeconds"`
}

func (svc *BillingManagerService) assignCustomerPricingPlan(customerID string, pricingPlanID string) error {
	currentTime := timeNow()
	// Pricing plan needs to be assigned at the start of the hour due to Amberflo requirement.
	startTimeInSeconds := currentTime.Truncate(time.Hour).Unix()

	body := assignCustomerPricingPlanRequest{
		ProductID:          1,
		CustomerID:         customerID,
		ProductPlanID:      pricingPlanID,
		StartTimeInSeconds: startTimeInSeconds,
	}

	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/customer-pricing"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    body,
		Client:  svc.client,
	}

	_, err := rest.Post(&reqInput)
	if err != nil {
		return fmt.Errorf("Failed to assign customer pricing plan: %v", err)
	}

	return nil
}

func (svc *BillingManagerService) getDefaultPricingPlan() (string, error) {
	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans/list"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Client:  svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return "", fmt.Errorf("Unable to retrieve default pricing plan: %v", err)
	}

	idList := gjson.Get(string(resp), "#.{isDefault,id}")
	for _, id := range idList.Array() {
		if gjson.Get(id.String(), "isDefault").Bool() {
			return gjson.Get(id.String(), "id").String(), nil
		}
	}

	return "", fmt.Errorf("Unable to retrieve default pricing plan: %v", err)
}

type switchPaymentMethodRequest struct {
	CustomerID               string                  `json:"customerId"`
	SourcePaymentType        constants.PaymentMethod `json:"sourcePaymentType"`
	SourcePaymentId          string                  `json:"sourcePaymentId"`
	TargetPaymentType        constants.PaymentMethod `json:"targetPaymentType"`
	TargetPaymentId          string                  `json:"targetPaymentId"`
	TargetCustomerIdentifier string                  `json:"targetCustomerIdentifier"`
	SwitchTimeInSeconds      int64                   `json:"switchTimeInSeconds"`
}

// SwitchPaymentMethod sends a request to Amberflo to switch the payment method of a customer
// that will go into effect after the current billing cycle.
func (svc *BillingManagerService) SwitchPaymentMethod(ctx context.Context, in *pb.SwitchPaymentMethodRequest) error {
	if in.TargetPaymentMethod != constants.PaymentMethodStripe && in.TargetPaymentMethod != constants.PaymentMethodAWSMarketplace {
		return fmt.Errorf("invalid payment method: %v", in.TargetPaymentMethod)
	}

	orgID := middleware.GetOrgID(ctx)
	amberfloCustomer, err := svc.getCustomer(orgID)
	if err != nil {
		return fmt.Errorf("failed to get Amberflo customer: %v", err)
	}

	currentPaymentMethod, ok := amberfloCustomer.Traits["paymentProviderName"]
	if !ok {
		return fmt.Errorf("paymentProviderName not found in Amberflo customer(%s) traits", orgID)
	}

	var sourcePaymentID string
	var sourcePaymentType constants.PaymentMethod
	if currentPaymentMethod == "STRIPE" {
		if in.TargetPaymentMethod == constants.PaymentMethodStripe {
			return fmt.Errorf("customer is already using Stripe as payment method")
		}

		sourcePaymentType = constants.PaymentMethodStripe
		sourcePaymentID = svc.cfg.StripeAccountID
	} else if currentPaymentMethod == svc.cfg.Marketplace.AmberfloID {
		if in.TargetPaymentMethod == constants.PaymentMethodAWSMarketplace {
			return fmt.Errorf("customer is already using AWS Marketplace as payment method")
		}

		sourcePaymentType = constants.PaymentMethodAWSMarketplace
		sourcePaymentID = svc.cfg.Marketplace.AmberfloID
	}

	var targetCustomerIdentifier string
	var targetPaymentId string
	if in.TargetPaymentMethod == constants.PaymentMethodStripe {
		customerInfo, err := svc.db.GetCustomerInfo(orgID)
		if err != nil {
			return fmt.Errorf("failed to get customer(%s) info: %v", orgID, err)
		}

		targetCustomerIdentifier = customerInfo.StripeID
		targetPaymentId = svc.cfg.StripeAccountID
	} else if in.TargetPaymentMethod == constants.PaymentMethodAWSMarketplace {
		awsCustomerInfo, err := svc.db.GetAWSMarketplaceInfoByCustomerID(orgID)
		if err != nil {
			return fmt.Errorf("failed to get aws marketplace info for customer %s: %v", orgID, err)
		}

		targetCustomerIdentifier = awsCustomerInfo.MarketplaceID
		targetPaymentId = svc.cfg.Marketplace.AmberfloID
	}

	payload := &switchPaymentMethodRequest{
		CustomerID:               orgID,
		SourcePaymentType:        sourcePaymentType,
		SourcePaymentId:          sourcePaymentID,
		TargetPaymentType:        in.TargetPaymentMethod,
		TargetPaymentId:          targetPaymentId,
		TargetCustomerIdentifier: targetCustomerIdentifier,
		SwitchTimeInSeconds:      timeNow().Unix(),
	}

	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "customers/payment-method/switch"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    payload,
		Client:  svc.client,
	}

	_, err = rest.Post(&reqInput)
	if err != nil {
		return fmt.Errorf("failed to switch payment method: %v", err)
	}

	if in.DefaultCardID != "" {
		err = svc.UpdateCustomerDefaultCreditCard(ctx, orgID, in.DefaultCardID)
		if err != nil {
			return fmt.Errorf("failed to update customer default credit card: %v", err)
		}
	}

	return nil
}

func (svc *BillingManagerService) GetBillingStatus(ctx context.Context, customerID string) (*pb.BillingStatusResponse, error) {
	billingStatus := &pb.BillingStatusResponse{
		AWS:   constants.MarketplaceStatusNoAssociation,
		Azure: constants.MarketplaceStatusNoAssociation,
		GCP:   constants.MarketplaceStatusNoAssociation,
	}

	awsMarketplaceInfo, err := svc.db.GetAWSMarketplaceInfoByCustomerID(customerID)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("Failed to retrieve AWS marketplace information: %v", err)
		}
	} else {
		billingStatus.AWS = awsMarketplaceInfo.Status
	}

	amberfloCustomer, err := svc.getCustomer(customerID)
	if err != nil {
		return nil, err
	}

	currentPaymentMethod, ok := amberfloCustomer.Traits["paymentProviderName"]
	if !ok {
		return nil, fmt.Errorf("paymentProviderName not found in Amberflo customer(%s) traits", customerID)
	}

	if currentPaymentMethod == "STRIPE" {
		billingStatus.PaymentMethod = constants.PaymentMethodStripe
	} else if currentPaymentMethod == svc.cfg.Marketplace.AmberfloID {
		billingStatus.PaymentMethod = constants.PaymentMethodAWSMarketplace
	}

	return billingStatus, nil
}
