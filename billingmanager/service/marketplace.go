package service

import (
	"context"
	"strings"
)

func (svc *BillingManagerService) GetCookieDomain(ctx context.Context) string {
	if strings.Contains(svc.cfg.Marketplace.HostURL, "localhost") {
		return "localhost"
	}

	return svc.cfg.Marketplace.HostURL
}

// ShouldUseSecureCookie returns whether use secure cookie or not,
// shall disable secure cookie when run locally.
func (svc *BillingManagerService) ShouldUseSecureCookie(ctx context.Context) bool {
	return svc.GetCookieDomain(ctx) != "localhost"
}
