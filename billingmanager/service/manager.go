package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	cache "github.com/patrickmn/go-cache"
	"github.com/tidwall/gjson"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/email"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

const amberfloURL = "https://app.amberflo.io/"

type BillingManagerService struct {
	db       database.BillingManagerDB
	emailSvc email.EmailServiceIface
	cfg      *config.Config
	cache    *cache.Cache
	client   *resty.Client
}

func NewBillingManagerService(file string, emailSvc email.EmailServiceIface) (pb.BillingManager, error) {
	cfg, err := config.LoadConfig(file)
	if err != nil {
		return nil, err
	}

	db, err := database.NewBillingManagerDatabase(cfg.DBDSN)
	if err != nil {
		return nil, err
	}

	cache := cache.New(1*time.Hour, 30*time.Minute)

	client := resty.New().
		SetTimeout(0).
		SetRetryCount(3).
		SetRedirectPolicy(resty.FlexibleRedirectPolicy(3))

	return &BillingManagerService{
		db:       db,
		cfg:      &cfg,
		emailSvc: emailSvc,
		cache:    cache,
		client:   client,
	}, nil
}

func (svc *BillingManagerService) NotifyInvoiceReady(ctx context.Context, customerID string) error {
	customerInfo, err := svc.db.GetCustomerInfo(customerID)
	if err != nil {
		return err
	}

	recipients := strings.Split(customerInfo.Emails, ",")

	return svc.emailSvc.SendInvoiceNotificationEmail(customerInfo.CustomerName, recipients)
}

type GetSessionRequest struct {
	CustomerID                  string `json:"customerId"`
	ExpirationEpochMilliSeconds int64  `json:"expirationEpochMilliSeconds"`
}

type GetSessionResponse struct {
	SessionToken string `json:"sessionToken"`
}

func (svc *BillingManagerService) CreateSession(ctx context.Context, customerID string) (string, error) {
	body := GetSessionRequest{
		CustomerID:                  customerID,
		ExpirationEpochMilliSeconds: time.Now().Add(time.Hour * 24).UnixMilli(), // 1 day
	}

	reqInput := rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "session"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Body:    body,
		Client:  svc.client,
	}

	resp, err := rest.Post(&reqInput)
	if err != nil {
		return "", err
	}

	var sessionResponse GetSessionResponse
	err = json.Unmarshal(resp, &sessionResponse)
	if err != nil {
		return "", err
	}

	return sessionResponse.SessionToken, nil
}

func (svc *BillingManagerService) GetResourcePricing(ctx context.Context) (*pb.ResourcePricing, error) {
	// Check cache for result.
	cachePricing, found := svc.cache.Get("resourcePricing")
	if found {
		return cachePricing.(*pb.ResourcePricing), nil
	}

	pricingPlanID, err := svc.getDefaultPricingPlan()
	if err != nil {
		return nil, err
	}

	reqInput := rest.RequestInput{
		URL:         fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans"),
		Headers:     map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{"productPlanId": pricingPlanID},
		Client:      svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return nil, err
	}

	// Extract productItemPriceIdsMap from the response.
	productItemMapResult := gjson.Get(string(resp), "productItemPriceIdsMap")

	// Populate ProductItemPriceIdsMap into a map.
	productItemMap := make(map[string]string)
	productItemMapResult.ForEach(func(key, value gjson.Result) bool {
		productItemMap[key.String()] = value.String()
		return true // keep iterating
	})

	reqInput = rest.RequestInput{
		URL:     fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-items/list"),
		Headers: map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		Client:  svc.client,
	}

	resp, err = rest.Get(&reqInput)
	if err != nil {
		return nil, err
	}

	// Extract ID and meteApiName from the response and create map.
	meterApiNameToIdMap := make(map[string]string)
	gjson.Parse(string(resp)).ForEach(func(key, value gjson.Result) bool {
		id := value.Get("id").String()
		meterApiName := value.Get("meterApiName").String()
		meterApiNameToIdMap[meterApiName] = id
		return true // keep iterating
	})

	id, ok := meterApiNameToIdMap["ComputeUsage"]
	if !ok {
		return nil, fmt.Errorf("ComputeUsage meter api name not found")
	}

	productItemId, ok := productItemMap[id]
	if !ok {
		return nil, fmt.Errorf("ProductItemId %s not found", id)
	}

	workspacePricing, err := svc.getWorkspacePricing(productItemId)
	if err != nil {
		return nil, fmt.Errorf("Failed to get workspace pricing: %v", err)
	}

	id, ok = meterApiNameToIdMap["AddOn"]
	if !ok {
		return nil, fmt.Errorf("AddOn meter api name not found")
	}

	productItemId, ok = productItemMap[id]
	if !ok {
		return nil, fmt.Errorf("ProductItemId %s not found", id)
	}

	addOnPricing, err := svc.getAddOnPricing(productItemId)
	if err != nil {
		return nil, fmt.Errorf("Failed to get AddOn pricing: %v", err)
	}

	id, ok = meterApiNameToIdMap["DataStorage"]
	if !ok {
		return nil, fmt.Errorf("DataStorage meter api name not found")
	}

	productItemId, ok = productItemMap[id]
	if !ok {
		return nil, fmt.Errorf("ProductItemId %s not found", id)
	}

	dataStoragePricing, err := svc.getDataStoragePricing(productItemId)
	if err != nil {
		return nil, fmt.Errorf("Failed to get Data Storage Pricing:%v", err)
	}

	resourcePricing := &pb.ResourcePricing{
		WorkspacePricing:   workspacePricing,
		AddOnPricing:       addOnPricing,
		DataStoragePricing: dataStoragePricing,
	}

	// Cache the result for subsequent requests.
	svc.cache.Set("resourcePricing", resourcePricing, cache.DefaultExpiration)

	return resourcePricing, nil

}

func (svc *BillingManagerService) getWorkspacePricing(workspaceProductItemID string) ([]pb.WorkspacePricing, error) {
	log := logger.L()
	reqInput := rest.RequestInput{
		URL:         fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-item-price"),
		Headers:     map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{"id": workspaceProductItemID},
		Client:      svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return nil, err
	}

	// Extract pricing plan information from response.
	workspacePricings := make([]pb.WorkspacePricing, 0)

	// dimensionKeys is an array of dimension values used in Amberflo's Pricing Plan.
	dimensionKeys := gjson.Get(string(resp), "priceGenerator.dimensionKeys")
	// priceTiers is an array of objects containing pricing information for each set of dimension values.
	priceTiers := gjson.Get(string(resp), "priceGenerator.priceTiers")
	// Iterate through each price tier and create a pb.WorkspacePricing object to represent the
	// pricing information for each combination of dimension values.
	priceTiers.ForEach(func(key, value gjson.Result) bool {
		pricing := pb.WorkspacePricing{}

		// dimensionKeys belongs in a separate array object from  priceTiers, so we create a nested loop
		// to grab the corresponding dimension value for each dimension key.
		dimensionKeys.ForEach(func(i, dimKey gjson.Result) bool {
			dimValue := value.Get(fmt.Sprintf("dimensionValues.%s", i.String())).String()
			switch dimKey.String() {
			case "WorkspaceType":
				pricing.WorkspaceType = dimValue
			case "Platform":
				pricing.Platform = dimValue
			case "Region":
				pricing.Region = dimValue
			case "HA":
				pricing.HA = dimValue
			case "CloudProvider":
				pricing.CloudProvider = dimValue
			default:
				log.Errorf("Unknown dimension key: %s", dimKey.String())
			}

			return true
		})

		pricing.PricePerHour = (value.Get("priceTiers.0.pricePerBatch").Float() / float64(svc.cfg.TCRRatio)) * 100
		workspacePricings = append(workspacePricings, pricing)

		return true
	})

	return workspacePricings, nil
}

func (svc *BillingManagerService) getAddOnPricing(addonProductItemID string) ([]pb.AddOnPricing, error) {
	log := logger.L()
	reqInput := rest.RequestInput{
		URL:         fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-item-price"),
		Headers:     map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{"id": addonProductItemID},
		Client:      svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return nil, err
	}

	// Extract pricing plan information from response.
	addOnPricings := make([]pb.AddOnPricing, 0)

	// dimensionKeys is an array of dimension values used in Amberflo's Pricing Plan.
	dimensionKeys := gjson.Get(string(resp), "priceGenerator.dimensionKeys")
	// priceTiers is an array of objects containing pricing information for each set of dimension values.
	priceTiers := gjson.Get(string(resp), "priceGenerator.priceTiers")
	// Iterate through each price tier and create a pb.AddOnPricing object to represent the
	// pricing information for each combination of dimension values.
	priceTiers.ForEach(func(key, value gjson.Result) bool {
		pricing := pb.AddOnPricing{}

		// dimensionKeys belongs in a separate array object from  priceTiers, so we create a nested loop
		// to grab the corresponding dimension value for each dimension key.
		dimensionKeys.ForEach(func(i, dimKey gjson.Result) bool {
			dimValue := value.Get(fmt.Sprintf("dimensionValues.%s", i.String())).String()
			switch dimKey.String() {
			case "WorkspaceType":
				pricing.WorkspaceType = dimValue
			case "Platform":
				pricing.Platform = dimValue
			case "Region":
				pricing.Region = dimValue
			case "Type":
				pricing.Type = dimValue
			default:
				log.Errorf("Unknown dimension key: %s", dimKey.String())
			}

			return true
		})

		pricing.PricePerHour = (value.Get("priceTiers.0.pricePerBatch").Float() / float64(svc.cfg.TCRRatio)) * 100
		addOnPricings = append(addOnPricings, pricing)

		return true
	})

	return addOnPricings, nil
}

func (svc *BillingManagerService) getDataStoragePricing(dataStorageProductItemID string) ([]pb.DataStoragePricing, error) {
	log := logger.L()
	reqInput := rest.RequestInput{
		URL:         fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-item-price"),
		Headers:     map[string]string{"x-api-key": svc.cfg.AmberfloAPIKey},
		QueryParams: map[string]string{"id": dataStorageProductItemID},
		Client:      svc.client,
	}

	resp, err := rest.Get(&reqInput)
	if err != nil {
		return nil, err
	}

	// Extract pricing plan information from response.
	dataStoragePricings := make([]pb.DataStoragePricing, 0)

	// dimensionKeys is an array of dimension values used in Amberflo's Pricing Plan.
	dimensionKeys := gjson.Get(string(resp), "priceGenerator.dimensionKeys")
	// priceTiers is an array of objects containing pricing information for each set of dimension values.
	priceTiers := gjson.Get(string(resp), "priceGenerator.priceTiers")
	// Iterate through each price tier and create a pb.DataStoragePricing object to represent the
	// pricing information for each combination of dimension values.
	priceTiers.ForEach(func(key, value gjson.Result) bool {
		pricing := pb.DataStoragePricing{}

		// dimensionKeys belongs in a separate array object from  priceTiers, so we create a nested loop
		// to grab the corresponding dimension value for each dimension key.
		dimensionKeys.ForEach(func(i, dimKey gjson.Result) bool {
			dimValue := value.Get(fmt.Sprintf("dimensionValues.%s", i.String())).String()
			switch dimKey.String() {
			case "Platform":
				pricing.Platform = dimValue
			case "Region":
				pricing.Region = dimValue
			default:
				log.Errorf("Unknown dimension key: %s", dimKey.String())
			}

			return true
		})

		pricing.PricePerMonth = value.Get("priceTiers.0.pricePerBatch").Float() * 100
		dataStoragePricings = append(dataStoragePricings, pricing)

		return true
	})

	return dataStoragePricings, nil
}
