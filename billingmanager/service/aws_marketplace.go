package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/marketplacemetering"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"

	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/jinzhu/gorm"
	"github.com/tigergraph/cloud-universe/billingmanager/database"
	"github.com/tigergraph/cloud-universe/billingmanager/utils"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	audit "github.com/tigergraph/cloud-universe/observability/service/auditlog"
	"github.com/tigergraph/cloud-universe/quota-manager/data"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/logger/loggeriface"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	t "github.com/tigergraph/cloud-universe/utils/time"
)

func (svc *BillingManagerService) ResolveAWSCustomer(ctx context.Context, rawToken string) (customerID, productCode string, err error) {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(svc.cfg.Marketplace.Region))
	if err != nil {
		return "", "", fmt.Errorf("failed to load AWS SDK config: %w", err)
	}

	stsSvc := sts.NewFromConfig(cfg)
	creds := stscreds.NewAssumeRoleProvider(stsSvc, svc.cfg.Marketplace.RoleARN, func(op *stscreds.AssumeRoleOptions) {
		op.ExternalID = &svc.cfg.Marketplace.RoleExternalID
	})
	cfg.Credentials = aws.NewCredentialsCache(creds)

	meterClient := marketplacemetering.NewFromConfig(cfg)

	resolveOutput, err := meterClient.ResolveCustomer(ctx, &marketplacemetering.ResolveCustomerInput{
		RegistrationToken: &rawToken,
	})
	if err != nil {
		return "", "", err
	}

	if *resolveOutput.ProductCode != svc.cfg.Marketplace.ProductCode {
		return "", "", fmt.Errorf("unexpected product code: %v",
			*resolveOutput.ProductCode)
	}

	return *resolveOutput.CustomerIdentifier, *resolveOutput.ProductCode, nil
}

// ConnectMarketplaceUser creates AWSMarketplaceInfo record and connects it with the existing
// CustomerInfo record. The customer's billing will then be connected to AWS Marketplace through Ambeflo.
func (svc *BillingManagerService) ConnectMarketplaceUser(ctx context.Context, marketPlaceToken string, customerID string) error {
	log := logger.L()

	// Resolve AWS marketplace token.
	marketplaceID, productCode, err := svc.ResolveAWSCustomer(ctx, marketPlaceToken)
	if err != nil {
		return fmt.Errorf("failed to resolve AWS marketplace token: %w, token: %v", err, marketPlaceToken)
	}

	if marketplaceID == "" {
		return fmt.Errorf("marketplace ID is empty")
	}

	log.Infof("Resolved AWS marketplace token: marketplace ID: %v, product code: %v", marketplaceID, productCode)

	_, err = svc.db.GetAWSMarketplaceInfoByMarketplaceID(marketplaceID)
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return fmt.Errorf("failed to get AWS marketplace info by marketplace ID: %w", err)
	}

	// Found a user with the same Marketplace ID.
	if err == nil {
		return fmt.Errorf("AWS marketplace user already exists: %v", marketplaceID)
	}

	_, err = svc.db.GetAWSMarketplaceInfoByCustomerID(customerID)
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return fmt.Errorf("failed to get AWS marketplace info by customer ID: %w", err)
	}

	// User already has existing AWS Marketplace connection.
	if err == nil {
		return fmt.Errorf("org (%s) already has existing AWS Marketplace connection", customerID)
	}

	err = svc.db.CreateAWSMarketplaceInfo(customerID, marketplaceID, productCode)
	if err != nil {
		return fmt.Errorf("failed to create AWS marketplace info: %w", err)
	}

	// Update Amberflo Customer with AWS Marketplace ID.
	request := &pb.UpdateCustomerRequest{
		Traits: &map[string]string{
			"paymentProviderName":     svc.cfg.Marketplace.AmberfloID,
			"awsm_customerIdentifier": marketplaceID,
		},
	}

	_, err = svc.UpdateCustomer(ctx, customerID, request)
	if err != nil {
		return fmt.Errorf("failed to update Amberflo customer: %w", err)
	}

	return nil
}

type SQSClient interface {
	DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error)
	ReceiveMessage(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error)
	ChangeMessageVisibility(ctx context.Context, params *sqs.ChangeMessageVisibilityInput, optFns ...func(*sqs.Options)) (*sqs.ChangeMessageVisibilityOutput, error)
}

func (svc *BillingManagerService) PollSQSMessage(quotaManager pb.QuotaManager) {
	if !svc.cfg.Marketplace.EnablePollSQS {
		return
	}

	ctx := lmri.NewCtxWithReqId(context.Background())
	log := logger.L().WithContext(ctx)

	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(svc.cfg.Marketplace.Region))
	if err != nil {
		log.Errorf("failed to load AWS SDK config: %v", err)
		panic(fmt.Sprintf("failed to load AWS SDK config: %v", err))
	}

	stsSvc := sts.NewFromConfig(cfg)
	creds := stscreds.NewAssumeRoleProvider(stsSvc, svc.cfg.Marketplace.RoleARN, func(op *stscreds.AssumeRoleOptions) {
		op.ExternalID = &svc.cfg.Marketplace.RoleExternalID
	})
	cfg.Credentials = aws.NewCredentialsCache(creds)

	sqsClient := sqs.NewFromConfig(cfg)
	for {
		svc.pollSQSMessage(ctx, sqsClient, quotaManager, log)
	}
}

func (svc *BillingManagerService) pollSQSMessage(ctx context.Context, sqsClient SQSClient, quotaManager pb.QuotaManager, log loggeriface.Logger) {
	input := sqs.ReceiveMessageInput{
		QueueUrl: &svc.cfg.Marketplace.SQSURL,
		MessageSystemAttributeNames: []types.MessageSystemAttributeName{
			types.MessageSystemAttributeNameSentTimestamp,
			types.MessageSystemAttributeNameApproximateReceiveCount,
		},
	}

	sqsMsg, err := sqsClient.ReceiveMessage(ctx, &input)
	if err != nil {
		log.Errorf("failed to receive message from SQS: %v", err)

		// Sleep for 1 second to avoid throttling.
		time.Sleep(1 * time.Second)
		return
	}

	for _, msg := range sqsMsg.Messages {
		if msgTimeStampString, ok := msg.Attributes[string(types.MessageSystemAttributeNameSentTimestamp)]; ok {
			// Check the number of times the same message has been delivered.
			if approxRecvCountString, ok := msg.Attributes[string(types.MessageSystemAttributeNameApproximateReceiveCount)]; ok {
				if count, err := strconv.Atoi(approxRecvCountString); err == nil && count > 0 && count%1000 == 0 {
					log.Warnf("Message from AWS SQS: %v, has been received for %v times", *(msg.Body), count)
				}
			}

			// Get time stamp and parse payload to MessageBody from raw message.
			msgTimeStamp, err := t.MilliSecondsStringToTime(&msgTimeStampString)
			if err != nil {
				log.Errorf("failed to convert AWS marketplace SQS message time stamp: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}

			// Delete and alert if the message has been in queue for over 13 days.
			// Message will remain in queue if customer subscribes to our SaaS but never registers an account in our portal.
			if time.Since(*msgTimeStamp) > 13*24*time.Hour {
				log.Warnf("AWS sqs message has been in queue for over 13 days: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}

			// Unmarshal the payload, two layers of wrap by SQS and SNS respectively.
			rawMsg := RawMessage{}
			if err = json.Unmarshal([]byte(*(msg.Body)), &rawMsg); err != nil {
				log.Errorf("failed to unmarshall AWS marketplace SQS message into raw message: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}
			msgBody := MessageBody{}
			err = json.Unmarshal([]byte(rawMsg.Message), &msgBody)
			if err != nil {
				log.Errorf("failed to unmarshal AWS marketplace SQS message into message body: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}

			// Verify the product code.
			if msgBody.ProductCode != svc.cfg.Marketplace.ProductCode {
				log.Errorf("provided product code in SQS message does not match with our SaaS listing: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}

			// Verify if customer identifier is not empty.
			if msgBody.CustomerID == "" {
				log.Errorf("AWS marketplace customerID in SQS message is empty: %v, msg: %s", err, utils.SQSMessagePrettyPrint(&msg))
				svc.deleteSQSMessage(ctx, sqsClient, &msg)
				continue
			}

			log.Infof("AWS marketplace customer %v has %v", msgBody.CustomerID, msgBody.Action)
			// Process accordingly with action type.
			switch msgBody.Action {
			case SubscribeSuccess:
				svc.processSubSuccess(ctx, sqsClient, &msg, msgBody.CustomerID, msgTimeStamp, quotaManager)
			case SubscribeFail:
				log.Warnf("AWS marketplace customer %v tried to subscribe but failed", msgBody.CustomerID)
			case UnsubscribePending:
				svc.processUnsubPending(ctx, sqsClient, &msg, msgBody.CustomerID, msgTimeStamp, quotaManager)
			case UnsubscribeSuccess:
				svc.processUnsubSuccess(ctx, sqsClient, &msg, msgBody.CustomerID, msgTimeStamp)
			default:
				log.Errorf("unknown SQS message action type: %s", err, utils.SQSMessagePrettyPrint(&msg))
			}
		} else {
			log.Errorf("failed to get sentTimeStamp for AWS marketplace SQS message: %s", utils.SQSMessagePrettyPrint(&msg))
		}
	}
}

func (svc *BillingManagerService) deleteSQSMessage(ctx context.Context, sqsClient SQSClient, msg *types.Message) error {
	_, err := sqsClient.DeleteMessage(ctx, &sqs.DeleteMessageInput{
		QueueUrl:      &svc.cfg.Marketplace.SQSURL,
		ReceiptHandle: msg.ReceiptHandle,
	})

	return err
}

// updateStatusAndDeleteMessage updates status of the user in DB and ack(delete) the message.
// Return ok = true if it is a fresh new valid request.
// Return err != nil if any error occurs.
func (svc *BillingManagerService) updateStatusAndDeleteMessage(
	ctx context.Context,
	sqsClient SQSClient,
	customer *database.AWSMarketplaceInfo,
	msg *types.Message,
	newStatus constants.MarketplaceStatus,
	timeStamp *time.Time,
	expectedPrevStatus constants.MarketplaceStatus,
) (ok bool, err error) {
	log := logger.L()

	log.Infof("Updating AWS marketplace customer status: %v, new status: %v, expected prev status: %v",
		customer.CustomerID, newStatus, expectedPrevStatus)
	// Check if it is a duplicated status change request.
	if customer.Status == newStatus {
		if err = svc.deleteSQSMessage(ctx, sqsClient, msg); err != nil {
			return false, fmt.Errorf("failed to delete sqs message %v", err.Error())
		}
		return false, nil
	}

	// Check if the status request has a newer timestamp.
	if customer.StatusUpdateTime != nil && (!customer.StatusUpdateTime.Before(*timeStamp)) {
		if err := svc.deleteSQSMessage(ctx, sqsClient, msg); err != nil {
			return false, fmt.Errorf("failed to delete sqs message %v", err.Error())
		}
		return false, nil
	}

	// Check if the expected prev status matches.
	if expectedPrevStatus != "" && customer.Status != expectedPrevStatus {
		return false, fmt.Errorf("unable to match AWS marketplace customer previous status, expected: %v, presented: %v",
			expectedPrevStatus, customer.Status)
	}

	// Only update the status of the customer if it is a newer & different update.
	customer.Status = newStatus
	customer.StatusUpdateTime = timeStamp
	if err := svc.db.UpdateAWSMarketplaceInfo(customer); err != nil {
		return true, fmt.Errorf("failed to update AWS marketplace customer in db: %v", err)
	}

	// If marketplace subscription has been cancelled, we delete the DB record.
	if newStatus == constants.MarketplaceStatusCancelled {
		if err := svc.db.DeleteAWSMarketplaceInfo(customer.ID); err != nil {
			return true, fmt.Errorf("failed to delete AWS marketplace customer in db: %v", err)
		}

	}

	// Delete the message from the queue.
	if err := svc.deleteSQSMessage(ctx, sqsClient, msg); err != nil {
		return true, fmt.Errorf("failed to delete AWS SQS message: %v", err.Error())
	}
	return true, nil
}

func (svc *BillingManagerService) processSubSuccess(ctx context.Context, sqsClient SQSClient, msg *types.Message, marketplaceCustomerID string, timeStamp *time.Time, quotaManager pb.QuotaManager) {
	log := logger.L()
	customer, err := svc.db.GetAWSMarketplaceInfoByMarketplaceID(marketplaceCustomerID)
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			log.Errorf("failed to get AWS marketplace customer in db: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
		} else {
			// Delay subscribe-success message redelivery.
			_, err = sqsClient.ChangeMessageVisibility(ctx, &sqs.ChangeMessageVisibilityInput{
				ReceiptHandle:     msg.ReceiptHandle,
				QueueUrl:          &svc.cfg.Marketplace.SQSURL,
				VisibilityTimeout: 60,
			})
			if err != nil {
				log.Errorf("failed to delay AWS SQS message redelivery for marketplace: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
			}
		}

		return
	}

	// If customer account already exists, update the status.
	isValidRequest, err := svc.updateStatusAndDeleteMessage(ctx, sqsClient, customer, msg, constants.MarketplaceStatusActive, timeStamp, "")
	if err != nil {
		log.Errorf("failed to update AWS marketplace customer status: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
	}

	if isValidRequest {
		err := quotaManager.UpdateOrgQuotaTier(ctx, customer.CustomerID, data.PaidCustomerTier)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to update org quota tier for customer(%s) after AWS marketplace subscription: %v", customer.CustomerID, err)
			log.Errorf(errMsg)
			svc.emailSvc.Notify("Failed to update org quota tier after AWS marketplace subscription", errMsg)
		}

		al := audit.L().WithContext(ctx).WithService(constants.AuditServiceName)
		al.Successf(constants.ActionAddMarketplaceSubscription, "Successfully added [AWS] marketplace subscription [%s].", customer.MarketplaceID)
	}
}

func (svc *BillingManagerService) processUnsubPending(ctx context.Context, sqsClient SQSClient, msg *types.Message, marketplaceCustomerID string, timeStamp *time.Time, quotaManager pb.QuotaManager) {
	log := logger.L()
	customer, err := svc.db.GetAWSMarketplaceInfoByMarketplaceID(marketplaceCustomerID)
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			log.Errorf("Failed to get AWS marketplace customer in db: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
		} else {
			svc.deleteSQSMessage(ctx, sqsClient, msg)
		}
		return
	}

	// If customer exists, update status
	isValidRequest, err := svc.updateStatusAndDeleteMessage(ctx, sqsClient, customer, msg, constants.MarketplaceStatusPendingCancellation, timeStamp, constants.MarketplaceStatusActive)
	if err != nil {
		log.Errorf("Failed to update AWS marketplace customer status: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
	}

	// 1. Customer unsubscribes from marketplace after payment method switch has taken effect.
	//     - Do nothing.
	// 2. Customer unsubscribes from marketplace mid-billing cycle, but still has valid payment method on file.
	//     - Send internal email alert to contact customer.
	// 3. Customer unsubscribes from marketplace mid-billing cycle, but has no valid payment method.
	//     - Mark customer as delinquent. Resources will be stopped based on grace period.
	if isValidRequest {
		amberfloCustomer, err := svc.getCustomer(customer.CustomerID)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get Amberflo customer(%s) after AWS marketplace unsubscription: %v", customer.CustomerID, err)
			log.Errorf(errMsg)
			svc.emailSvc.Notify("Failed to get Amberflo customer after AWS marketplace unsubscription", errMsg)
			return
		}

		credit, err := svc.GetCredit(ctx, customer.CustomerID, true)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get credit for customer(%s) after AWS marketplace unsubscription: %v", customer.CustomerID, err)
			log.Errorf(errMsg)
			svc.emailSvc.Notify("Failed to get credit after AWS marketplace unsubscription", errMsg)
			return
		}

		dbCustomer, err := svc.db.GetCustomerInfo(customer.CustomerID)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to retrieve customer(%s) info from db after AWS marketplace unsubscription: %v", customer.CustomerID, err)
			log.Errorf(errMsg)
			svc.emailSvc.Notify("Failed to retrieve customer info from db after AWS marketplace unsubscription", errMsg)
			return
		}

		al := audit.L().WithContext(ctx).WithService(constants.AuditServiceName)
		al.Successf(constants.ActionRemoveMarketplaceSubscription, "Successfully removed [AWS] marketplace subscription [%s].", customer.MarketplaceID)

		paymentProvider, ok := amberfloCustomer.Traits["paymentProviderName"]
		if ok && (paymentProvider == "STRIPE" && dbCustomer.HasValidCard) {
			// Customer unsubscribes from marketplace after payment method switch has taken effect.
			// Do nothing.
			return
		}

		if dbCustomer.HasValidCard || credit > 0 {
			// Customer unsubscribes from marketplace mid-billing cycle, but still has valid payment method on file.
			// Send internal email alert to contact customer.
			emailMsg := fmt.Sprintf("Customer %s has unsubscribed from AWS marketplace mid-billing cycle, "+
				"but still has a valid payment method on file. Please contact the customer regarding further payment, "+
				"or check if resources should be stopped manually.",
				customer.CustomerID)

			svc.emailSvc.Notify("Customer unsubscribed from AWS marketplace mid-billing cycle", emailMsg)
		} else {
			// Customer unsubscribes from marketplace mid-billing cycle, but has no valid payment method.
			// We mark them as delinquent, and their resources will be stopped based on grace period.
			_, err := svc.db.MarkCustomerDelinquent(customer.CustomerID)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to mark customer(%s) delinquent after AWS marketplace unsubscirption: %v", customer.CustomerID, err)
				log.Errorf(errMsg)
				svc.emailSvc.Notify("Failed to mark customer delinquent after AWS marketplace unsubscription", errMsg)
			}

			err = quotaManager.UpdateOrgQuotaTier(ctx, customer.CustomerID, data.InitialCustomerTier)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to update org quota tier for customer(%s) after AWS marketplace unsubscription: %v", customer.CustomerID, err)
				log.Errorf(errMsg)
				svc.emailSvc.Notify("Failed to update org quota tier after AWS marketplace unsubscription", errMsg)
			}
		}
	}
}

func (svc *BillingManagerService) processUnsubSuccess(ctx context.Context, sqsClient SQSClient, msg *types.Message, marketplaceCustomerID string, timeStamp *time.Time) {
	log := logger.L()
	customer, err := svc.db.GetAWSMarketplaceInfoByMarketplaceID(marketplaceCustomerID)
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			log.Errorf("Failed to get AWS marketplace customer in db: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
		} else {
			svc.deleteSQSMessage(ctx, sqsClient, msg)
		}
		return
	}

	if customer.Status == constants.MarketplaceStatusActive {
		errMsg := fmt.Sprintf("Unexpected AWS marketplace subscription cancelled request for customer %s: %s", customer.CustomerID, utils.SQSMessagePrettyPrint(msg))
		log.Errorf(errMsg)
		svc.emailSvc.Notify("Unexpected AWS marketplace subscription cancelled request", errMsg)
	}

	if _, err = svc.updateStatusAndDeleteMessage(ctx, sqsClient, customer, msg, constants.MarketplaceStatusCancelled, timeStamp, constants.MarketplaceStatusPendingCancellation); err != nil {
		log.Errorf("Failed to update AWS marketplace customer status: %v, msg: %s", err, utils.SQSMessagePrettyPrint(msg))
	}
}
