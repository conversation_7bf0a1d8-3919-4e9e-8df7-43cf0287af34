package service

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/rest"
)

func TestCreateSession(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Get all product plans.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "session")
	httpmock.RegisterResponder("POST", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.<PERSON><PERSON><PERSON>("Expected x-api-key: %s, got: %s", test<PERSON><PERSON><PERSON><PERSON>, req.Header.Get("x-api-key"))
			}

			resp := `
				{
					"url": "https://portal.amberflo.io/session/9cd47c746d194674c44184317e8f7f6483f2c47bbc24771ce96fcb594bce7fc5625d4f5f5c99?returnUrl=https%3A%2F%2Fnetlify.com%2F",
					"sessionToken": "9cd42972321b1123924184317f8d246483f2c47bbc27261eba6f9c5f1dce7e9a635a1d5d545"
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
		},
		client: client,
	}

	session, err := svc.CreateSession(context.Background(), "test-org")
	assert.Equal(t, session, "9cd42972321b1123924184317f8d246483f2c47bbc27261eba6f9c5f1dce7e9a635a1d5d545")
	assert.NoError(t, err)

}

func TestGetResourcePricing(t *testing.T) {
	client := rest.NewClient(0)
	httpmock.ActivateNonDefault(client.GetClient())
	defer httpmock.DeactivateAndReset()

	// Get all product plans.
	urlPath := fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans/list")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				[
					{
						"id": "9131fc0f-e211-4df9-b4df-1382adc12506",
						"productId": "1",
						"productItemPriceIdsMap": {
						"30d0e3dc-f5c3-4c6b-ae9a-e50ea12d19e9": "97dfb442-89aa-423b-b968-a2cca4f13225",
						"9ce6d78d-7fee-4b51-bec1-3ee655fd85c1": "1b1dfeb6-7977-4ccd-90e3-7bb81402616e",
						"87611aaa-181c-4698-9a12-fc531ac193f4": "4543cf2c-e6c9-4122-b962-1b248e9ef2af"
						},
						"billingPeriod": {
						"interval": "month",
						"intervalsCount": 1
						},
						"planLevelFreeTier": null,
						"invoiceBasedFeeIds": null,
						"planMinimumPayment": null,
						"minimumPaymentGranularity": null,
						"productPlanName": "Default-Pricing-Plan",
						"description": "",
						"lastUpdateTimeInMillis": *************,
						"feeMap": {},
						"entitlements": null,
						"lockingStatus": "close_to_changes",
						"isDefault": true,
						"successorPlanId": null,
						"transitionStrategy": null,
						"type": "custom_template",
						"prepaidBuyingRules": null,
						"planGenerator": null,
						"planCurrency": null
					}
				]`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Get product plan details by ID.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-plans")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			if !strings.Contains(req.URL.Query().Encode(), "productPlanId=9131fc0f-e211-4df9-b4df-1382adc12506") {
				t.Errorf("Missing query param 'productPlanId=9131fc0f-e211-4df9-b4df-1382adc12506'")
			}

			resp := `
				{
					"id": "9131fc0f-e211-4df9-b4df-1382adc12506",
					"productId": "1",
					"productItemPriceIdsMap": {
						"30d0e3dc-f5c3-4c6b-ae9a-e50ea12d19e9": "97dfb442-89aa-423b-b968-a2cca4f13225",
						"9ce6d78d-7fee-4b51-bec1-3ee655fd85c1": "1b1dfeb6-7977-4ccd-90e3-7bb81402616e",
						"87611aaa-181c-4698-9a12-fc531ac193f4": "4543cf2c-e6c9-4122-b962-1b248e9ef2af"
					},
					"billingPeriod": {
						"interval": "month",
						"intervalsCount": 1
					},
					"planLevelFreeTier": null,
					"invoiceBasedFeeIds": null,
					"planMinimumPayment": null,
					"minimumPaymentGranularity": null,
					"productPlanName": "Default-Pricing-Plan",
					"description": "",
					"lastUpdateTimeInMillis": *************,
					"feeMap": {},
					"entitlements": null,
					"lockingStatus": "close_to_changes",
					"isDefault": true,
					"successorPlanId": null,
					"transitionStrategy": null,
					"type": "custom_template",
					"prepaidBuyingRules": null,
					"planGenerator": null,
					"planCurrency": null
				}`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Get all product items.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-items/list")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			resp := `
				[
					{
						"id": "9ce6d78d-7fee-4b51-bec1-3ee655fd85c1",
						"productId": "1",
						"meterApiName": "AddOn",
						"dimensionLabelInfoList": null,
						"productItemName": "Add_On",
						"description": "Tracks the hours of Add On use for workspaces",
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": *************
					},
					{
						"id": "30d0e3dc-f5c3-4c6b-ae9a-e50ea12d19e9",
						"productId": "1",
						"meterApiName": "ComputeUsage",
						"dimensionLabelInfoList": null,
						"productItemName": "Compute_Usage",
						"description": "Tracks the runtime in hours of a compute resource",
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": *************
					},
					{
						"id": "87611aaa-181c-4698-9a12-fc531ac193f4",
						"productId": "1",
						"meterApiName": "DataStorage",
						"dimensionLabelInfoList": null,
						"productItemName": "Data_Storage",
						"description": "Tracks storage consumption by summing the size of the storage resource per hour",
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": *************
					}
				]`
			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	// Get product item price by ID.
	urlPath = fmt.Sprintf("%s%s", amberfloURL, "payments/pricing/amberflo/account-pricing/product-item-price")
	httpmock.RegisterResponder("GET", urlPath,
		func(req *http.Request) (*http.Response, error) {
			if req.Header.Get("x-api-key") != testAPIKey {
				t.Errorf("Expected x-api-key: %s, got: %s", testAPIKey, req.Header.Get("x-api-key"))
			}

			queryParams := req.URL.Query()
			id := queryParams.Get("id")
			var resp string

			if id == "97dfb442-89aa-423b-b968-a2cca4f13225" {
				resp = `
					{
						"associatedProductPlanIds": [
							"9131fc0f-e211-4df9-b4df-1382adc12506"
						],
						"id": "97dfb442-89aa-423b-b968-a2cca4f13225",
						"productItemId": "30d0e3dc-f5c3-4c6b-ae9a-e50ea12d19e9",
						"subscriptionPrice": null,
						"subscriptionPriceGenerator": null,
						"productItemPriceName": "97dfb442-89aa-423b-b968-a2cca4f13225",
						"productItemInvoiceLabel": null,
						"scalar": null,
						"dimensionalScalars": null,
						"subscriptionSettings": null,
						"paymentMinimums": null,
						"usageTransformerId": null,
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": *************,
						"priceGenerator": {
							"dimensionKeys": [
							"WorkspaceType",
							"Platform",
							"Region",
							"HA"
							],
							"id": "97dfb442-89aa-423b-b968-a2cca4f13225",
							"priceTiers": [
								{
									"dimensionValues": [
									"TG-00",
									"aws",
									"us-east-1",
									"disabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 0.25
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								},
								{
									"dimensionValues": [
									"TG-0",
									"aws",
									"us-east-1",
									"disabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 0.50
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								},
								{
									"dimensionValues": [
									"TG-1",
									"aws",
									"us-east-1",
									"disabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 1
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								},
								{
									"dimensionValues": [
									"TG-00",
									"aws",
									"us-east-1",
									"enabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 0.50
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								},
								{
									"dimensionValues": [
									"TG-0",
									"aws",
									"us-east-1",
									"enabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 1
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								},
								{
									"dimensionValues": [
									"TG-1",
									"aws",
									"us-east-1",
									"enabled"
									],
									"priceTiers": [
									{
										"startAfterUnit": 0,
										"batchSize": 1,
										"pricePerBatch": 2
									}
									],
									"leafNodeType": "PartialUsageLeafNode"
								}
							],
							"productItemId": "30d0e3dc-f5c3-4c6b-ae9a-e50ea12d19e9",
							"groupDimensions": [
							"ResourceName"
							],
							"lockingStatus": "open",
							"productItemInvoiceLabel": null,
							"productItemPriceName": "97dfb442-89aa-423b-b968-a2cca4f13225",
							"scalar": null,
							"dimensionalScalars": null,
							"usageTransformer": null,
							"type": "usage_based_dimension",
							"useAverageReducer": null
						}
					}`
			} else if id == "1b1dfeb6-7977-4ccd-90e3-7bb81402616e" {
				resp = `
					{
						"associatedProductPlanIds": [
							"bae20361-c2a4-4f06-b22c-6d7d486a945e"
						],
						"id": "d6131347-608f-411e-8515-b50679df4916",
						"productItemId": "9ce6d78d-7fee-4b51-bec1-3ee655fd85c1",
						"subscriptionPrice": null,
						"subscriptionPriceGenerator": null,
						"productItemPriceName": "d6131347-608f-411e-8515-b50679df4916",
						"productItemInvoiceLabel": null,
						"scalar": null,
						"dimensionalScalars": null,
						"subscriptionSettings": null,
						"paymentMinimums": null,
						"usageTransformerId": null,
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": 1725997025061,
						"priceGenerator": {
							"dimensionKeys": [
							"Type",
							"WorkspaceType",
							"Platform",
							"Region"
							],
							"id": "d6131347-608f-411e-8515-b50679df4916",
							"priceTiers": [
							{
								"dimensionValues": [
								"CoPilot",
								"TG-00",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.25
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							{
								"dimensionValues": [
								"CoPilot",
								"TG-0",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.5
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							{
								"dimensionValues": [
								"CoPilot",
								"TG-1",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 1
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							{
								"dimensionValues": [
								"Insights",
								"TG-00",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.25
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							{
								"dimensionValues": [
								"Insights",
								"TG-0",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.5
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							{
								"dimensionValues": [
								"Insights",
								"TG-1",
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 1
								}
								],
								"leafNodeType": "PricePerUnitLeafNode"
							},
							],
							"productItemId": "9ce6d78d-7fee-4b51-bec1-3ee655fd85c1",
							"groupDimensions": [
							"Type",
							"WorkspaceName"
							],
							"lockingStatus": "open",
							"productItemInvoiceLabel": null,
							"productItemPriceName": "d6131347-608f-411e-8515-b50679df4916",
							"scalar": null,
							"dimensionalScalars": null,
							"usageTransformer": null,
							"type": "usage_based_dimension",
							"useAverageReducer": null
						}
					}`
			} else if id == "4543cf2c-e6c9-4122-b962-1b248e9ef2af" {
				resp = `
					{
						"associatedProductPlanIds": [
							"844cbdc8-ec75-4e19-ae7c-dc183cfcea26"
						],
						"id": "b175b22c-cc17-45ec-ad20-a4911e822897",
						"productItemId": "87611aaa-181c-4698-9a12-fc531ac193f4",
						"subscriptionPrice": null,
						"subscriptionPriceGenerator": null,
						"productItemPriceName": "b175b22c-cc17-45ec-ad20-a4911e822897",
						"productItemInvoiceLabel": null,
						"scalar": null,
						"dimensionalScalars": null,
						"subscriptionSettings": null,
						"paymentMinimums": null,
						"usageTransformerId": null,
						"lockingStatus": "close_to_changes",
						"lastUpdateTimeInMillis": 1726711911475,
						"priceGenerator": {
							"dimensionKeys": [
							"Platform",
							"Region"
							],
							"id": "b175b22c-cc17-45ec-ad20-a4911e822897",
							"priceTiers": [
							{
								"dimensionValues": [
								"aws",
								"us-east-1"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.025
								}
								],
								"leafNodeType": "PartialUsageLeafNode"
							},
							{
								"dimensionValues": [
								"aws",
								"us-east-2"
								],
								"priceTiers": [
								{
									"startAfterUnit": 0,
									"batchSize": 1,
									"pricePerBatch": 0.0282
								}
								],
								"leafNodeType": "PartialUsageLeafNode"
							}
							],
							"productItemId": "87611aaa-181c-4698-9a12-fc531ac193f4",
							"groupDimensions": [
							"ResourceName"
							],
							"lockingStatus": "open",
							"productItemInvoiceLabel": null,
							"productItemPriceName": "b175b22c-cc17-45ec-ad20-a4911e822897",
							"scalar": null,
							"dimensionalScalars": null,
							"usageTransformer": null,
							"type": "usage_based_dimension",
							"useAverageReducer": "hourly"
						}
					}`
			} else {
				t.Errorf("Missing query param 'id=%s' not recognized.", id)
			}

			return httpmock.NewBytesResponse(200, []byte(resp)), nil
		},
	)

	svc := &BillingManagerService{
		cfg: &config.Config{
			AmberfloAPIKey: testAPIKey,
			TCRRatio:       4,
		},
		client: client,
		cache:  cache.New(cache.NoExpiration, cache.NoExpiration),
	}

	resourcePricings, err := svc.GetResourcePricing(context.Background())
	assert.NoError(t, err)

	expectedPricing := &pb.ResourcePricing{
		WorkspacePricing: []pb.WorkspacePricing{
			{
				WorkspaceType: "TG-00",
				Region:        "us-east-1",
				Platform:      "aws",
				HA:            "disabled",
				PricePerHour:  6.25,
			},
			{
				WorkspaceType: "TG-0",
				Region:        "us-east-1",
				Platform:      "aws",
				PricePerHour:  12.5,
				HA:            "disabled",
			},
			{
				WorkspaceType: "TG-1",
				Region:        "us-east-1",
				Platform:      "aws",
				PricePerHour:  25,
				HA:            "disabled",
			},
			{
				WorkspaceType: "TG-00",
				Region:        "us-east-1",
				Platform:      "aws",
				PricePerHour:  12.5,
				HA:            "enabled",
			},
			{
				WorkspaceType: "TG-0",
				Region:        "us-east-1",
				Platform:      "aws",
				PricePerHour:  25,
				HA:            "enabled",
			},
			{
				WorkspaceType: "TG-1",
				Region:        "us-east-1",
				Platform:      "aws",
				PricePerHour:  50,
				HA:            "enabled",
			},
		},
		AddOnPricing: []pb.AddOnPricing{
			{
				Type:          "CoPilot",
				WorkspaceType: "TG-00",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  6.25,
			},
			{
				Type:          "CoPilot",
				WorkspaceType: "TG-0",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  12.5,
			},
			{
				Type:          "CoPilot",
				WorkspaceType: "TG-1",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  25,
			},
			{
				Type:          "Insights",
				WorkspaceType: "TG-00",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  6.25,
			},
			{
				Type:          "Insights",
				WorkspaceType: "TG-0",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  12.5,
			},
			{
				Type:          "Insights",
				WorkspaceType: "TG-1",
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerHour:  25,
			},
		},
		DataStoragePricing: []pb.DataStoragePricing{
			{
				Platform:      "aws",
				Region:        "us-east-1",
				PricePerMonth: 2.5,
			},
			{
				Platform:      "aws",
				Region:        "us-east-2",
				PricePerMonth: 2.82,
			},
		},
	}
	assert.Equal(t, expectedPricing, resourcePricings)
}
