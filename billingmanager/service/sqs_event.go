package service

const (
	SubscribeSuccess   = "subscribe-success"
	SubscribeFail      = "subscribe-fail"
	UnsubscribePending = "unsubscribe-pending"
	UnsubscribeSuccess = "unsubscribe-success"
)

type MessageBody struct {
	Action      string `json:"action" binding:"required"`
	CustomerID  string `json:"customer-identifier" binding:"required"`
	ProductCode string `json:"product-code" binding:"required"`
}

type RawMessage struct {
	Message string `json:"Message" binding:"required"`
}
