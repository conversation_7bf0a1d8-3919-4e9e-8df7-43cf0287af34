package config

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLoadConfig(t *testing.T) {
	// No config file test.
	config, err := LoadConfig(".")
	require.NoError(t, err)

	require.Equal(t, config.DBDSN, "testdbdsn")
	require.Equal(t, config.SNSARN, "testsnsarn")
	require.Equal(t, config.SNSRegion, "test-region")
	require.Equal(t, config.AmberfloAPIKey, "testapikey")
	require.Equal(t, config.EnableMetricCollection, true)
	require.Equal(t, config.StorageMetricInterval, 10)
	require.Equal(t, config.DeveloperMode, true)
	require.Equal(t, config.StripeAccountID, "teststripeaccountid")
	require.Equal(t, config.Marketplace.AmberfloID, "test-amberflo-id")
	require.Equal(t, config.Marketplace.RoleARN, "testrolearn")
	require.Equal(t, config.Marketplace.RoleExternalID, "testroleexternalid")
	require.Equal(t, config.Marketplace.Region, "test-region")
	require.Equal(t, config.Marketplace.SQSURL, "testsqsurl")
	require.Equal(t, config.Marketplace.ProductCode, "testproductcode")
	require.Equal(t, config.Marketplace.HostURL, "testhosturl")
	require.Equal(t, config.Marketplace.EnablePollSQS, true)
	require.Equal(t, config.IgnoreMetricCollection, []string{"org-id-1", "org-id-2"})
}
