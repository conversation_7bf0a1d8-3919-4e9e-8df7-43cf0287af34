package config

import (
	"github.com/spf13/viper"
)

type Config struct {
	DBDSN                  string `yaml:"dbdsn"`
	SNSARN                 string `yaml:"snsarn"`
	SNSRegion              string `yaml:"snsregion"`
	AmberfloAPIKey         string `yaml:"amberfloapikey"`
	EnableMetricCollection bool   `yaml:"enablemetriccollection"`
	StorageMetricInterval  int    `yaml:"storagemetricinterval"`
	// Ignore metric collection for specified orgs.
	IgnoreMetricCollection []string `yaml:"ignoremetriccollection"`
	// Ratio of TCR to be used for billing. 1 TCR = 4 USD
	TCRRatio int `yaml:"tcrratio"`
	// When enabled, certain Amberflo and Billing features are disabled.
	DeveloperMode   bool              `yaml:"developermode"`
	StripeAccountID string            `yaml:"stripeaccountid"`
	Marketplace     MarketplaceConfig `yaml:"marketplace"`
}

type MarketplaceConfig struct {
	AmberfloID     string `yaml:"amberfloID"`
	RoleARN        string `yaml:"rolearn"`
	RoleExternalID string `yaml:"roleexternalid"`
	Region         string `yaml:"region"`
	SQSURL         string `yaml:"sqsurl"`
	ProductCode    string `yaml:"productcode"`
	HostURL        string `yaml:"hosturl"`
	EnablePollSQS  bool   `yaml:"enablepollsqs"`
}

func LoadConfig(path string) (config Config, err error) {
	viper.AutomaticEnv()

	viper.AddConfigPath(path)
	viper.SetConfigName("billing-manager")
	viper.SetConfigType("yaml")
	if err = viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return
		}
	}

	err = viper.Unmarshal(&config)
	return
}
