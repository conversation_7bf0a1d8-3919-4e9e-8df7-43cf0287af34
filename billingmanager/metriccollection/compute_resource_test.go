package metriccollection

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/common/pb"
)

const layout = "2006-01-02 15:04:05"

//nolint:gochecknoinits // for test
func init() {
	timeNow = func() time.Time {
		t, _ := time.Parse(layout, "2023-10-16 16:13:05")
		return t
	}
}

func TestCheckTimestamps(t *testing.T) {
	// Test running Compute Resource.
	currentTimestamp, _ := time.Parse(layout, "2023-10-16 16:12:05")
	previousTimestamp, _ := time.Parse(layout, "2023-10-16 16:11:05")

	meterValue, meterTimeInMillis := checkTimestamps(previousTimestamp, currentTimestamp)

	// Meter value of 1 and provided currentTimestamp should be returned since current time is
	// only one minute after current timestamp.
	require.Equal(t, float64(1), meterValue)
	require.Equal(t, meterTimeInMillis, currentTimestamp.UnixMilli())

	// Test stopped Compute Resource.
	currentTimestamp, _ = time.Parse(layout, "2023-10-16 16:09:05")
	previousTimestamp, _ = time.Parse(layout, "2023-10-16 16:08:05")

	meterValue, meterTimeInMillis = checkTimestamps(previousTimestamp, currentTimestamp)

	// Meter value of 0 and provided currentTimestamp + 1x interval should be returned since
	// more than 3 minutes have elapsed since provided currentTimestamp.
	require.Equal(t, float64(0), meterValue)
	require.Equal(t, meterTimeInMillis, currentTimestamp.Add(collectComputeMetricsInterval).UnixMilli())

	// Test Resource Manager crash where currentTimestamp - previousTimestamp > 3x interval.
	currentTimestamp, _ = time.Parse(layout, "2023-10-16 16:11:05")
	previousTimestamp, _ = time.Parse(layout, "2023-10-16 16:07:05")

	meterValue, meterTimeInMillis = checkTimestamps(previousTimestamp, currentTimestamp)

	// Meter value of 0 and provided previousTimestamp + 1x interval should be returned since the
	// time elapsed between currentTimestamp and previousTimestamp > 3x interval.
	require.Equal(t, float64(0), meterValue)
	require.Equal(t, meterTimeInMillis, previousTimestamp.Add(collectComputeMetricsInterval).UnixMilli())

	// Test new Compute Resource where only CurrentTimestamp is available.
	currentTimestamp, _ = time.Parse(layout, "2023-10-16 16:12:05")
	previousTimestamp = time.Time{}

	meterValue, meterTimeInMillis = checkTimestamps(previousTimestamp, currentTimestamp)

	// Meter value of 1 and provided current timestamp should be returned for newly provisioned
	// Compute Resource.
	require.Equal(t, float64(1), meterValue)
	require.Equal(t, meterTimeInMillis, currentTimestamp.UnixMilli())
}

func TestCreateAddOnMeterEvent(t *testing.T) {
	orgId := "test-org"
	meterTime, _ := time.Parse(layout, "2023-10-16 16:12:05")
	meterTimeInMillis := meterTime.UnixMilli()
	workSpaceName := "test-ws"
	region := "test-region"
	addOnName := "CoPilot"
	workgroupName := "testWorkgroup"
	id := "test-id"
	platform := "aws"
	workspaceType := "small"

	input := &addOnMeterInput{
		ResourceID:        id,
		OrgID:             orgId,
		WorkgroupName:     workgroupName,
		WorkspaceName:     workSpaceName,
		Region:            region,
		Type:              addOnName,
		Platform:          platform,
		WorkspaceType:     workspaceType,
		MeterTimeInMIllis: meterTimeInMillis,
	}

	event := createAddOnMeterEvent(input)

	require.Equal(t, orgId, event.CustomerID)
	require.Equal(t, meterTimeInMillis, event.MeterTimeInMillis)
	require.Equal(t, workSpaceName, event.Dimensions["WorkspaceName"])
	require.Equal(t, addOnName, event.Dimensions["Type"])
	require.Equal(t, region, event.Dimensions["Region"])
	require.Equal(t, workgroupName, event.Dimensions["WorkgroupName"])
	require.Equal(t, id, event.Dimensions["ResourceID"])
	require.Equal(t, id, event.Dimensions["Resource_ID"])
}

type mockSNSClient struct {
	mock.Mock
	SNSClient
}

func (m *mockSNSClient) Publish(input *sns.PublishInput) (*sns.PublishOutput, error) {
	args := m.Called(input)
	return args.Get(0).(*sns.PublishOutput), args.Error(1)
}

func TestCollectComputeMetrics(t *testing.T) {
	rm := &pb.MockResourceManager{}
	rm.On("GetAllWorkspaceMetrics", mock.Anything).Return([]pb.WorkspaceMetric{
		{
			Name:     "test-name",
			ID:       "test-id",
			EnableHA: true,
			Addons: []pb.WorkspaceAddonsRequest{
				{
					UniqueName: "test-addon",
				},
			},
		},
	}, nil)

	svc := &MetricCollectionService{
		ResourceManagerClient: rm,
		Config: &config.Config{
			IgnoreMetricCollection: []string{"test-org"},
			SNSARN:                 "test-topic",
		},
	}

	meterEvents := []MeterEvent{
		{
			CustomerID:        "",
			MeterApiName:      "ComputeUsage",
			MeterValue:        0,
			MeterTimeInMillis: -62135596740000,
			Dimensions: map[string]string{
				"CloudProvider": "",
				"HA":            "enabled",
				"Platform":      "",
				"Region":        "",
				"ResourceID":    "test-id",
				"ResourceName":  "test-name",
				"Resource_ID":   "test-id",
				"WorkgroupName": "",
				"WorkspaceType": "",
			},
		},
		{
			CustomerID:        "",
			MeterApiName:      "AddOn",
			MeterValue:        0,
			MeterTimeInMillis: -62135596740000,
			Dimensions: map[string]string{
				"CloudProvider": "",
				"Platform":      "",
				"Region":        "",
				"ResourceID":    "test-id",
				"Resource_ID":   "test-id",
				"Type":          "test-addon",
				"WorkgroupName": "",
				"WorkspaceName": "test-name",
				"WorkspaceType": "",
			},
		},
	}

	jsonMsg, err := json.Marshal(meterEvents)
	require.NoError(t, err)

	input := &sns.PublishInput{
		Message: aws.String(string(jsonMsg)),

		TopicArn: aws.String("test-topic"),
	}
	snsClient := &mockSNSClient{}
	snsClient.On("Publish", input).Return(&sns.PublishOutput{}, nil)

	svc.collectComputeMetrics(snsClient)

	snsClient.AssertExpectations(t)
}
