package metriccollection

import (
	"context"
	"time"

	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/set"
)

// Package variable so we can stub for testing.
var timeNow = time.Now

func (svc *MetricCollectionService) collectComputeMetrics(snsClient SNSClient) {
	log := logger.L()
	// Call Resource Manager for list of compute resources.
	computeResources, err := svc.ResourceManagerClient.GetAllWorkspaceMetrics(context.Background())
	if err != nil {
		log.Error("error getting compute resources from resource manager: ", err)
	}

	ignoreOrgSet := set.New()
	for _, orgID := range svc.Config.IgnoreMetricCollection {
		ignoreOrgSet.Add(orgID)
	}

	eventCount := 0
	batchEvents := []*MeterEvent{}

	for _, computeResource := range computeResources {
		if ignoreOrgSet.Has(computeResource.OrgID) {
			continue
		}

		meterValue, meterTimeInMillis := checkTimestamps(computeResource.PreviousTimestamp, computeResource.CurrentTimestamp)

		HA := "disabled"
		if computeResource.EnableHA {
			HA = "enabled"
		}
		meterEvent := &MeterEvent{
			CustomerID:        computeResource.OrgID,
			MeterApiName:      constants.ComputeUsage,
			MeterValue:        meterValue,
			MeterTimeInMillis: meterTimeInMillis,
			Dimensions: map[string]string{
				"ResourceID":    computeResource.ID,
				"Resource_ID":   computeResource.ID,
				"ResourceName":  computeResource.Name,
				"WorkgroupName": computeResource.WorkgroupName,
				"Region":        computeResource.Region,
				"Platform":      string(computeResource.Platform),
				"WorkspaceType": computeResource.WorkspaceType,
				"CloudProvider": computeResource.CloudProvider,
				"HA":            HA,
			},
		}

		batchEvents = append(batchEvents, meterEvent)
		eventCount++

		for _, addon := range computeResource.Addons {
			// Skip sending meter for free addons.
			if addon.IsFree {
				continue
			}

			addonMeterValue := 0

			// If addon enabled and workspace running send running meter, o/w send off meter.
			if addon.Enable && meterValue == 1 {
				addonMeterValue = 1
			}

			input := &addOnMeterInput{
				ResourceID:        computeResource.ID,
				OrgID:             computeResource.OrgID,
				WorkgroupName:     computeResource.WorkgroupName,
				WorkspaceName:     computeResource.Name,
				Region:            computeResource.Region,
				Type:              addon.UniqueName,
				Platform:          string(computeResource.Platform),
				WorkspaceType:     computeResource.WorkspaceType,
				CloudProvider:     computeResource.CloudProvider,
				MeterValue:        float64(addonMeterValue),
				MeterTimeInMIllis: meterTimeInMillis,
			}

			addOnMeterEvent := createAddOnMeterEvent(input)
			batchEvents = append(batchEvents, addOnMeterEvent)
			eventCount++
		}

		if eventCount >= batchCount {
			err := svc.writeToSNS(batchEvents, snsClient)
			if err != nil {
				log.Errorf("error writing metrics to SNS: %v\n metrics: %v", err, batchEvents)
			}

			eventCount = 0
			batchEvents = []*MeterEvent{}
		}
	}

	// Send remaining meter events.
	if eventCount > 0 {
		err := svc.writeToSNS(batchEvents, snsClient)
		if err != nil {
			log.Errorf("error writing metrics to SNS: %v\n metrics: %v", err, batchEvents)
		}
	}
}

type addOnMeterInput struct {
	ResourceID        string
	OrgID             string
	WorkgroupName     string
	WorkspaceName     string
	Region            string
	Type              string
	Platform          string
	WorkspaceType     string
	CloudProvider     string
	MeterValue        float64
	MeterTimeInMIllis int64
}

func createAddOnMeterEvent(input *addOnMeterInput) *MeterEvent {
	return &MeterEvent{
		CustomerID:        input.OrgID,
		MeterApiName:      constants.AddOn,
		MeterValue:        input.MeterValue,
		MeterTimeInMillis: input.MeterTimeInMIllis,
		Dimensions: map[string]string{
			"ResourceID":    input.ResourceID,
			"Resource_ID":   input.ResourceID,
			"WorkgroupName": input.WorkgroupName,
			"WorkspaceName": input.WorkspaceName,
			"Region":        input.Region,
			"Type":          input.Type,
			"Platform":      input.Platform,
			"WorkspaceType": input.WorkspaceType,
			"CloudProvider": input.CloudProvider,
		},
	}
}

// checkTimestamps checks two timestamps which should be the current and previous heartbeat from the
// ResourceManager of a specific Compute Resource and returns the meter value and and meter time
// based on them.
func checkTimestamps(previousTimestamp time.Time, currentTimestamp time.Time) (meterValue float64, meterTimeInMillis int64) {
	meterValue = 1
	meterTimeInMillis = currentTimestamp.UnixMilli()

	// If (stopInterval * interval) have passed since last heartbeat we consider the
	// Compute Resource to have been stopped.
	currentTime := timeNow()
	if currentTime.After(currentTimestamp.Add(stopComputeInterval * collectComputeMetricsInterval)) {
		// We set the timestamp of the meter to the last received heartbeat + 1x interval.
		meterTimeInMillis = currentTimestamp.Add(collectComputeMetricsInterval).UnixMilli()
		meterValue = 0
	} else if !previousTimestamp.IsZero() && currentTimestamp.Sub(previousTimestamp) > stopComputeInterval*collectComputeMetricsInterval {
		// If currentTimestamp - previousTimestamp > (stopInterval * interval), we send stop event with
		// timestamp as previousTimestamp + 1x interval. This for the case if Resource Manager crash.
		meterValue = 0
		meterTimeInMillis = previousTimestamp.Add(collectComputeMetricsInterval).UnixMilli()
	}

	return meterValue, meterTimeInMillis
}
