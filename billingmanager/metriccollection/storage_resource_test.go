package metriccollection

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	"github.com/tigergraph/cloud-universe/common/pb"
)

func TestCollectStorageMetrics(t *testing.T) {
	rm := &pb.MockResourceManager{}
	rm.On("GetAllTGDatabaseMetrics", mock.Anything).Return([]pb.TGDatabaseMetric{
		{
			Name:      "test-name",
			ID:        "test-id",
			DeletedAt: time.UnixMilli(1740530293266),
		},
	}, nil)

	svc := &MetricCollectionService{
		ResourceManagerClient: rm,
		Config: &config.Config{
			IgnoreMetricCollection: []string{"test-org"},
			SNSARN:                 "test-topic",
		},
	}

	meterEvents := []MeterEvent{
		{
			CustomerID:        "",
			MeterApiName:      "DataStorage",
			MeterValue:        0,
			MeterTimeInMillis: 1740530293266,
			Dimensions: map[string]string{
				"Platform":      "",
				"Region":        "",
				"ResourceID":    "test-id",
				"ResourceName":  "test-name",
				"Resource_ID":   "test-id",
				"WorkgroupName": "",
			},
		},
	}

	jsonMsg, err := json.Marshal(meterEvents)
	require.NoError(t, err)

	input := &sns.PublishInput{
		Message: aws.String(string(jsonMsg)),

		TopicArn: aws.String("test-topic"),
	}
	snsClient := &mockSNSClient{}
	snsClient.On("Publish", input).Return(&sns.PublishOutput{}, nil)

	svc.collectStorageMetrics(snsClient)

	snsClient.AssertExpectations(t)
}
