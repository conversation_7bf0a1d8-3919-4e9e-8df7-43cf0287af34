package metriccollection

import (
	"encoding/json"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/tigergraph/cloud-universe/billingmanager/config"
	bmConstants "github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/utils/lock"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

const (
	collectComputeMetricsInterval  = 1 * time.Minute // Keep compute interval hardcoded as it affects heartbeat calculation.
	batchCount                     = 10              // Number of meter events to batch before sending to Amberflo.
	stopComputeInterval            = 3
	computeMetricCollectionLockKey = "job/metrics/collection/compute"
	storageMetricCollectionLockKey = "job/metrics/collection/storage"
)

// Struct for standardized meter format accepted by Amberflo ingest API.
type MeterEvent struct {
	CustomerID        string            `json:"customerId"`
	MeterApiName      bmConstants.Meter `json:"meterApiName"`
	MeterValue        float64           `json:"meterValue"`
	MeterTimeInMillis int64             `json:"meterTimeInMillis"`
	Dimensions        map[string]string `json:"dimensions"`
}

type SNSClient interface {
	Publish(input *sns.PublishInput) (*sns.PublishOutput, error)
}

type MetricCollectionService struct {
	ResourceManagerClient pb.ResourceManager
	LockSvc               lock.DLockService
	Config                *config.Config
}

func NewMetricCollectionService(file string, lockSvc lock.DLockService, resourceManagerClient pb.ResourceManager) (*MetricCollectionService, error) {
	cfg, err := config.LoadConfig(file)
	if err != nil {
		return nil, err
	}

	return &MetricCollectionService{
		ResourceManagerClient: resourceManagerClient,
		LockSvc:               lockSvc,
		Config:                &cfg,
	}, nil
}

func (svc *MetricCollectionService) CollectMetrics() {
	if svc.Config.EnableMetricCollection {
		// Initialize session for SNS service
		snsSess := session.Must(session.NewSessionWithOptions(session.Options{
			SharedConfigState: session.SharedConfigEnable,
			Config: aws.Config{
				Region: aws.String(svc.Config.SNSRegion),
			},
		}))

		snsSvc := sns.New(snsSess)

		go svc.callCollectComputeMetrics(snsSvc)
		go svc.callCollectStorageMetrics(snsSvc)
	}
}

// callCollectComputeMetrics calls is wrapper function to acquire lock, call 'collectComputeMetrics',
// and sleep based on interval.
func (svc *MetricCollectionService) callCollectComputeMetrics(snsSvc *sns.SNS) {
	log := logger.L()
	for {
		lock, err := svc.LockSvc.NewLock(computeMetricCollectionLockKey, true)
		if err == nil {
			if err := lock.Lock(); err != nil {
				log.Errorf("Failed to obtain lock: %v", err)
			} else {
				svc.collectComputeMetrics(snsSvc)

				// We sleep here and unlock afterwards to avoid the scenario where another instance
				// is waiting to acquire the lock before the interval since the last metric collection has elapsed.
				time.Sleep(collectComputeMetricsInterval)
				lock.Unlock()
				continue
			}
		}

		time.Sleep(collectComputeMetricsInterval)
	}

}

// callCollectStorageMetrics calls is wrapper function to acquire lock, call 'collectStorageMetrics',
// and sleep based on interval.
func (svc *MetricCollectionService) callCollectStorageMetrics(snsSvc *sns.SNS) {
	log := logger.L()
	for {
		lock, err := svc.LockSvc.NewLock(storageMetricCollectionLockKey, true)
		if err == nil {
			if err := lock.Lock(); err != nil {
				log.Errorf("Failed to obtain lock: %v", err)
			} else {
				svc.collectStorageMetrics(snsSvc)

				// We sleep here and unlock afterwards to avoid the scenario where another instance
				// is waiting to acquire the lock before the interval since the last metric collection has elapsed.
				time.Sleep(time.Duration(svc.Config.StorageMetricInterval) * time.Minute)
				lock.Unlock()
				continue
			}
		}

		time.Sleep(time.Duration(svc.Config.StorageMetricInterval) * time.Minute)
	}
}

func (svc *MetricCollectionService) writeToSNS(meterEvents []*MeterEvent, snsClient SNSClient) error {
	// Convert to JSON.
	jsonMsg, err := json.Marshal(meterEvents)
	if err != nil {
		return err
	}

	// Write to SNS.
	_, err = snsClient.Publish(&sns.PublishInput{
		Message:  aws.String(string(jsonMsg)),
		TopicArn: &svc.Config.SNSARN,
	})

	if err != nil {
		return err
	}

	return nil
}
