package metriccollection

import (
	"context"
	"time"

	"github.com/dustin/go-humanize"
	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/set"
)

func (svc *MetricCollectionService) collectStorageMetrics(snsClient SNSClient) {
	log := logger.L()
	// Call Resource Manager for list of storage resources.
	storageResources, err := svc.ResourceManagerClient.GetAllTGDatabaseMetrics(context.Background())
	if err != nil {
		log.Error("error getting storage resources from resource manager: ", err)
	}

	ignoreOrgSet := set.New()
	for _, orgID := range svc.Config.IgnoreMetricCollection {
		ignoreOrgSet.Add(orgID)
	}

	eventCount := 0
	batchEvents := []*MeterEvent{}

	for _, storageResource := range storageResources {
		if ignoreOrgSet.Has(storageResource.OrgID) {
			continue
		}

		meterTime := storageResource.DeletedAt
		if meterTime.IsZero() {
			meterTime = time.Now()
		}

		// Convert to GiB.
		meterValue := float64(0)
		if storageResource.FolderStatistics != nil {
			meterValue = float64(storageResource.FolderStatistics.BilledSize) / humanize.GiByte
		}

		// Don't start charging customer until they reach 1 GB so we send 0.
		if meterValue < 1 {
			meterValue = 0
		}

		meterEvent := &MeterEvent{
			CustomerID:        storageResource.OrgID,
			MeterApiName:      constants.DataStorage,
			MeterValue:        meterValue,
			MeterTimeInMillis: meterTime.UnixMilli(),
			Dimensions: map[string]string{
				"ResourceID":    storageResource.ID,
				"Resource_ID":   storageResource.ID,
				"ResourceName":  storageResource.Name,
				"WorkgroupName": storageResource.WorkgroupName,
				"Region":        storageResource.Region,
				"Platform":      string(storageResource.Platform),
			},
		}

		batchEvents = append(batchEvents, meterEvent)

		eventCount++
		if eventCount >= batchCount {
			err := svc.writeToSNS(batchEvents, snsClient)
			if err != nil {
				log.Errorf("error writing metrics to SNS: %v\n metrics: %v", err, meterEvent)
			}

			eventCount = 0
			batchEvents = []*MeterEvent{}
		}
	}

	// Send remaining meter events.
	if eventCount > 0 {
		err := svc.writeToSNS(batchEvents, snsClient)
		if err != nil {
			log.Errorf("error writing metrics to SNS: %v\n metrics: %v", err, batchEvents)
		}
	}
}
