package observability

import (
	"github.com/prometheus/client_golang/prometheus"
)

const (
	StatusSuccess = "success"
	StatusFailed  = "failed"
)

type ClusterAgentMetrics struct {
	CloudProviderId string

	TotalRequestsCount   prometheus.Counter
	SuccessRequestsCount prometheus.Counter
	ErrorRequestsCount   prometheus.Counter
	RequestsCountTypes   *prometheus.CounterVec
	HeartbeatCount       prometheus.Counter
	MessagesSentCount    *prometheus.CounterVec
	ReconnectCount       prometheus.Counter
}

func NewClusterAgentMetrics(cloudProviderId string) *ClusterAgentMetrics {

	return &ClusterAgentMetrics{
		CloudProviderId: cloudProviderId,

		TotalRequestsCount: prometheus.NewCounter(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_total_requests_count",
			Help:      "Total number of total processed requests",
		}),

		SuccessRequestsCount: prometheus.NewCounter(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_success_requests_count",
			Help:      "Total number of success requests",
		}),

		ErrorRequestsCount: prometheus.NewCounter(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_error_requests_count",
			Help:      "Total number of error requests",
		}),

		RequestsCountTypes: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_requests_count_per_type",
			Help:      "Number of requests per type",
		}, []string{"request_type"}),

		HeartbeatCount: prometheus.NewCounter(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_heartbeat_count",
			Help:      "Total number of heartbeats reported",
		}),

		MessagesSentCount: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_messages_sent_count",
			Help:      "Total number of messages sent",
		}, []string{"status"}),

		ReconnectCount: prometheus.NewCounter(prometheus.CounterOpts{
			Namespace: "clusteragent",
			Name:      "clusteragent_reconnect_count",
			Help:      "Total number of reconnects",
		}),
	}

}

func (cam *ClusterAgentMetrics) RegisterClusterAgentMetrics() {
	prometheus.MustRegister(cam.TotalRequestsCount)
	prometheus.MustRegister(cam.SuccessRequestsCount)
	prometheus.MustRegister(cam.ErrorRequestsCount)
	prometheus.MustRegister(cam.HeartbeatCount)
	prometheus.MustRegister(cam.MessagesSentCount)
	prometheus.MustRegister(cam.RequestsCountTypes)
	prometheus.MustRegister(cam.ReconnectCount)
}

func (cam *ClusterAgentMetrics) UnegisterClusterAgentMetrics() {
	prometheus.Unregister(cam.TotalRequestsCount)
	prometheus.Unregister(cam.SuccessRequestsCount)
	prometheus.Unregister(cam.ErrorRequestsCount)
	prometheus.Unregister(cam.HeartbeatCount)
	prometheus.Unregister(cam.MessagesSentCount)
	prometheus.Unregister(cam.RequestsCountTypes)
	prometheus.Unregister(cam.ReconnectCount)
}

func (cam *ClusterAgentMetrics) IncreaseTotalRequestsCount() {
	cam.TotalRequestsCount.Inc()
}

func (cam *ClusterAgentMetrics) IncreaseSuccessRequestsCount() {
	cam.SuccessRequestsCount.Inc()
}

func (cam *ClusterAgentMetrics) IncreaseErrorRequestsCount() {
	cam.ErrorRequestsCount.Inc()
}

func (cam *ClusterAgentMetrics) IncreaseHeartbeatCount() {
	cam.HeartbeatCount.Inc()
}

func (cam *ClusterAgentMetrics) IncreaseMessagesSentCount(label string) {
	cam.MessagesSentCount.With(prometheus.Labels{"status": label}).Inc()
}

func (cam *ClusterAgentMetrics) IncreaseReconnectCount() {
	cam.ReconnectCount.Inc()
}

func (cam *ClusterAgentMetrics) IncreaseRequestsCountTypes(label string) {
	cam.RequestsCountTypes.WithLabelValues(label).Inc()
}
