package util

import (
	"fmt"
	"github.com/go-resty/resty/v2"
	"net/http"
	"strconv"
	"strings"
)

// GetRequestCount get the number of unprocessed request of the tg agent from exposed openmetrics port.
// It's used when the maintenance agent is checking if the origin tg agent finish all its request.
func GetRequestCount() (int, error) {

	queryURL := fmt.Sprintf("http://127.0.0.1%v/metrics", exporterPort)
	client := resty.New()
	resp, err := client.R().Get(queryURL)
	if err != nil || resp.StatusCode() != http.StatusOK {
		return -1, fmt.Errorf("GetRequestCount: unable to get request count: %w", err)
	}

	// Remove everything before the agent_version header
	header := "# TYPE tg_agent_request_cnt gauge"
	content := resp.String()
	i := strings.Index(content, header)
	content = content[i+len(header)+1:]

	// Remove everything before name the agent_version
	name := "tg_agent_request_cnt"
	i = strings.Index(content, name)
	content = content[i+len(name)+1:]

	// Remove everything after the version number
	i = strings.Index(content, "\n")
	content = content[:i]

	cnt, err := strconv.Atoi(content)
	if err != nil {
		return -1, fmt.Errorf("GetRequestCount: unable to convert count string to int: %w", err)
	}

	return cnt, nil
}
