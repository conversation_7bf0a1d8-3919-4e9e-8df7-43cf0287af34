package util

import (
	"os"
	"strings"

	"github.com/spf13/viper"
	"github.com/tigergraph/cloud-universe/clusteragent/internal/zaplg"
)

var (
	config       Configuration
	exporterPort string
)

func init() {

	// Paths to search the config file
	viper.AddConfigPath(".")
	viper.AddConfigPath("./client")
	viper.AddConfigPath("./cmd/client")
	viper.AddConfigPath("/etc/tg_agent/")

	// Paths for development only
	viper.AddConfigPath("..")
	viper.AddConfigPath("../client")
	viper.AddConfigPath("../cmd/client")

	// Set name of the config file
	viper.SetConfigName("tg_agent_config")
	viper.SetConfigType("yaml")

	viper.AutomaticEnv()

	err := viper.ReadInConfig()
	if err != nil {
		zaplg.SugarLog.Errorf("client main: error in loading config")
		os.Exit(1)
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		zaplg.SugarLog.Errorf("client main: error in loading config2")
		os.Exit(1)
	}

	i := strings.Index(config.ExporterAddr, ":")
	exporterPort = config.ExporterAddr[i:]

}
