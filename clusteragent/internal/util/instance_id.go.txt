package util

import (
	"fmt"
	"github.com/go-resty/resty/v2"
	"net/http"
	"time"
)

func GetInstanceIDAWS() string {
	queryURL := "http://169.254.169.254/latest/meta-data/instance-id"
	client := resty.New()
	client.SetTimeout(time.Second * 2)
	resp, err := client.R().
		Get(queryURL)

	if err != nil || resp.StatusCode() != http.StatusOK {
		return ""
	}
	return resp.String()
}
func GetInstanceIDGCP() string {
	queryURL := "http://169.254.169.254/computeMetadata/v1/instance/name"
	client := resty.New()
	client.SetTimeout(time.Second * 2)
	resp, err := client.R().
		SetHeader("Metadata-Flavor", "Google").
		Get(queryURL)
	if err != nil || resp.StatusCode() != http.StatusOK {
		return ""
	}

	return resp.String()
}

type ApiVersion struct {
	ApiVersions []string `json:"apiVersions"`
}

type AzureInfo struct {
	Compute map[string]interface{} `json:"compute"`
}

func GetInstanceAzure() string {

	var ApiVersion ApiVersion

	queryURL := "http://169.254.169.254/metadata/versions"
	client := resty.New()
	client.SetTimeout(time.Second * 2)

	resp, err := client.R().SetHeader("Metadata", "true").SetResult(&ApiVersion).Get(queryURL)

	if err != nil || resp.StatusCode() != http.StatusOK {
		return ""
	}

	newestVer := ApiVersion.ApiVersions[len(ApiVersion.ApiVersions)-1]

	queryURL = fmt.Sprintf("http://169.254.169.254/metadata/instance?api-version=%v", newestVer)

	var info AzureInfo
	resp, err = client.R().SetHeader("Metadata", "true").SetResult(&info).Get(queryURL)

	if err != nil || resp.StatusCode() != http.StatusOK {
		return ""
	}
	return info.Compute["name"].(string)
}

func GetInstanceID() string {
	idAWS := GetInstanceIDAWS()
	idGCP := GetInstanceIDGCP()
	idAzure := GetInstanceAzure()

	// Only one of them will contain the actual instance id depending on CSP so concatenating will give the result
	return idAWS + idGCP + idAzure

}
