package util

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/tigergraph/cloud-universe/clusteragent/internal/zaplg"
)

// GetAgentVersion get the version of tg agent from exposed openmetrics port. It's used when updating a tg_agent
func GetAgentVersion() (string, error) {

	queryURL := fmt.Sprintf("http://127.0.0.1%v/metrics", exporterPort)
	client := resty.New()
	resp, err := client.R().Get(queryURL)
	if err != nil || resp.StatusCode() != http.StatusOK {
		return "", fmt.Errorf("GetAgentVersion: unable to get agent version: %w", err)
	}

	// Remove everything before the agent_version header
	header := "# TYPE tg_agent_version gauge"
	content := resp.String()
	i := strings.Index(content, header)
	content = content[i+len(header)+1:]

	// Remove everything before the "
	i = strings.Index(content, "\"")
	content = content[i+1:]

	// Remove everything after the "
	i = strings.Index(content, "\"")
	content = content[:i]
	zaplg.SugarLog.Debugf("agent version from openmetric: %v", content)
	return content, nil
}
