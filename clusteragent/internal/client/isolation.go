package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) SetIsolationRule(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing set isolation rule")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetIsolationRule, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	var rule data.IsolationRule
	err := json.Unmarshal(params["request"], &rule)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetIsolationRule, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.SetIsolationRule(ctx, string(namespace), &rule)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetIsolationRule, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetIsolationRule, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"isolationRule": resp,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetIsolationRule, req.Initiator, respParams, 0, "")
}

func (c *Client) ApplyIsolationRule(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing apply isolation rule")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdApplyIsolationRule, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	ruleName, ok := params["ruleName"]
	if !ok {
		log.Error("failed to read ruleName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdApplyIsolationRule, req.Initiator, nil, 1, "error reading ruleName")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspaceName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdApplyIsolationRule, req.Initiator, nil, 1, "error reading workspaceName")
		return
	}

	err := c.OpClient.ApplyIsolationRule(ctx, string(namespace), string(ruleName), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdApplyIsolationRule, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdApplyIsolationRule, req.Initiator, respParams, 0, "")
}

func (c *Client) RemoveIsolationRule(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing remove isolation rule")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRemoveIsolationRule, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	ruleName, ok := params["ruleName"]
	if !ok {
		log.Error("failed to read ruleName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRemoveIsolationRule, req.Initiator, nil, 1, "error reading ruleName")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspaceName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRemoveIsolationRule, req.Initiator, nil, 1, "error reading workspaceName")
		return
	}

	err := c.OpClient.RemoveIsolationRule(ctx, string(namespace), string(ruleName), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRemoveIsolationRule, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRemoveIsolationRule, req.Initiator, respParams, 0, "")
}
