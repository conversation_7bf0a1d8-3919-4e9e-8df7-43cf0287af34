package client

import (
	"context"
	"time"

	"github.com/google/uuid"
	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	pb "github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	tq "github.com/tigergraph/cloud-universe/utils/taskqueue"
	"google.golang.org/protobuf/types/known/timestamppb"

	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
)

// PeriodUpdate check update for instances in the defined time period
func (c *Client) startPeriodicHeartbeat() {
	//make sure we only start heartbeater once
	if !c.heartbeaterStarted {
		c.heartbeaterStarted = true

		params := map[string][]byte{
			"OrgId":             []byte(c.Config.OrgId),
			"ComponentVersions": []byte(c.componentVersion),
		}

		go func() {

			tick := time.Tick(time.Duration(c.Config.HeartbeatInterval) * time.Second)
			for {
				ctx := lmri.NewCtxWithReqId(context.Background())
				log := logger.L().WithContext(ctx)
				if !c.Config.NoK8SConnection {
					// fetch component vertsions from k8s
					versionMap, err := c.versionFetcher.FetchComponentVersions()
					var versionJson []byte
					if err != nil {
						log.Errorf("failed to fetch component versions: %v", err)
					} else {
						componentVersions := cp.NewComponentVersionWithMap(versionMap)
						versionJson, err = componentVersions.MarshalJSON()
						if err != nil {
							log.Errorf("failed to marshal component versions: %v, version: %v", err, componentVersions.String())
						} else {
							log.Infof("fetched k8s component versions: %v", string(versionJson))
						}
					}
					params["ComponentVersions"] = versionJson
				}

				if c.Stream == nil {
					log.Infof("PeriodicHeartbeat: preform nothing because client side gRPC stream is nil")
					continue
				}

				err := c.SendAgentMessage(ctx, &pb.AgentMessage{
					Id:        uuid.New().String(),
					Initiator: c.Id,
					Target:    c.Config.GRPCServerAddr,
					Type:      tq.AgentTaskTypeHeartBeat,
					Command:   "",
					Params:    params,
					Timestamp: timestamppb.New(time.Now()),
					Status:    0,
					Error:     "",
				})
				if err != nil {
					log.Warn(err.Error())
				} else {
					c.ClusterAgentMetrics.IncreaseHeartbeatCount()
				}
				<-tick
			}
		}()
	}
}
