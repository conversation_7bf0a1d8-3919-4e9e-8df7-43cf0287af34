package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) UpdateWorkspace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing update workspace")

	//UpdateWorkspace(namespace string, workspaceName string, request *data.UpdateWorkspaceRequest) (*data.Workspace, error)

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	reqPayload, ok := params["request"]
	if !ok {
		log.Error("failed to read request payload")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.UpdateWorkspaceRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.UpdateWorkspace(ctx, string(namespace), opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"workspace": resp,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdUpdateWorkspace, req.Initiator, respParams, 0, "")
}
