// Package client implements gRPC client logic to communicate with authentication server and gRPC server.
// In-service update mechanism is also realized in this package.
package client

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"math/rand"
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/clusteragent/config"
	"github.com/tigergraph/cloud-universe/controller/observability"
	"github.com/tigergraph/cloud-universe/resource-manager/interfaces"
	"github.com/tigergraph/cloud-universe/utils/logger"
	lmri "github.com/tigergraph/cloud-universe/utils/logger/middleware/requestid"
	tq "github.com/tigergraph/cloud-universe/utils/taskqueue"
	"go.uber.org/ratelimit"

	"google.golang.org/grpc/credentials/insecure"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	set "github.com/deckarep/golang-set/v2"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/status"

	cio "github.com/tigergraph/cloud-universe/clusteragent/internal/observability"
	cp "github.com/tigergraph/cloud-universe/common/cloudprovider"
	"github.com/tigergraph/cloud-universe/common/k8sclient"
	pb "github.com/tigergraph/cloud-universe/proto"

	"google.golang.org/grpc"
)

type Client struct {
	Id            string
	Version       string
	Token         string
	IsMaintenance bool

	Config   config.Config
	grpcConn *grpc.ClientConn
	Stream   pb.TGControlPlaneService_ControlAgentCommandsClient

	ClusterAgentMetrics *cio.ClusterAgentMetrics

	AwaitUpdateMap map[string]UpdateInfo
	OpClient       interfaces.TGOperator

	// heatbeater started
	heartbeaterStarted bool
	// component version
	componentVersion string

	k8scilent      *k8sclient.K8sClient
	versionFetcher cp.ComponentVersionFetcherInterface

	ObsServer *observability.Observer
}

type UpdateInfo struct {
	transactionId  string
	serviceName    string
	serviceVersion string
}

func (c *Client) StartClient(addr string, opts []grpc.DialOption) (pb.TGControlPlaneServiceClient, error) {
	conn, err := grpc.Dial(addr, opts...)
	if err != nil {
		return nil, fmt.Errorf("StartClient: failed to dial into server at %v: %w", addr, err)
	}
	c.grpcConn = conn
	c1 := pb.NewTGControlPlaneServiceClient(conn)
	return c1, nil
}

func (c *Client) Close() {
	log := logger.L()
	log.Info("closing cluster agent client")
	if c.grpcConn != nil {
		c.grpcConn.Close()
	}
	if c.ObsServer != nil {
		c.ObsServer.Close()
	}
	if c.ClusterAgentMetrics != nil {
		c.ClusterAgentMetrics.UnegisterClusterAgentMetrics()
	}
}

func (c *Client) StartClientWithRetry(opts []grpc.DialOption) pb.TGControlPlaneServiceClient {
	log := logger.L()
	for i := 0; i < c.Config.RetryCount; i++ {
		client, err := c.StartClient(c.Config.GRPCServerAddr, opts)

		st, ok := status.FromError(err)
		if !ok {
			log.Panicf("main client: fatal error when dialing into gRPC service: %v", err)
		}

		switch st.Code() {
		case codes.OK:
			return client
		case codes.Unknown:
			log.Panicf("main client: fatal error when dialing into gRPC service: %v", err)
		default:
			log.Errorf("main client: unable to dial into gRPC service: %v (Retry %v/%v)",
				err, i, c.Config.RetryCount)
		}

		time.Sleep(time.Duration(c.Config.RetryInterval) * time.Second)
	}

	log.Panicf("main client: unable to dial into gRPC service after %v attempts", c.Config.RetryCount)
	return nil
}

// Register this client with gRPC service
func (c *Client) RegisterWithCert(ctx context.Context, client pb.TGControlPlaneServiceClient) error {

	isMaintenance := "0"
	if c.IsMaintenance {
		isMaintenance = "1"
	}

	md := metadata.Pairs(
		"version", c.Version,
		"ismaint", isMaintenance,
		"id", c.Id,
		"token", c.Token)

	ctx = metadata.NewOutgoingContext(ctx, md)
	log := logger.L().WithContext(ctx)

	stream, err := client.ControlAgentCommands(ctx)
	if err != nil {
		log.Errorf("client [%v]: unable to register with the service: %v", c.Id, err)
		return err
	}
	// must set it before any RPC is sent over
	c.Stream = stream

	err = c.SendAgentMessage(ctx, &pb.AgentMessage{
		Id:        uuid.New().String(),
		Initiator: c.Id,
		Target:    c.Config.GRPCServerAddr,
		Type:      tq.AgentTaskTypeHeartBeat,
		Command:   "",
		Params:    map[string][]byte{},
		Timestamp: timestamppb.New(time.Now()),
		Status:    0,
		Error:     "",
	})

	if err != nil {
		log.Errorf("client [%v]: error in sending the registration request: %v", c.Id, err)
		return err
	}
	log.Debugf("version %v client [%v]: registered with the server, is maintenance: %v", c.Version, c.Id, isMaintenance)

	return nil
}

func (c *Client) SendAgentMessage(ctx context.Context, msg *pb.AgentMessage) error {
	log := logger.L().WithContext(ctx)
	if msg.Id == "" {
		msg.Id = uuid.New().String()
	}
	if msg.Status == 0 {
		// Success message
		c.ClusterAgentMetrics.IncreaseSuccessRequestsCount()
	} else {
		// Error message
		c.ClusterAgentMetrics.IncreaseErrorRequestsCount()
	}
	err := c.Stream.Send(lmri.ProtoMessageReqIdSetter(ctx, msg).(*pb.AgentMessage))
	if err != nil {
		c.ClusterAgentMetrics.IncreaseMessagesSentCount(cio.StatusFailed)
		log.Warnf("SendHeartbeat: failed [target] %v, [transactionId] %v", msg.Target, msg.Id)
		return err
	}
	c.ClusterAgentMetrics.IncreaseMessagesSentCount(cio.StatusSuccess)
	return nil
}

func (c *Client) RegisterWithCertWithRetry(c1 pb.TGControlPlaneServiceClient) {
	for i := 0; i < c.Config.RetryCount; i++ {
		ctx := lmri.NewCtxWithReqId(context.Background())
		log := logger.L().WithContext(ctx)
		err := c.RegisterWithCert(ctx, c1)
		st, ok := status.FromError(err)
		if !ok {
			log.Panicf("main client: fatal error when registering with gRPC Service: %v", err)
		}

		switch st.Code() {
		case codes.OK:
			return
		case codes.Unknown:
			log.Panicf("main client: fatal error when registering with gRPC service: %v", err)
		default:
			log.Errorf("main client: unable to register with gRPC service: %v (Retry %v/%v)",
				err, i, c.Config.RetryCount)
		}

		time.Sleep(time.Duration(c.Config.RetryInterval) * time.Second)
	}

	logger.L().Panicf("main client: unable to register with gRPC service after %v attempts", c.Config.RetryCount)
}

func (c *Client) LoadClientCredentials(clientCertBytes, clientKeyBytes []byte) (credentials.TransportCredentials, error) {

	certPool, _ := x509.SystemCertPool()

	clientCert, err := tls.X509KeyPair(clientCertBytes, clientKeyBytes)
	if err != nil {
		return nil, fmt.Errorf("LoadClientCredentials_MTLS: failed to load key-value cert pair: %w", err)
	}

	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{clientCert},
		RootCAs:      certPool,
	}
	return credentials.NewTLS(tlsConfig), nil
}

func (c *Client) SendMsgToServer(ctx context.Context, msgType, transactionId, command, target string, params map[string][]byte, status int32, commandErr string) error {
	log := logger.L().WithContext(ctx)
	id, _ := uuid.NewRandom()
	err := c.SendAgentMessage(ctx, &pb.AgentMessage{
		Id:        id.String(),
		Type:      msgType,
		Command:   command,
		Params:    params,
		Target:    target,
		Initiator: c.Id,
		ReplyToId: transactionId,
		Timestamp: timestamppb.New(time.Now()),
		Status:    status,
		Error:     commandErr,
	})
	if err != nil {
		log.Warnf("SendMsgToServer: failed [headline] %v, [transactionId] %v, [detail] %v, [status] %v", msgType, transactionId, command, status)
	}
	return err
}

type TransactionMgmt struct {
	TransSet    set.Set[string]
	TransBuffer chan TransactionInfo
}

type TransactionInfo struct {
	transactionId string
	task          string
	payload       []byte
}

const (
	maxConcurrentReq = 100
)

func (c *Client) ReceiveMsgFromServer() *errgroup.Group {
	// we use errgroup without error propagation since processRequest always return nil
	g := new(errgroup.Group)
	g.SetLimit(maxConcurrentReq)

	g.Go(func() error {
		for {
			req, err := c.Stream.Recv()
			ctx := lmri.NewContextFromMessage(context.Background(), req)
			log := logger.L().WithContext(ctx)
			if err != nil {
				log.Error("recv err", err.Error())
				return err
			}
			g.Go(func() error {
				return c.processRequest(req)
			})
		}
	})
	return g
}

func (c *Client) processRequest(req *pb.ControlMessage) error {
	ctx := lmri.NewContextFromMessage(context.Background(), req)
	log := logger.L().WithContext(ctx)
	msgType := req.GetType()
	command := req.GetCommand()
	transactionId := req.GetId()

	c.ClusterAgentMetrics.IncreaseTotalRequestsCount()

	switch msgType {
	case tq.ControllerTaskTypePing:
		log.Info("Cluster Agent: received message ping")
		c.Pong(ctx, req)

	case tq.ControllerTaskTypeGeneral:
		c.ClusterAgentMetrics.IncreaseRequestsCountTypes(command)
		switch command {
		case tq.ControllerCmdListWorkspaces:
			log.Info("Cluster Agent: received message ListWorkspace")
			c.ListWorkspaces(ctx, req)
		case tq.ControllerCmdGetWorkspace:
			log.Info("Cluster Agent: received message GetWorkspace")
			c.GetWorkspace(ctx, req)
		case tq.ControllerCmdCreateWorkspace:
			log.Info("Cluster Agent: received message CreateWorkspace")
			c.CreateWorkspaces(ctx, req)
		case tq.ControllerCmdUpdateWorkspace:
			log.Info("Cluster Agent: received message UpdateWorkspace")
			c.UpdateWorkspace(ctx, req)
		case tq.ControllerCmdDeleteWorkspace:
			log.Info("Cluster Agent: received message DeleteWorkspace")
			c.DeleteWorkspace(ctx, req)
		case tq.ControllerCmdExportWorkspace:
			log.Info("Cluster Agent: received message ExportWorkspace")
			c.ExportWorkspace(ctx, req)
		case tq.ControllerCmdImportWorkspace:
			log.Info("Cluster Agent: received message ImportWorkspace")
			c.ImportWorkspace(ctx, req)
		case tq.ControllerCmdPauseWorkspace:
			log.Info("Cluster Agent: received message PauseWorkspace")
			c.PauseWorkspace(ctx, req)
		case tq.ControllerCmdResumeWorkspace:
			log.Info("Cluster Agent: received message ResumeWorkspace")
			c.ResumeWorkspace(ctx, req)

		case tq.ControllerCmdCreateWorkgroup:
			log.Info("Cluster Agent: received message CreateWorkgroup")
			c.CreateNamespace(ctx, req)
		case tq.ControllerCmdDeleteWorkgroup:
			log.Info("Cluster Agent: received message DeleteWorkgroup")
			c.DeleteNamespace(ctx, req)
		case tq.ControllerCmdCreateStorageClass:
			log.Info("Cluster Agent: received message CreateStorageClass")
			c.CreateStorageClass(ctx, req)
		case tq.ControllerCmdDeleteStorageClass:
			log.Info("Cluster Agent: received message DeleteStorageClass")
			c.DeleteStorageClass(ctx, req)
		case tq.ControllerCmdCreateTGDatabsePVC:
			log.Info("Cluster Agent: received message CreateTGDatabsePVC")
			c.CreateTGDatabsePVC(ctx, req)
		case tq.ControllerCmdDeleteTGDatabsePVC:
			log.Info("Cluster Agent: received message DeleteTGDatabsePVC")
			c.DeleteTGDatabsePVC(ctx, req)
		case tq.ControllerCmdCreateServiceAccount:
			log.Info("Cluster Agent: received message CreateServiceAccount")
			c.CreateServiceAccount(ctx, req)
		case tq.ControllerCmdDeleteServiceAccount:
			log.Info("Cluster Agent: received message DeleteServiceAccount")
			c.DeleteServiceAccount(ctx, req)

		case tq.ControllerCmdCreateBackup:
			log.Info("Cluster Agent: received message CreateBackup")
			c.CreateBackup(ctx, req)
		case tq.ControllerCmdRestoreBackup:
			log.Info("Cluster Agent: received message RestoreBackup")
			c.RestoreBackup(ctx, req)
		case tq.ControllerCmdGetBackupList:
			log.Info("Cluster Agent: received message GetBackupList")
			c.ListBackups(ctx, req)
		case tq.ControllerCmdDeleteBackup:
			log.Info("Cluster Agent: received message DeleteBackup")
			c.DeleteBackup(ctx, req)
		case tq.ControllerCmdGetBackupRestoreStatus:
			log.Info("Cluster Agent: received message GetBackupRestoreStatus")
			c.GetBackupRestoreStatus(ctx, req)
		case tq.ControllerCmdSetBackupSchedule:
			log.Info("Cluster Agent: received message SetBackupSchedule")
			c.SetBackupSchedule(ctx, req)
		case tq.ControllerCmdGetBackupSchedule:
			log.Info("Cluster Agent: received message GetBackupSchedule")
			c.GetBackupSchedule(ctx, req)

		case tq.ControllerCmdGetWorkgroupLabels:
			log.Info("Cluster Agent: received message GetWorkgroupLabels")
			c.GetWorkgroupLabels(ctx, req)
		case tq.ControllerCmdSetIsolationRule:
			log.Info("Cluster Agent: received message SetIsolationRule")
			c.SetIsolationRule(ctx, req)
		case tq.ControllerCmdApplyIsolationRule:
			log.Info("Cluster Agent: received message ApplyIsolationRule")
			c.ApplyIsolationRule(ctx, req)
		case tq.ControllerCmdRemoveIsolationRule:
			log.Info("Cluster Agent: received message RemoveIsolationRule")
			c.RemoveIsolationRule(ctx, req)
		case tq.ControllerCmdDeleteResource:
			log.Info("Cluster Agent: received message DeleteResource")
			c.DeleteResource(ctx, req)
		case tq.ControllerCmdDeleteResourceCollection:
			log.Info("Cluster Agent: received message DeleteResourceCollection")
			c.DeleteResourceCollection(ctx, req)
		case tq.ControllerCmdGetResource:
			log.Info("Cluster Agent: received message GetResource")
			c.GetResource(ctx, req)
		case tq.ControllerCmdListResource:
			log.Info("Cluster Agent: received message ListResource")
			c.ListResource(ctx, req)

		case tq.ControllerCmdCreateKMSSecret:
			log.Infof("Cluster Agent: received message CreateKMSSecret")
			c.CreateKMSSecret(ctx, req)
		default:
			log.Errorf("Cluster Agent: received unsupported command %v", command)
			c.SendMsgToServer(ctx, tq.ControllerTaskTypeGeneral, req.Id, command, req.Initiator, nil, -1, "Unsupported command")
		}

	default:
		log.Warnf("Unsupported type: %s", msgType)
		c.SendMsgToServer(ctx, msgType, transactionId, command, req.Initiator, nil, -2, "Unsupported type")
	}
	return nil
}

func (c *Client) startClient(Conf *config.Config) (*errgroup.Group, error) {
	var opts []grpc.DialOption
	log := logger.L()
	if Conf.NonSecure {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	} else {
		certPool, err := x509.SystemCertPool()
		if err != nil {
			log.Errorf("failed to load system cert pool: %v", err.Error())
		}

		tlsConfig := &tls.Config{
			RootCAs: certPool,
		}

		cred := credentials.NewTLS(tlsConfig)
		opts = append(opts, grpc.WithTransportCredentials(cred))
	}

	opts = append(opts, grpc.WithDefaultCallOptions(
		grpc.MaxCallRecvMsgSize(Conf.MaxMsgSize),
		grpc.MaxCallSendMsgSize(Conf.MaxMsgSize)))

	keepaliveParams := keepalive.ClientParameters{
		Time:                10 * time.Second, // send pings every 10 seconds if there is no activity
		Timeout:             2 * time.Second,  // wait 2 seconds for ping ack before considering the connection dead
		PermitWithoutStream: true,             // send pings even without active streams
	}

	opts = append(opts, grpc.WithKeepaliveParams(keepaliveParams))

	c1 := c.StartClientWithRetry(opts)

	c.RegisterWithCertWithRetry(c1)

	transactionMgmt := TransactionMgmt{TransSet: set.NewSet[string](), TransBuffer: make(chan TransactionInfo, Conf.MaxTransactionNum)}

	c.startPeriodicHeartbeat()

	// go routine to process transactions with rate limit
	rl := ratelimit.New(Conf.TransactionRateLimit)
	go func() {
		log := logger.L()
		for {
			rl.Take()
			select {
			case info := <-transactionMgmt.TransBuffer:
				go func() {
					log.Infof("client main: received message: %v", info.task)

					transactionMgmt.TransSet.Remove(info.transactionId)

					err := c.SendMsgToServer(lmri.NewCtxWithReqId(context.Background()), info.task, info.transactionId, "", "", nil, 0, "")

					if err != nil {
						log.Errorf("client main: error in sending request response: %v", err)
						return
					}
				}()
			}
		}
	}()

	g := c.ReceiveMsgFromServer()
	return g, nil
}

func NewClient(Conf *config.Config, opClient interfaces.TGOperator, Version string) (*Client, error) {
	log := logger.L()
	clientID := Conf.CloudProviderId

	var k8sClient *k8sclient.K8sClient = nil
	var versionFetcher *cp.ComponentVersionFetcher = nil
	var err error
	var versionJson []byte

	if Conf.NoK8SConnection {
		componentVersion := cp.NewComponentVersion()
		if Conf.ClusterAgentImageTag != "" {
			componentVersion.SetVersion(cp.Component_ClusterAgent, Conf.ClusterAgentImageTag)
		}
		if Conf.TGOperatorImageTag != "" {
			componentVersion.SetVersion(cp.Component_TGOperator, Conf.TGOperatorImageTag)
		}
		if Conf.AutoStartHandlerImageTag != "" {
			componentVersion.SetVersion(cp.Component_AutoStartHandler, Conf.AutoStartHandlerImageTag)
		}

		versionJson, err = componentVersion.MarshalJSON()
		if err != nil {
			log.Errorf("Cluster Agent: failed to marshal component version: %v", err)
		}

		log.Infof("Cluster Agent: component version: %v", string(versionJson))
	} else {
		// Set up k8s client
		k8sClient, err = k8sclient.NewK8sClient(&Conf.K8SConfig)
		if err != nil {
			log.Errorf("Cluster Agent: failed to create k8s client: %v", err)
			return nil, err
		}
		// Set up ComponentVersionFetcher
		versionFetcher = cp.NewComponentVersionFetcher(k8sClient)
	}

	metrics := cio.NewClusterAgentMetrics(Conf.CloudProviderId)

	myClient := Client{
		Id:                  clientID,
		Version:             Version,
		Token:               Conf.Token,
		IsMaintenance:       false,
		Config:              *Conf,
		Stream:              nil,
		AwaitUpdateMap:      make(map[string]UpdateInfo),
		OpClient:            opClient,
		heartbeaterStarted:  false,
		componentVersion:    string(versionJson),
		k8scilent:           k8sClient,
		versionFetcher:      versionFetcher,
		ClusterAgentMetrics: metrics,
	}

	log.Infof("ClusterAgent: Id: %v, Version: %v", clientID, myClient.Version)
	return &myClient, nil
}

// ServeClient starts the client and serves the client
// It is a blocking call
func ServeClient(myClient *Client) {
	log := logger.L()

	Conf := &myClient.Config

	myClient.ObsServer = observability.NewObserver(":24240")
	go func() {
		myClient.ClusterAgentMetrics.RegisterClusterAgentMetrics()
		if err := myClient.ObsServer.Serve(); err != nil {
			logger.L().Fatalf("error serving observability:%v", err)
		}
	}()

	log.Info("ClusterAgent: agent running...")

RECONNECT:
	g, err := myClient.startClient(Conf)
	if err != nil {
		log.Errorf("Cluster Agent: client exiting because of error %v", err)
	}

	err = g.Wait()
	c, ok := status.FromError(err) // Check if it is a gRPC-related error
	if !ok {
		log.Panicf("Cluster Agent: client exiting because of error %v", err)
	}
	switch c.Code() {
	case codes.OK: // OK stands for a shutdown command: exit normally
		log.Warn("exit ok")
		return
	default:
		// radom delay reconnect by 500ms to 3000ms
		sleepDuration := rand.Intn(2500) + 500
		log.Warnf("reconn with random delay of %v ms...", sleepDuration)
		myClient.ClusterAgentMetrics.IncreaseReconnectCount()
		time.Sleep(time.Duration(time.Duration(sleepDuration) * time.Millisecond))
		goto RECONNECT // Otherwise, attempt to reconnect
	}
}
