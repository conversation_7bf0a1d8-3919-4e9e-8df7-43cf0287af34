package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) CreateServiceAccount(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing create service account")

	// func (client *TGRemoteOperator) CreateServiceAccount(namespace string, roleArn string) (saName string, err error) {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateServiceAccount, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	saName, ok := params["saName"]
	if !ok {
		log.Error("failed to read saName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateServiceAccount, req.Initiator, nil, 1, "error reading saName")
		return
	}

	roleArn, ok := params["roleArn"]
	if !ok {
		log.Error("failed to read roleArn")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateServiceAccount, req.Initiator, nil, 1, "error reading roleArn")
		return
	}

	err := c.OpClient.CreateServiceAccount(ctx, string(namespace), string(saName), string(roleArn))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateServiceAccount, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"saName": []byte(saName),
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateServiceAccount, req.Initiator, respParams, 0, "")
}
