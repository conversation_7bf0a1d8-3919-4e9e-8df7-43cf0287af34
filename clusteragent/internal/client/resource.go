package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) DeleteResource(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing delete resource")

	params := req.GetParams()

	reqPayload, ok := params["request"]

	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResource, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.DeleteResourceRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResource, req.Initiator, nil, 1, err.Error())
		return
	}

	err = c.OpClient.DeleteResource(ctx, opRequest)

	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResource, req.Initiator, nil, 1, err.Error())
		return
	}

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResource, req.Initiator, nil, 0, "")
}

func (c *Client) GetResource(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing get resource")

	params := req.GetParams()

	reqPayload, ok := params["request"]

	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.GetResourceRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.GetResource(ctx, opRequest)

	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, nil, 1, err.Error())
		return
	}

	if result == nil {
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, nil, 0, "")
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"response": resp,
	}

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetResource, req.Initiator, respParams, 0, "")
}

func (c *Client) ListResource(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing list resource")

	params := req.GetParams()

	reqPayload, ok := params["request"]

	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.ListResourceRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.ListResource(ctx, opRequest)

	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, nil, 1, err.Error())
		return
	}

	if result == nil {
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, nil, 0, "")
		return
	}

	respParams := map[string][]byte{
		"response": resp,
	}

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdListResource, req.Initiator, respParams, 0, "")
}

func (c *Client) DeleteResourceCollection(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing delete resource collection")

	params := req.GetParams()

	reqPayload, ok := params["request"]

	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResourceCollection, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.DeleteResourceCollectionRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResourceCollection, req.Initiator, nil, 1, err.Error())
		return
	}

	err = c.OpClient.DeleteResourceCollection(ctx, opRequest)

	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResourceCollection, req.Initiator, nil, 1, err.Error())
		return
	}

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteResourceCollection, req.Initiator, nil, 0, "")
}
