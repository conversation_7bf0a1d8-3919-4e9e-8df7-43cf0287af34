package client

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/RichardKnop/machinery/v1"
	"github.com/alicebob/miniredis/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	agw "github.com/tigergraph/cloud-universe/agentgateway/server"
	"github.com/tigergraph/cloud-universe/clusteragent/config"
	"github.com/tigergraph/cloud-universe/common/cloudprovider"
	"github.com/tigergraph/cloud-universe/tgIAM"

	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"

	"github.com/stretchr/testify/mock"
	conf "github.com/tigergraph/cloud-universe/resource-manager/config"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	manager "github.com/tigergraph/cloud-universe/resource-manager/service"
)

type bYOCMockOperator struct {
	mock.Mock
	manager.MockOperator
}

const (
	addr = ":0"
	id   = "cloudprovider1"
)

var ExportGetMetadata = (*agw.Server).GetMetadata
var redisServer *miniredis.Miniredis
var machineryServer *machinery.Server

func loadAgwConfig() (agw.Configuration, error) {
	config := agw.Configuration{
		HostingAddr:             "localhost:50055",
		ServerExporterAddr:      "localhost:9954",
		ServerCertName:          "server.pem",
		ServerKeyName:           "server-key.pem",
		CACertName:              "ca.pem",
		EnableGRPCLogging:       true,
		UpdateTimeout:           20,
		InsecureSkipAuthServer:  true,
		Region:                  "us-east-1",
		PingInterval:            100,
		ProcessInterval:         1,
		MaxHeartbeatLostSeconds: 1,
		QueueConfig: taskqueue.QueueConfig{
			RedisURL:       fmt.Sprintf("redis://%v", redisServer.Addr()),
			RedisRdTimeout: 5,
			RedisWtTimeout: 5,
			WorkerCount:    1000,
		},
		SkipInitialLoading: true,
		TGIAMConfig: conf.TGIAMConfig{
			IAMService:    "https://api.tgcloud-dev.com/iam",
			OrgService:    "https://apx7zkpgw3.execute-api.us-east-1.amazonaws.com/dev",
			ClientID:      "TbaIi568sw50vN26QntoMNTgP2McaGxK",
			ClientSecret:  "{{ AUTH0_SECRET }}",
			Auth0HostName: "tgcloud-dev.auth0.com",
		},
	}

	return config, nil
}

func loadCAConfig() (*config.Config, error) {
	config := config.Config{
		CloudProviderId:      "cloudprovider1",
		Token:                "randomtoken1",
		AuthServerAddr:       "localhost:50051",
		GRPCServerAddr:       "localhost:50055",
		ExporterAddr:         "localhost:9955",
		NonSecure:            true,
		MaxMsgSize:           4096,
		AuthServerTimeOut:    10,
		RetryCount:           3,
		RetryInterval:        1,
		HeartbeatInterval:    2,
		TransactionRateLimit: 50,

		NoK8SConnection:          true,
		K8SConfig:                conf.K8SConfig{},
		ClusterAgentImageTag:     "06151515",
		AutoStartHandlerImageTag: "04141414",
		TGOperatorImageTag:       "1.22",
	}
	return &config, nil
}

func createTestTaskQueueServer() (*machinery.Server, error) {
	log := logger.L()
	config, err := loadAgwConfig()
	if err != nil {
		return nil, err
	}

	machineryServer, err = taskqueue.NewMachineryServer(&config.QueueConfig)
	if err != nil {
		log.Errorf("Failed to create new machinery server: %v", err)
		return nil, err
	}
	return machineryServer, nil
}

func sendTaskAndWaitResult(t *testing.T, server *machinery.Server, task *taskqueue.TGTask) (*taskqueue.TGTask, error) {
	producer, err := taskqueue.NewMachineryTaskProducer(server)
	require.NoError(t, err)

	respTask, err := producer.SendAndWaitResult(context.Background(), taskqueue.TGTaskQueueName, task, 5)
	if err != nil {
		return nil, fmt.Errorf("unable to send task to cloud provider: %v", err)
	}
	if respTask.Status != taskqueue.TaskStatusOk {
		return nil, fmt.Errorf("cloud provider failed to create workspace: %v", respTask.Error)
	}
	return respTask, nil
}
func TestManyClients(t *testing.T) {

	t.Skip("This test is always skipped by default, need to run this manually.")

	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	config, err := loadAgwConfig()
	require.NoError(t, err)

	l, err := net.Listen("tcp", config.HostingAddr)
	require.NoError(t, err)

	grpcServer, myServer, err := agw.New(config)
	require.NoError(t, err)
	require.NotNil(t, grpcServer)
	require.NotNil(t, myServer)

	// create pseudo IAM service
	iam := tgIAM.NewPseudoTGIAM()
	myServer.TGIAM = iam

	go func() {
		_ = grpcServer.Serve(l)
	}()

	caConfig, err := loadCAConfig()
	require.NoError(t, err)
	op := &bYOCMockOperator{}

	count := 100
	clients := make([]Client, count)
	version := "1.1"
	for i := 0; i < count; i++ {
		clientID := uuid.New().String()

		clients[i] = Client{
			Id:             clientID,
			Version:        version,
			Token:          caConfig.Token,
			IsMaintenance:  false,
			Config:         *caConfig,
			Stream:         nil,
			AwaitUpdateMap: make(map[string]UpdateInfo),
			OpClient:       op,
		}

		go func(index int, clientID string) {
			logger.L().Infof("Starting client: %v - %v", index, clientID)
			ServeClient(&clients[index])
		}(i, clientID)
	}

	// now wait server to serve
	time.Sleep(10 * time.Second)
	// now disconnect two clients
	for i := 0; i < count; i++ {
		clients[i].Close()
	}
	time.Sleep(3 * time.Second)

}

func TestRegister(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test register", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testRegister(t, server, client, op)
	})
}

func TestK8sVersionFetch(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test k8s version fetch", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testK8SVersionFetch(t, server, client, op)
	})
}

func TestWorkspace(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test workspace", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testWorkspace(t, server, client, op)
	})
}

func TestDatabasePVC(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test database pvc", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testDatabasePVC(t, server, client, op)
	})
}

func TestWorkgroup(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test workgroup", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testWorkgroup(t, server, client, op)
	})
}

func TestServiceAccount(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test workgroup", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testServiceAccount(t, server, client, op)
	})
}

func TestBackup(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test backup", func(t *testing.T) {
		server, client, op, teardown := setupTest(t)
		defer teardown()
		testBackup(t, server, client, op)
	})
}

func TestMetrics(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test metrics", func(t *testing.T) {
		server, client, _, teardown := setupTest(t)
		defer teardown()
		testMetrics(t, server, client)
	})
}

func TestCreateKMS(t *testing.T) {
	redisServer = miniredis.RunT(t)
	defer redisServer.Close()
	s, err := createTestTaskQueueServer()
	require.NoError(t, err)
	machineryServer = s

	t.Run("test metrics", func(t *testing.T) {
		server, client, _, teardown := setupTest(t)
		defer teardown()
		testCreateKMS(t, server, client)
	})
}

func setupTest(t *testing.T) (
	server *agw.Server,
	client *Client,
	operator *bYOCMockOperator,
	teardown func(),
) {
	t.Helper()

	config, err := loadAgwConfig()
	require.NoError(t, err)

	l, err := net.Listen("tcp", config.HostingAddr)
	require.NoError(t, err)

	s, myServer, err := agw.New(config)
	require.NoError(t, err)

	// create pseudo IAM service
	iam := tgIAM.NewPseudoTGIAM()
	myServer.TGIAM = iam

	go func() {
		_ = s.Serve(l)
	}()

	caConfig, err := loadCAConfig()
	require.NoError(t, err)
	op := &bYOCMockOperator{}

	c, err := NewClient(caConfig, op, "1.0")
	require.NoError(t, err)

	return myServer, c, op, func() {
		s.Stop()
		_ = l.Close()
		c.Close()
	}
}

func testRegister(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {
	// register with a new agent

	// now start the client
	go func() {
		ServeClient(client)
	}()

	// should have registered
	time.Sleep(200 * time.Millisecond)

	clientKey := agw.NewClientMapKey(id, false)

	fmt.Printf("clientKey: %v\n", clientKey)

	keyCount := 0
	server.MyClients.Range(func(k, v interface{}) bool {
		fmt.Printf("key: %v, value: %v\n", k, v)
		keyCount++
		return true
	})
	value, ok := server.MyClients.Load(*clientKey)
	require.Equal(t, 1, keyCount)
	require.True(t, ok)

	fmt.Printf("value: %v\n", value)

	info := value.(*agw.ClientInfo)
	require.Equal(t, info.GetVersion(), "1.0")

	client.Close()

	// now start another client
	config2 := client.Config
	id2 := "cloudprovider2"
	version2 := "1.2"
	config2.CloudProviderId = id2
	op2 := &bYOCMockOperator{}

	c2, err := NewClient(&config2, op2, version2)
	require.NoError(t, err)
	// now start the client
	go func() {
		ServeClient(c2)
	}()

	time.Sleep(200 * time.Millisecond)

	clientKey2 := agw.NewClientMapKey(id2, false)
	// now assert the new client is registered
	keyCount = 0
	server.MyClients.Range(func(k, v interface{}) bool {
		fmt.Printf("key: %v, value: %v\n", k, v)
		keyCount++
		return true
	})

	value, ok = server.MyClients.Load(*clientKey2)
	require.True(t, ok)
	require.Equal(t, 2, keyCount)

	info = value.(*agw.ClientInfo)
	require.Equal(t, info.GetVersion(), version2)
	c2.Close()
}

func testK8SVersionFetch(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {
	// register with a new agent

	// now start the client
	client.Config.NoK8SConnection = false
	mockFetcher := &cloudprovider.MockComponentVersionFetcher{}
	client.versionFetcher = mockFetcher

	versionMap := &map[string]string{
		"tgoperator":       "1.2.3",
		"clusteragent":     "4.5.6",
		"autostarthandler": "7.8.9",
	}

	originalCV := cloudprovider.NewComponentVersionWithMap(*versionMap)

	mockFetcher.On("FetchComponentVersions").Return(*versionMap, nil)

	go func() {
		ServeClient(client)
	}()

	// should have registered
	time.Sleep(310 * time.Millisecond)

	clientKey := agw.NewClientMapKey(id, false)

	fmt.Printf("clientKey: %v\n", clientKey)

	keyCount := 0
	server.MyClients.Range(func(k, v interface{}) bool {
		fmt.Printf("key: %v, value: %v\n", k, v)
		keyCount++
		return true
	})
	value, ok := server.MyClients.Load(*clientKey)
	require.Equal(t, 1, keyCount)
	require.True(t, ok)

	fmt.Printf("value: %v\n", value)

	info := value.(*agw.ClientInfo)
	require.Equal(t, info.GetVersion(), "1.0")

	cv := cloudprovider.NewComponentVersion()
	cv.UnmarshalJSON([]byte(info.GetComponentVersion()))

	fmt.Printf("originalCV: %v\n", originalCV)
	fmt.Printf("cv: %v\n", cv)

	require.True(t, originalCV.Equal(cv))
	mockFetcher.AssertExpectations(t)
}

func testWorkspace(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {
	log := logger.L()
	// firstly start the client, waiting for server tasks
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. test create workspace
	// prepare task parameters
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")

	wsId, _ := uuid.NewRandom()
	log.Infof("New ws id: %v", wsId.String())
	request := &data.CreateWorkspaceRequest{
		OrgID:           "org_Aw5TWfMHTvoa3PDo",
		WorkgroupID:     "77a76a17-68f3-4975-8960-2a78c3b37010", //"69d46b95-7c29-49e6-a8b6-b01f97c72888",
		WorkspaceID:     wsId.String(),
		TGDatabaseID:    "cd631ad1-11ca-489d-ab91-4ec0e662f20e",
		TGVersion:       "3.10.0",
		VersionNumber:   0,
		IsRW:            true,
		CloudProviderID: id,

		BucketName:            "",
		S3AccessRoleARN:       "",
		ServiceAccountRoleARN: "",

		Image: "docker.io/tginternal/tigergraph-k8s:3.10.0-wip-test-42450-centos",
		Name:  fmt.Sprintf("tg-%v", wsId.String()),
		PodResource: data.PodResource{
			CpuRequest:       "2",
			CpuLimit:         "2",
			MemoryRequest:    "8Gi",
			MemoryLimit:      "8Gi",
			Storage:          "10Gi",
			EphemeralStorage: "10Gi",
			KafkaStorage:     "40Gi",
			LogStorage:       "10Gi",

			NodeGroup: "NG-16",
		},
		HA:       1,
		Replicas: 1,
		License:  "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fUU-N53sXLurJLB3Hq2hCMePTgo1KapHzhklnJ6JXaS9PjkuFecGk8W3f3QalVrYsrFPrlPgyOuUx08_nJObvnyEnbB4iKLArsqV50t4qaqZdxoGzK2I_QbFKV2Yg71MMM6JTTYIYGcpOqacawkHrr8jyZS96_IgN7wo_G05_dsy-6BYmwr4oz3xURb4BQskJqAWPMvayKntHGaLoOK8vFCODnJoRw5GfkHoKR-hidUBG1ibbZlK7xodvuHU4WskjZROGpyykJjE2yt-WUgZmzejFdmtwZLS7b3vZIyqOfrtvuYj2twk2NKwVYKsuxhuXP7sE3VcjBP59-youPUn6g",
	}

	j, err := json.Marshal(request)
	require.NoError(t, err)

	// generate the task to send over the task queue
	task := &taskqueue.TGTask{
		Id:        "123",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateWorkspace,
		Params: map[string][]byte{
			"namespace": namespace,
			"request":   []byte(j),
		},
	}

	// send the task and wait for the result
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)
	fmt.Printf("resp: %v\n", resp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert workspace in Params
	require.NotNil(t, resp.Params["workspace"])
	// assert reply to id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert reply target
	require.Equal(t, task.Initiator, resp.Target)
	// assert reply initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	// assert workspace
	created := &data.Workspace{}
	err = json.Unmarshal(resp.Params["workspace"], created)
	fmt.Printf("created: %v\n", *created)
	require.NoError(t, err)
	require.Equal(t, request.WorkspaceID, created.WorkspaceID)
	require.Equal(t, request.Name, created.Name)
	require.Equal(t, request.OrgID, created.OrgID)
	require.Equal(t, request.WorkgroupID, created.WorkgroupID)
	require.Equal(t, request.TGDatabaseID, created.TGDatabaseID)
	require.Equal(t, request.TGVersion, created.TGVersion)
	require.Equal(t, request.VersionNumber, created.VersionNumber)
	require.Equal(t, request.CloudProviderID, created.CloudProviderID)

	// 2. test get workspace task
	// prepare task parameters
	wsNameToGet := []byte(created.Name)
	getTask := &taskqueue.TGTask{
		Id:        "124",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdGetWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsNameToGet,
		},
	}

	// send the task and wait for the result
	getResp, err := sendTaskAndWaitResult(t, server.MachineryServer, getTask)
	require.NoError(t, err)
	fmt.Printf("getResp: %v\n", getResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, getResp.Status)
	// assert workspace in Params
	require.NotNil(t, getResp.Params["workspace"])
	// assert reply to id
	require.Equal(t, getTask.Id, getResp.ReplyToId)
	// assert reply target
	require.Equal(t, getTask.Initiator, getResp.Target)
	// assert reply initiator
	require.Equal(t, getTask.Target, getResp.Initiator)
	// assert type/command
	require.Equal(t, getTask.Type, getResp.Type)
	require.Equal(t, getTask.Command, getResp.Command)

	// get workspace
	got := &data.Workspace{}
	err = json.Unmarshal(getResp.Params["workspace"], got)
	fmt.Printf("got workspace: %v\n", *got)
	require.NoError(t, err)
	require.Equal(t, created.WorkspaceID, got.WorkspaceID)
	require.Equal(t, created.Name, got.Name)
	require.Equal(t, created.OrgID, got.OrgID)
	require.Equal(t, created.WorkgroupID, got.WorkgroupID)
	require.Equal(t, created.TGDatabaseID, got.TGDatabaseID)
	require.Equal(t, created.TGVersion, got.TGVersion)
	require.Equal(t, created.VersionNumber, got.VersionNumber)
	require.Equal(t, created.CloudProviderID, got.CloudProviderID)

	// 3. test list workspaces task
	// prepare task parameters
	listTask := &taskqueue.TGTask{
		Id:        "125",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdListWorkspaces,
		Params: map[string][]byte{
			"namespace": namespace,
		},
	}

	// send the task and wait for the result
	listResp, err := sendTaskAndWaitResult(t, server.MachineryServer, listTask)
	require.NoError(t, err)
	fmt.Printf("listResp: %v\n", listResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, listResp.Status)
	// assert workspaces in Params
	require.NotNil(t, listResp.Params["results"])
	// assert reply to id
	require.Equal(t, listTask.Id, listResp.ReplyToId)
	// assert reply target
	require.Equal(t, listTask.Initiator, listResp.Target)
	// assert reply initiator
	require.Equal(t, listTask.Target, listResp.Initiator)
	// assert type/command
	require.Equal(t, listTask.Type, listResp.Type)
	require.Equal(t, listTask.Command, listResp.Command)

	// get workspaces
	workspaces := []*data.Workspace{}
	err = json.Unmarshal(listResp.Params["results"], &workspaces)
	fmt.Printf("workspaces: %v\n", workspaces)
	require.NoError(t, err)
	require.Equal(t, 1, len(workspaces))
	require.Equal(t, created.WorkspaceID, workspaces[0].WorkspaceID)
	require.Equal(t, created.Name, workspaces[0].Name)
	require.Equal(t, created.OrgID, workspaces[0].OrgID)
	require.Equal(t, created.WorkgroupID, workspaces[0].WorkgroupID)
	require.Equal(t, created.TGDatabaseID, workspaces[0].TGDatabaseID)
	require.Equal(t, created.TGVersion, workspaces[0].TGVersion)
	require.Equal(t, created.VersionNumber, workspaces[0].VersionNumber)
	require.Equal(t, created.CloudProviderID, workspaces[0].CloudProviderID)

	// 4. test update workspace
	req := &data.UpdateWorkspaceRequest{
		UpdateContext: data.UpdateContext{
			VersionNumber: 1,
			Name:          created.Name,
			OrgID:         "org",
			WorkgroupID:   "workgroup",
			TGDatabaseID:  "database",
			WorkspaceID:   "workspace",
		},

		WorkspaceType: data.WorkspaceType{
			TypeName:  "TG-0",
			CPU:       "1",
			Memory:    "1GiB",
			HA:        1,
			Partition: 1,
			PodResource: data.PodResource{
				CpuRequest:       "1",
				CpuLimit:         "1",
				MemoryRequest:    "1GiB",
				MemoryLimit:      "1GiB",
				Storage:          "1GiB",
				EphemeralStorage: "1GiB",
				KafkaStorage:     "40Gi",
				LogStorage:       "10Gi",
				NodeGroup:        "group",
			},
		},

		EnableHA: false,
	}

	jsonBytes, err := json.Marshal(req)
	require.NoError(t, err)

	// now prepare task
	// now test list workspaces task
	// prepare task parameters
	updateTask := &taskqueue.TGTask{
		Id:        "126",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdUpdateWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
			"request":       jsonBytes,
		},
	}

	// send the task and wait for the result
	updateResp, err := sendTaskAndWaitResult(t, server.MachineryServer, updateTask)
	require.NoError(t, err)
	fmt.Printf("updateResp: %v\n", updateResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, updateResp.Status)
	// assert workspace in Params
	require.NotNil(t, updateResp.Params["workspace"])
	// assert reply to id
	require.Equal(t, updateTask.Id, updateResp.ReplyToId)
	// assert reply target
	require.Equal(t, updateTask.Initiator, updateResp.Target)
	// assert reply initiator
	require.Equal(t, updateTask.Target, updateResp.Initiator)
	// assert type/command
	require.Equal(t, updateTask.Type, updateResp.Type)
	require.Equal(t, updateTask.Command, updateResp.Command)

	// get workspace
	updated := &data.Workspace{}
	err = json.Unmarshal(updateResp.Params["workspace"], updated)
	fmt.Printf("updated workspace: %v\n", *updated)
	require.NoError(t, err)
	require.Equal(t, created.WorkspaceID, updated.WorkspaceID)
	require.Equal(t, created.Name, updated.Name)
	require.Equal(t, created.OrgID, updated.OrgID)
	require.Equal(t, created.WorkgroupID, updated.WorkgroupID)
	require.Equal(t, created.TGDatabaseID, updated.TGDatabaseID)
	require.Equal(t, created.TGVersion, updated.TGVersion)
	require.Equal(t, req.VersionNumber, updated.VersionNumber)
	require.Equal(t, created.CloudProviderID, updated.CloudProviderID)

	// 5. test export workspace
	// prepare task parameters
	exportTask := &taskqueue.TGTask{
		Id:        "127",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdExportWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
		},
	}

	// send the task and wait for the result
	exportResp, err := sendTaskAndWaitResult(t, server.MachineryServer, exportTask)
	require.NoError(t, err)
	fmt.Printf("exportResp: %v\n", exportResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, exportResp.Status)
	// assert exportedPath in Params
	require.NotNil(t, exportResp.Params["exportedPath"])
	// assert reply to id
	require.Equal(t, exportTask.Id, exportResp.ReplyToId)
	// assert reply target
	require.Equal(t, exportTask.Initiator, exportResp.Target)
	// assert reply initiator
	require.Equal(t, exportTask.Target, exportResp.Initiator)
	// assert type/command
	require.Equal(t, exportTask.Type, exportResp.Type)
	require.Equal(t, exportTask.Command, exportResp.Command)

	// get exportedPath
	exportedPath := string(exportResp.Params["exportedPath"])
	fmt.Printf("exportedPath: %v\n", exportedPath)
	require.NotEmpty(t, exportedPath)

	// 6. test import workspace
	// prepare task parameters
	importTask := &taskqueue.TGTask{
		Id:        "128",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdImportWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
			"exportedPath":  []byte(exportedPath),
		},
	}

	// send the task and wait for the result
	importResp, err := sendTaskAndWaitResult(t, server.MachineryServer, importTask)
	require.NoError(t, err)
	fmt.Printf("importResp: %v\n", importResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, importResp.Status)
	// assert reply to id
	require.Equal(t, importTask.Id, importResp.ReplyToId)
	// assert reply target
	require.Equal(t, importTask.Initiator, importResp.Target)
	// assert reply initiator
	require.Equal(t, importTask.Target, importResp.Initiator)
	// assert type/command
	require.Equal(t, importTask.Type, importResp.Type)
	require.Equal(t, importTask.Command, importResp.Command)

	// 7. test pause workspace
	// prepare task parameters
	pauseTask := &taskqueue.TGTask{
		Id:        "129",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdPauseWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
		},
	}

	// send the task and wait for the result
	pauseResp, err := sendTaskAndWaitResult(t, server.MachineryServer, pauseTask)
	require.NoError(t, err)
	fmt.Printf("pauseResp: %v\n", pauseResp)
	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, pauseResp.Status)

	// 8. test resume workspace
	// prepare task parameters
	resumeTask := &taskqueue.TGTask{
		Id:        "130",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdResumeWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
		},
	}

	// send the task and wait for the result
	resumeResp, err := sendTaskAndWaitResult(t, server.MachineryServer, resumeTask)
	require.NoError(t, err)
	fmt.Printf("resumeResp: %v\n", resumeResp)
	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resumeResp.Status)

	// 9. test delete workspace
	// prepare task parameters
	deleteTask := &taskqueue.TGTask{
		Id:        "150",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdDeleteWorkspace,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": []byte(created.Name),
		},
	}

	// send the task and wait for the result
	deleteResp, err := sendTaskAndWaitResult(t, server.MachineryServer, deleteTask)
	require.NoError(t, err)
	fmt.Printf("deleteResp: %v\n", deleteResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, deleteResp.Status)
}

func testDatabasePVC(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {

	// firstly start the client, waiting for server tasks
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. test create database pvc
	// prepare task parameters
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")
	databaseId := []byte("cd631ad1-11ca-489d-ab91-4ec0e662f20e")
	storageClass := []byte("gp2")
	storageSize := []byte("1Gi")

	task := &taskqueue.TGTask{
		Id:        "131",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateTGDatabsePVC,
		Params: map[string][]byte{
			"namespace": namespace,

			"tgDatabaseID": databaseId,
			"storageClass": storageClass,
			"size":         storageSize,
		},
	}

	// send the task and wait for the result
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)
	fmt.Printf("resp: %v\n", resp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert database pvc in Params
	require.NotNil(t, resp.Params["pvcName"])
	// assert reply to id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert reply target
	require.Equal(t, task.Initiator, resp.Target)
	// assert reply initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	// assert database pvc
	pvcName := string(resp.Params["pvcName"])
	fmt.Printf("pvcName: %v\n", pvcName)
	require.NotEmpty(t, pvcName)
	require.Equal(t, "pvcNameTest", pvcName)

	// 2. test delete database pvc
	// prepare task parameters
	deleteTask := &taskqueue.TGTask{
		Id:        "132",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdDeleteTGDatabsePVC,
		Params: map[string][]byte{
			"namespace":    namespace,
			"tgDatabaseID": databaseId,
		},
	}

	// send the task and wait for the result
	deleteResp, err := sendTaskAndWaitResult(t, server.MachineryServer, deleteTask)
	require.NoError(t, err)
	fmt.Printf("deleteResp: %v\n", deleteResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, deleteResp.Status)
	// assert result id
	require.Equal(t, deleteTask.Id, deleteResp.ReplyToId)
	// assert result target
	require.Equal(t, deleteTask.Initiator, deleteResp.Target)
	// assert result initiator
	require.Equal(t, deleteTask.Target, deleteResp.Initiator)
	// assert type/command
	require.Equal(t, deleteTask.Type, deleteResp.Type)
	require.Equal(t, deleteTask.Command, deleteResp.Command)

}

func testWorkgroup(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {

	// firstly start the client, waiting for server tasks
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. test create workgroup
	// prepare task parameters
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")

	task := &taskqueue.TGTask{
		Id:        "141",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateWorkgroup,
		Params: map[string][]byte{
			"namespace": namespace,
		},
	}

	// send the task and wait for the result
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)
	fmt.Printf("resp: %v\n", resp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert result id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert result target
	require.Equal(t, task.Initiator, resp.Target)
	// assert result initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	// 2. test get workgroup labels
	// prepare task parameters
	getTask := &taskqueue.TGTask{
		Id:        "142",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdGetWorkgroupLabels,

		Params: map[string][]byte{
			"namespace": namespace,
		},
	}

	// send the task and wait for the result
	getResp, err := sendTaskAndWaitResult(t, server.MachineryServer, getTask)
	require.NoError(t, err)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, getResp.Status)
	// assert result id
	require.Equal(t, getTask.Id, getResp.ReplyToId)
	// assert result target
	require.Equal(t, getTask.Initiator, getResp.Target)
	// assert result initiator
	require.Equal(t, getTask.Target, getResp.Initiator)
	// assert type/command
	require.Equal(t, getTask.Type, getResp.Type)
	require.Equal(t, getTask.Command, getResp.Command)

	// verify the labels
	labels := make(map[string]string)
	require.NotNil(t, getResp.Params["labels"])
	err = json.Unmarshal(getResp.Params["labels"], &labels)
	require.NoError(t, err)
	fmt.Printf("labels: %v\n", labels)
	require.NotEmpty(t, labels)
	require.Equal(t, "value1", labels["key1"])

	// 3. test delete namespace
	// prepare task parameters
	deleteTask := &taskqueue.TGTask{
		Id:        "142",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdDeleteWorkgroup,
		Params: map[string][]byte{
			"namespace": namespace,
		},
	}

	// send the task and wait for the result
	deleteResp, err := sendTaskAndWaitResult(t, server.MachineryServer, deleteTask)
	require.NoError(t, err)
	fmt.Printf("deleteResp: %v\n", deleteResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, deleteResp.Status)
	// assert result id
	require.Equal(t, deleteTask.Id, deleteResp.ReplyToId)
	// assert result target
	require.Equal(t, deleteTask.Initiator, deleteResp.Target)
	// assert result initiator
	require.Equal(t, deleteTask.Target, deleteResp.Initiator)
	// assert type/command
	require.Equal(t, deleteTask.Type, deleteResp.Type)
	require.Equal(t, deleteTask.Command, deleteResp.Command)

}

func testServiceAccount(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {

	// firstly start the client, waiting for server tasks
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. test create service account
	// prepare task parameters
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")
	roleArn := []byte("test-service-account")

	task := &taskqueue.TGTask{
		Id:        "151",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateServiceAccount,
		Params: map[string][]byte{
			"namespace": namespace,
			"saName":    []byte("test-service-account"),
			"roleArn":   roleArn,
		},
	}

	// send the task and wait for the result
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)
	fmt.Printf("resp: %v\n", resp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert result id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert result target
	require.Equal(t, task.Initiator, resp.Target)
	// assert result initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	// 2. test delete service account
	// prepare task parameters
	deleteTask := &taskqueue.TGTask{
		Id:        "152",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,

		Command: taskqueue.ControllerCmdDeleteServiceAccount,
		Params: map[string][]byte{
			"namespace": namespace,
			"saName":    []byte("test-service-account"),
		},
	}

	// send the task and wait for the result
	deleteResp, err := sendTaskAndWaitResult(t, server.MachineryServer, deleteTask)
	require.NoError(t, err)
	fmt.Printf("deleteResp: %v\n", deleteResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, deleteResp.Status)
	// assert result id
	require.Equal(t, deleteTask.Id, deleteResp.ReplyToId)
	// assert result target
	require.Equal(t, deleteTask.Initiator, deleteResp.Target)
	// assert result initiator
	require.Equal(t, deleteTask.Target, deleteResp.Initiator)
	// assert type/command
	require.Equal(t, deleteTask.Type, deleteResp.Type)
	require.Equal(t, deleteTask.Command, deleteResp.Command)

}

func testBackup(t *testing.T, server *agw.Server, client *Client, operator *bYOCMockOperator) {

	// firstly start the client, waiting for server tasks
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. test create backup
	// prepare task parameters
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")
	wsName := []byte("test-workspace")
	backupName := "test-backup"

	request := &data.CreateBackupRequest{
		Name:       backupName,
		BucketName: "test-bucket",
	}

	j, err := json.Marshal(request)
	require.NoError(t, err)

	task := &taskqueue.TGTask{
		Id:        "161",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateBackup,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
			"request":       []byte(j),
		},
	}

	// send the task and wait for the result
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)
	fmt.Printf("resp: %v\n", resp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert result id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert result target
	require.Equal(t, task.Initiator, resp.Target)
	// assert result initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	require.NotNil(t, resp.Params["backupName"])
	// assert backup name
	tag := string(resp.Params["backupName"])
	fmt.Printf("backupName: %v\n", tag)

	// 2. test list backup
	// prepare task parameters
	listTask := &taskqueue.TGTask{
		Id:        "162",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdGetBackupList,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
		},
	}

	// send the task and wait for the result
	listResp, err := sendTaskAndWaitResult(t, server.MachineryServer, listTask)
	require.NoError(t, err)
	fmt.Printf("listResp: %v\n", listResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, listResp.Status)
	// assert result id
	require.Equal(t, listTask.Id, listResp.ReplyToId)
	// assert result target
	require.Equal(t, listTask.Initiator, listResp.Target)
	// assert result initiator
	require.Equal(t, listTask.Target, listResp.Initiator)
	// assert type/command
	require.Equal(t, listTask.Type, listResp.Type)
	require.Equal(t, listTask.Command, listResp.Command)

	// get backups
	backups := []*data.Backup{}
	err = json.Unmarshal(listResp.Params["backups"], &backups)
	fmt.Printf("backups: %v\n", backups)
	require.NoError(t, err)
	require.Equal(t, 1, len(backups))
	fmt.Printf("backupName: %v\n", backups[0].Tag)
	require.Equal(t, tag, backups[0].Tag)

	// 3. test restore backup

	restoreReq := data.CreateRestoreRequest{
		BackupID:   backupName,
		BucketName: "test-bucket",
	}

	j, err = json.Marshal(restoreReq)
	require.NoError(t, err)

	// prepare task parameters
	restoreTask := &taskqueue.TGTask{
		Id:        "163",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdRestoreBackup,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
			"request":       []byte(j),
		},
	}

	// send the task and wait for the result
	restoreResp, err := sendTaskAndWaitResult(t, server.MachineryServer, restoreTask)
	require.NoError(t, err)
	fmt.Printf("restoreResp: %v\n", restoreResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, restoreResp.Status)
	// assert result id
	require.Equal(t, restoreTask.Id, restoreResp.ReplyToId)
	// assert result target
	require.Equal(t, restoreTask.Initiator, restoreResp.Target)
	// assert result initiator
	require.Equal(t, restoreTask.Target, restoreResp.Initiator)
	// assert type/command
	require.Equal(t, restoreTask.Type, restoreResp.Type)
	require.Equal(t, restoreTask.Command, restoreResp.Command)

	// extract restore name
	require.NotNil(t, restoreResp.Params["restoreName"])
	restoreName := string(restoreResp.Params["restoreName"])
	fmt.Printf("restoreName: %v\n", restoreName)
	require.NotEmpty(t, restoreName)

	// 4. GetBackupRestoreStatus
	// prepare task parameters
	getRestoreTask := &taskqueue.TGTask{
		Id:        "164",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdGetBackupRestoreStatus,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
			"job":           []byte(restoreName),
		},
	}

	// send the task and wait for the result
	getRestoreResp, err := sendTaskAndWaitResult(t, server.MachineryServer, getRestoreTask)
	require.NoError(t, err)
	fmt.Printf("getRestoreResp: %v\n", getRestoreResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, getRestoreResp.Status)
	// assert result id
	require.Equal(t, getRestoreTask.Id, getRestoreResp.ReplyToId)
	// assert result target
	require.Equal(t, getRestoreTask.Initiator, getRestoreResp.Target)
	// assert result initiator
	require.Equal(t, getRestoreTask.Target, getRestoreResp.Initiator)
	// assert type/command
	require.Equal(t, getRestoreTask.Type, getRestoreResp.Type)
	require.Equal(t, getRestoreTask.Command, getRestoreResp.Command)

	// verify restore status
	require.NotNil(t, getRestoreResp.Params["status"])
	restoreStatus := string(getRestoreResp.Params["status"])
	fmt.Printf("restoreStatus: %v\n", restoreStatus)
	require.NotEmpty(t, restoreStatus)

	// 5. test set backup schedule
	// prepare task parameters
	scheduleReq := data.BackupSchedule{
		BucketName: "test-bucket",
		Schedule:   "0 0 * * *",
	}

	j, err = json.Marshal(scheduleReq)
	require.NoError(t, err)

	// prepare task parameters
	scheduleTask := &taskqueue.TGTask{
		Id:        "165",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdSetBackupSchedule,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
			"request":       []byte(j),
		},
	}

	// send the task and wait for the result
	scheduleResp, err := sendTaskAndWaitResult(t, server.MachineryServer, scheduleTask)
	require.NoError(t, err)
	fmt.Printf("scheduleResp: %v\n", scheduleResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, scheduleResp.Status)
	// assert result id
	require.Equal(t, scheduleTask.Id, scheduleResp.ReplyToId)
	// assert result target
	require.Equal(t, scheduleTask.Initiator, scheduleResp.Target)
	// assert result initiator
	require.Equal(t, scheduleTask.Target, scheduleResp.Initiator)
	// assert type/command
	require.Equal(t, scheduleTask.Type, scheduleResp.Type)
	require.Equal(t, scheduleTask.Command, scheduleResp.Command)

	// 6. test get backup schedule
	// prepare task parameters
	getScheduleTask := &taskqueue.TGTask{
		Id:        "166",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdGetBackupSchedule,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
		},
	}

	// send the task and wait for the result
	getScheduleResp, err := sendTaskAndWaitResult(t, server.MachineryServer, getScheduleTask)
	require.NoError(t, err)
	fmt.Printf("getScheduleResp: %v\n", getScheduleResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, getScheduleResp.Status)
	// assert result id
	require.Equal(t, getScheduleTask.Id, getScheduleResp.ReplyToId)
	// assert result target
	require.Equal(t, getScheduleTask.Initiator, getScheduleResp.Target)
	// assert result initiator
	require.Equal(t, getScheduleTask.Target, getScheduleResp.Initiator)
	// assert type/command
	require.Equal(t, getScheduleTask.Type, getScheduleResp.Type)
	require.Equal(t, getScheduleTask.Command, getScheduleResp.Command)

	// get schedule
	schedule := &data.BackupSchedule{}
	err = json.Unmarshal(getScheduleResp.Params["schedule"], schedule)
	fmt.Printf("schedule: %v\n", schedule)
	require.NoError(t, err)

	// 7. test delete backup
	// prepare task parameters
	deleteTask := &taskqueue.TGTask{
		Id:        "163",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdDeleteBackup,
		Params: map[string][]byte{
			"namespace":     namespace,
			"workspaceName": wsName,
			"tag":           []byte(tag),
		},
	}

	// send the task and wait for the result
	deleteResp, err := sendTaskAndWaitResult(t, server.MachineryServer, deleteTask)
	require.NoError(t, err)
	fmt.Printf("deleteResp: %v\n", deleteResp)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, deleteResp.Status)
	// assert result id
	require.Equal(t, deleteTask.Id, deleteResp.ReplyToId)
	// assert result target
	require.Equal(t, deleteTask.Initiator, deleteResp.Target)
	// assert result initiator
	require.Equal(t, deleteTask.Target, deleteResp.Initiator)
	// assert type/command
	require.Equal(t, deleteTask.Type, deleteResp.Type)
	require.Equal(t, deleteTask.Command, deleteResp.Command)

}

func testCreateKMS(t *testing.T, server *agw.Server, client *Client) {

	// Firstly start the client, waiting for server tasks.
	go func() {
		ServeClient(client)
	}()

	time.Sleep(200 * time.Millisecond)

	// 1. Test create KMS Secret
	// Prepare the task parameters.
	namespace := []byte("77a76a17-68f3-4975-8960-2a78c3b37010")
	secretName := []byte("secret-name")
	roleARN := []byte("role-arn")
	region := []byte("region")
	keyID := []byte("key-id")
	externalID := []byte("external-id")
	encryptedDEK := []byte("encrypted-dek")

	task := &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"secretName":   secretName,
			"roleARN":      roleARN,
			"region":       region,
			"keyID":        keyID,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	resp, err := sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.NoError(t, err)

	// assert result status
	require.Equal(t, taskqueue.TaskStatusOk, resp.Status)
	// assert result id
	require.Equal(t, task.Id, resp.ReplyToId)
	// assert result target
	require.Equal(t, task.Initiator, resp.Target)
	// assert result initiator
	require.Equal(t, task.Target, resp.Initiator)
	// assert type/command
	require.Equal(t, task.Type, resp.Type)
	require.Equal(t, task.Command, resp.Command)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"secretName":   secretName,
			"roleARN":      roleARN,
			"region":       region,
			"keyID":        keyID,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"roleARN":      roleARN,
			"region":       region,
			"keyID":        keyID,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"secretName":   secretName,
			"region":       region,
			"keyID":        keyID,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"secretName":   secretName,
			"roleARN":      roleARN,
			"keyID":        keyID,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"secretName":   secretName,
			"roleARN":      roleARN,
			"region":       region,
			"externalID":   externalID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":    namespace,
			"secretName":   secretName,
			"roleARN":      roleARN,
			"region":       region,
			"keyID":        keyID,
			"encryptedDEK": encryptedDEK,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)

	// Test missing params
	task = &taskqueue.TGTask{
		Id:        "171",
		Initiator: server.Id(),
		Target:    "cloudprovider1",
		Type:      taskqueue.ControllerTaskTypeGeneral,
		Command:   taskqueue.ControllerCmdCreateKMSSecret,
		Params: map[string][]byte{
			"namespace":  namespace,
			"secretName": secretName,
			"roleARN":    roleARN,
			"region":     region,
			"keyID":      keyID,
			"externalID": externalID,
		},
	}

	// Send the task and wait for the result.
	_, err = sendTaskAndWaitResult(t, server.MachineryServer, task)
	require.Error(t, err)
}

func testMetrics(t *testing.T, server *agw.Server, client *Client) {
	go func() {
		ServeClient(client)
	}()
	server.Stop()

	time.Sleep(200 * time.Millisecond)

	resp, err := http.Get("http://localhost:24240/metrics")
	require.NoError(t, err)
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	bodyString := string(body)
	require.NoError(t, err)
	require.Equal(t, 200, resp.StatusCode)
	err = validatePrometheusMetrics(bodyString)
	require.NoError(t, err)
}

func validatePrometheusMetrics(response string) error {
	expectedMetrics := map[string]int{}

	scanner := bufio.NewScanner(strings.NewReader(response))
	for scanner.Scan() {
		line := scanner.Text()
		// Ignore comment lines
		if len(line) > 0 && line[0] == '#' {
			continue
		} else if len(line) > 0 {
			// Parse the metric line
			parts := strings.Fields(line)
			if len(parts) == 2 {
				metric := parts[0]
				value := parts[1]
				num, err := strconv.ParseFloat(value, 64)
				if err != nil {
					return fmt.Errorf("Error parsing the metric value:%v", err)
				}
				expectedMetrics[metric] = int(num)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("Error reading the input:%v", err)
	}

	// Validate the metrics
	_, ok := expectedMetrics["clusteragent_clusteragent_heartbeat_count"]
	if !ok {
		return fmt.Errorf("Expected metric not found: clusteragent_clusteragent_heartbeat_count")
	}
	_, ok = expectedMetrics["clusteragent_clusteragent_success_requests_count"]
	if !ok {
		return fmt.Errorf("Expected metric not found: clusteragent_clusteragent_success_requests_count")
	}
	_, ok = expectedMetrics["clusteragent_clusteragent_total_requests_count"]
	if !ok {
		return fmt.Errorf("Expected metric not found: clusteragent_clusteragent_total_requests_count")
	}
	return nil
}
