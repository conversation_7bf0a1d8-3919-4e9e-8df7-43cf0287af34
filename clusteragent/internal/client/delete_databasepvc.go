package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) DeleteTGDatabsePVC(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing delete tg database pvc")

	// func (client *TGRemoteOperator) DeleteTGDatabsePVC(namespace string, tgDatabaseID string) error {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteTGDatabsePVC, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	tgDatabaseID, ok := params["tgDatabaseID"]
	if !ok {
		log.Error("failed to read tgDatabaseID payload")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteTGDatabsePVC, req.Initiator, nil, 1, "error reading tgDatabaseID")
		return
	}

	err := c.OpClient.DeleteTGDatabsePVC(ctx, string(namespace), string(tgDatabaseID))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteTGDatabsePVC, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteTGDatabsePVC, req.Initiator, respParams, 0, "")
}
