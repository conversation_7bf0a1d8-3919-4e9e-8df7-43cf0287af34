package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) DeleteWorkspace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	// DeleteWorkspace(namespace string, workspaceName string) error
	log.Infof("Processing delete workspace")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkspace, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	err := c.OpClient.DeleteWorkspace(ctx, string(namespace), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkspace, req.Initiator, respParams, 0, "")
}
