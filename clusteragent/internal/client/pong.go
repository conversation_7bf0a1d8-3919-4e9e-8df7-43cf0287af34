package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"

	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) Pong(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Info("pong")
	err := c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypePing, req.Id, "pong", req.Initiator, nil, 0, "")
	if err != nil {
		log.Error(err.<PERSON>rror())
	}
}
