package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) CreateNamespace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing create namespace")

	// func (client *TGRemoteOperator) CreateNamespace(namespace string) error {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateWorkgroup, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	log.Infof("Creating namespace, client: %v", c.OpClient)
	err := c.OpClient.CreateWorkgroup(ctx, string(namespace))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateWorkgroup, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateWorkgroup, req.Initiator, respParams, 0, "")
}

func (c *Client) DeleteNamespace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing delete workgroup")

	// func (client *TGRemoteOperator) DeleteNamespace(namespace string) error {
	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkgroup, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	err := c.OpClient.DeleteWorkgroup(ctx, string(namespace))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkgroup, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteWorkgroup, req.Initiator, respParams, 0, "")
}

func (c *Client) GetWorkgroupLabels(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing get workgroup labels")

	// func (client *TGRemoteOperator) GetWorkgroupLabels(namespace string) (map[string]string, error) {
	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkgroupLabels, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	labels, err := c.OpClient.GetWorkgroupLabels(ctx, string(namespace))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkgroupLabels, req.Initiator, nil, 1, err.Error())
		return
	}

	labelBytes, err := json.Marshal(labels)
	if err != nil {
		log.Errorf("can not marshal labels: %v, labels: %v", err.Error(), labels)
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkgroupLabels, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"labels": labelBytes,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkgroupLabels, req.Initiator, respParams, 0, "")
}

func (c *Client) CreateStorageClass(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	params := req.GetParams()
	workgroupID, ok := params["workgroupID"]
	if !ok {
		log.Error("Failed to read workgroupID from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateStorageClass, req.Initiator, nil, 1, "error reading workgroupID")
		return
	}

	EFSID, ok := params["EFSID"]
	if !ok {
		log.Error("Failed to read EFSID from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateStorageClass, req.Initiator, nil, 1, "error reading EFSID")
		return
	}

	log.Infof("Creating storage class in workgroup [%v]...", workgroupID)
	scName, err := c.OpClient.CreateStorageClass(ctx, string(workgroupID), string(EFSID))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateStorageClass, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{"scName": []byte(scName)}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateStorageClass, req.Initiator, respParams, 0, "")
}

func (c *Client) DeleteStorageClass(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	params := req.GetParams()
	workgroupID, ok := params["workgroupID"]
	if !ok {
		log.Error("Failed to read workgroupID from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteStorageClass, req.Initiator, nil, 1, "error reading workgroupID")
		return
	}

	log.Infof("Deleting storage class in workgroup [%v]...", workgroupID)
	err := c.OpClient.DeleteStorageClass(ctx, string(workgroupID))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteStorageClass, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteStorageClass, req.Initiator, respParams, 0, "")
}
