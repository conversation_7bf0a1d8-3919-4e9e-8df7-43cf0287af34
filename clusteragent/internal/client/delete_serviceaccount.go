package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) DeleteServiceAccount(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing delete service account")

	// func (client *TGRemoteOperator) DeleteServiceAccount(namespace string) error {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteServiceAccount, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	saName, ok := params["saName"]
	if !ok {
		log.Error("failed to read saName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteServiceAccount, req.Initiator, nil, 1, "error reading saName")
		return
	}

	err := c.OpClient.DeleteServiceAccount(ctx, string(namespace), string(saName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteServiceAccount, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteServiceAccount, req.Initiator, respParams, 0, "")
}
