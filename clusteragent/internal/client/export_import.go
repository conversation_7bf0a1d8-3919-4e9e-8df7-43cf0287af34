package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) ExportWorkspace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdExportWorkspace, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdExportWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	log.Infof("Processing export workspace: %v/%v", namespace, workspaceName)
	exportedPath, err := c.OpClient.ExportWorkspace(ctx, string(namespace), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdExportWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"exportedPath": []byte(exportedPath),
	}

	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdExportWorkspace, req.Initiator, respParams, 0, "")
}

func (c *Client) ImportWorkspace(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdImportWorkspace, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdImportWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	exportedPath, ok := params["exportedPath"]
	if !ok {
		log.Error("failed to read exported path from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdImportWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	log.Infof("Processing import workspace: %s/%s, path: %s", namespace, workspaceName, exportedPath)
	err := c.OpClient.ImportWorkspace(ctx, string(namespace), string(workspaceName), string(exportedPath))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdImportWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdImportWorkspace, req.Initiator, respParams, 0, "")
}
