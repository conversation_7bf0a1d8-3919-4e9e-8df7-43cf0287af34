package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) GetWorkspace(ctx context.Context, req *proto.ControlMessage) {
	// GetWorkspace(namespace string, workspaceName string) (*data.Workspace, error)
	log := logger.L().WithContext(ctx)
	log.Infof("Processing get workspace")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkspace, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkspace, req.Initiator, nil, 1, "error reading payload")
		return
	}

	result, err := c.OpClient.GetWorkspace(ctx, string(namespace), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkspace, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"workspace": resp,
	}
	// log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetWorkspace, req.Initiator, respParams, 0, "")
}
