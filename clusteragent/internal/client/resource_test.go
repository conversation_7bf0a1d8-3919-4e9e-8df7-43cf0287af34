package client

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	pb "github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/resource-manager/interfaces"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	cio "github.com/tigergraph/cloud-universe/clusteragent/internal/observability"
)

func TestClient_DeleteResource(t *testing.T) {
	type fields struct {
		Stream   pb.TGControlPlaneService_ControlAgentCommandsClient
		OpClient interfaces.TGOperator
	}
	type args struct {
		req *pb.ControlMessage
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantOpRequest *data.DeleteResourceRequest
		wantStreamMsg *pb.AgentMessage
	}{
		{
			"normal",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","deleteOptions":{"dryRun":["test-dryrun"]}}`),
					},
				},
			},
			&data.DeleteResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				Name:      "test-name",
				DeleteOptions: metav1.DeleteOptions{
					DryRun: []string{"test-dryrun"},
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResource,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
		{
			"invalid request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`x{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","deleteOptions":{"dryRun":["test-dryrun"]}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResource,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "invalid character 'x' looking for beginning of value",
			},
		},
		{
			"no request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"xrequest": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","deleteOptions":{"dryRun":["test-dryrun"]}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResource,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "error reading payload",
			},
		},
		{
			"delete error",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					deleteResourceErr: errors.New("delete resource error"),
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","deleteOptions":{"dryRun":["test-dryrun"]}}`),
					},
				},
			},
			&data.DeleteResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				Name:      "test-name",
				DeleteOptions: metav1.DeleteOptions{
					DryRun: []string{"test-dryrun"},
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResource,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "delete resource error",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCam := cio.NewClusterAgentMetrics("0ce1640b-153b-4732-9ee4-f8ea54368f4b")
			c := &Client{
				Stream:              tt.fields.Stream,
				OpClient:            tt.fields.OpClient,
				ClusterAgentMetrics: mockCam,
			}

			c.DeleteResource(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantOpRequest, tt.fields.OpClient.(*mockOperator).reqDeleteResource)

			if tt.wantStreamMsg != nil {
				if tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq != nil {
					sendMsgReq := tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq
					sendMsgReq.Id = ""
					sendMsgReq.Timestamp = nil
				}
				expectedMsgJsonBytes, err := json.Marshal(tt.wantStreamMsg)
				assert.NoError(t, err)
				actualMsgJsonBytes, err := json.Marshal(tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
				assert.NoError(t, err)
				assert.Equal(t, expectedMsgJsonBytes, actualMsgJsonBytes)
			} else {
				assert.Nil(t, tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
			}

		})
	}
}

func TestClient_GetResource(t *testing.T) {
	testCmJson := `{"apiVersion":"v1","data":{"cluster_size":"1","cluster_size_pre":"0","ha":"1"},"kind":"ConfigMap","metadata":{"creationTimestamp":"2024-12-10T09:49:32Z","labels":{"tigergraph.com/cluster-name":"tg-98693b44-f467-4c00-8ffe-350f97a9fa84"},"name":"tg-98693b44-f467-4c00-8ffe-350f97a9fa84-env-config","namespace":"ec85bbdb-3df2-4036-8ba6-c314f84c8a98","ownerReferences":[{"apiVersion":"graphdb.tigergraph.com/v1alpha1","blockOwnerDeletion":true,"controller":true,"kind":"TigerGraph","name":"tg-98693b44-f467-4c00-8ffe-350f97a9fa84","uid":"0c1e4136-a98f-4076-95eb-331403a5a6b6"}],"resourceVersion":"58505238","uid":"f0113cf6-be40-4610-986b-ae614afda132"}}`
	testUnstructure := &unstructured.Unstructured{}
	testUnstructure.UnmarshalJSON([]byte(testCmJson))

	type fields struct {
		Stream   pb.TGControlPlaneService_ControlAgentCommandsClient
		OpClient interfaces.TGOperator
	}
	type args struct {
		req *pb.ControlMessage
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantOpRequest *data.GetResourceRequest
		wantStreamMsg *pb.AgentMessage
	}{
		{
			"normal",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					unstructured: testUnstructure,
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","getOptions":{"resourceVersion":"test-dryrun"}}`),
					},
				},
			},
			&data.GetResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				Name:      "test-name",
				GetOptions: metav1.GetOptions{
					ResourceVersion: "test-dryrun",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdGetResource,
				Params: map[string][]byte{
					"response": []byte(testCmJson),
				},
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
		{
			"invalid request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`x{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","getOptions":{"resourceVersion":"test-dryrun"}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdGetResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "invalid character 'x' looking for beginning of value",
			},
		},
		{
			"no request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"xrequest": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","getOptions":{"resourceVersion":"test-dryrun"}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdGetResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "error reading payload",
			},
		},
		{
			"get error",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					getResourceErr: errors.New("get resource error"),
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","getOptions":{"resourceVersion":"test-dryrun"}}`),
					},
				},
			},
			&data.GetResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				Name:      "test-name",
				GetOptions: metav1.GetOptions{
					ResourceVersion: "test-dryrun",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdGetResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "get resource error",
			},
		},
		{
			"empty result",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","getOptions":{"resourceVersion":"test-dryrun"}}`),
					},
				},
			},
			&data.GetResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				Name:      "test-name",
				GetOptions: metav1.GetOptions{
					ResourceVersion: "test-dryrun",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdGetResource,
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCam := cio.NewClusterAgentMetrics("0ce1640b-153b-4732-9ee4-f8ea54368f4b")
			c := &Client{
				Stream:              tt.fields.Stream,
				OpClient:            tt.fields.OpClient,
				ClusterAgentMetrics: mockCam,
			}
			c.GetResource(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantOpRequest, tt.fields.OpClient.(*mockOperator).reqGetResource)

			if tt.wantStreamMsg != nil {
				if tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq != nil {
					sendMsgReq := tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq
					sendMsgReq.Id = ""
					sendMsgReq.Timestamp = nil
				}
				expectedMsgJsonBytes, err := json.Marshal(tt.wantStreamMsg)
				assert.NoError(t, err)
				actualMsgJsonBytes, err := json.Marshal(tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
				assert.NoError(t, err)

				assert.Equal(t, string(expectedMsgJsonBytes), string(actualMsgJsonBytes))
			} else {
				assert.Nil(t, tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
			}

		})
	}
}

func TestClient_ListResource(t *testing.T) {
	testCmJson := `{"apiVersion":"v1","items":[{"apiVersion":"v1","data":{"cluster_size":"1","cluster_size_pre":"1","ha":"1","namespace":"ec85bbdb-3df2-4036-8ba6-c314f84c8a98","pod.prefix":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5","service.headless.name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5-internal-service"},"kind":"ConfigMap","metadata":{"creationTimestamp":"2024-12-10T09:55:34Z","labels":{"tigergraph.com/cluster-name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5"},"name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5-env-config","namespace":"ec85bbdb-3df2-4036-8ba6-c314f84c8a98","ownerReferences":[{"apiVersion":"graphdb.tigergraph.com/v1alpha1","blockOwnerDeletion":true,"controller":true,"kind":"TigerGraph","name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5","uid":"29c44cee-e613-4efa-bb8d-3b63a80a4cd7"}],"resourceVersion":"58517353","uid":"b672b394-36a5-458a-b7bc-f0edc0504c25"}},{"apiVersion":"v1","data":{"init_tg_cfg":"System.SSH.User.Username: tigergraph"},"kind":"ConfigMap","metadata":{"creationTimestamp":"2024-12-10T09:55:33Z","labels":{"tigergraph.com/cluster-name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5"},"name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5-init-config","namespace":"ec85bbdb-3df2-4036-8ba6-c314f84c8a98","ownerReferences":[{"apiVersion":"graphdb.tigergraph.com/v1alpha1","blockOwnerDeletion":true,"controller":true,"kind":"TigerGraph","name":"tg-edd8f69a-a81d-4102-8ddf-e21803af51c5","uid":"29c44cee-e613-4efa-bb8d-3b63a80a4cd7"}],"resourceVersion":"58509589","uid":"fd37ddc2-1d8d-419a-8b7d-1ffa7a090d94"}}],"kind":"List","metadata":{"resourceVersion":""}}`
	testUnstructure := &unstructured.UnstructuredList{}
	testUnstructure.UnmarshalJSON([]byte(testCmJson))

	type fields struct {
		Stream   pb.TGControlPlaneService_ControlAgentCommandsClient
		OpClient interfaces.TGOperator
	}
	type args struct {
		req *pb.ControlMessage
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantOpRequest *data.ListResourceRequest
		wantStreamMsg *pb.AgentMessage
	}{
		{
			"normal",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					unstructuredList: testUnstructure,
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","listOptions":{"labelSelector":"aaa"}}`),
					},
				},
			},
			&data.ListResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				ListOptions: metav1.ListOptions{
					LabelSelector: "aaa",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdListResource,
				Params: map[string][]byte{
					"response": []byte(testCmJson),
				},
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
		{
			"invalid request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`x{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","listOptions":{"labelSelector":"aaa"}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdListResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "invalid character 'x' looking for beginning of value",
			},
		},
		{
			"no request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"xrequest": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","listOptions":{"labelSelector":"aaa"}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdListResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "error reading payload",
			},
		},
		{
			"list error",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					listResourceErr: errors.New("list resource error"),
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","listOptions":{"labelSelector":"aaa"}}`),
					},
				},
			},
			&data.ListResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				ListOptions: metav1.ListOptions{
					LabelSelector: "aaa",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdListResource,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "list resource error",
			},
		},
		{
			"empty result",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","listOptions":{"labelSelector":"aaa"}}`),
					},
				},
			},
			&data.ListResourceRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				ListOptions: metav1.ListOptions{
					LabelSelector: "aaa",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdListResource,
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCam := cio.NewClusterAgentMetrics("0ce1640b-153b-4732-9ee4-f8ea54368f4b")
			c := &Client{
				Stream:              tt.fields.Stream,
				OpClient:            tt.fields.OpClient,
				ClusterAgentMetrics: mockCam,
			}
			c.ListResource(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantOpRequest, tt.fields.OpClient.(*mockOperator).reqListResource)

			if tt.wantStreamMsg != nil {
				if tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq != nil {
					sendMsgReq := tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq
					sendMsgReq.Id = ""
					sendMsgReq.Timestamp = nil
				}
				expectedMsgJsonBytes, err := json.Marshal(tt.wantStreamMsg)
				assert.NoError(t, err)
				actualMsgJsonBytes, err := json.Marshal(tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
				assert.NoError(t, err)

				assert.Equal(t, string(expectedMsgJsonBytes), string(actualMsgJsonBytes))
			} else {
				assert.Nil(t, tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
			}
		})
	}
}

func TestClient_DeleteResourceCollection(t *testing.T) {
	type fields struct {
		Stream   pb.TGControlPlaneService_ControlAgentCommandsClient
		OpClient interfaces.TGOperator
	}
	type args struct {
		req *pb.ControlMessage
	}
	tests := []struct {
		name          string
		fields        fields
		args          args
		wantOpRequest *data.DeleteResourceCollectionRequest
		wantStreamMsg *pb.AgentMessage
	}{
		{
			"normal",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","deleteOptions":{"dryRun":["test-dryrun"]},"listOptions":{"labelSelector":"test-selector"}}`),
					},
				},
			},
			&data.DeleteResourceCollectionRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				DeleteOptions: metav1.DeleteOptions{
					DryRun: []string{"test-dryrun"},
				},
				ListOptions: metav1.ListOptions{
					LabelSelector: "test-selector",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResourceCollection,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    0,
				Error:     "",
			},
		},
		{
			"invalid request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`x{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","deleteOptions":{"dryRun":["test-dryrun"]},"listOptions":{"labelSelector":"test-selector"}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResourceCollection,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "invalid character 'x' looking for beginning of value",
			},
		},
		{
			"no request",
			fields{
				Stream:   &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"xrequest": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","name":"test-name","deleteOptions":{"dryRun":["test-dryrun"]}}`),
					},
				},
			},
			nil,
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResourceCollection,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "error reading payload",
			},
		},
		{
			"delete error",
			fields{
				Stream: &mockTGControlPlaneService_ControlAgentCommandsClient{},
				OpClient: &mockOperator{
					deleteResourceCollectionErr: errors.New("delete resource collection error"),
				},
			},
			args{
				req: &pb.ControlMessage{
					Id:        "test-id",
					Initiator: "test-indicator",
					Params: map[string][]byte{
						"request": []byte(`{"group":"test-group","version":"test-version","resource":"test-resource","namespace":"test-namespace","deleteOptions":{"dryRun":["test-dryrun"]},"listOptions":{"labelSelector":"test-selector"}}`),
					},
				},
			},
			&data.DeleteResourceCollectionRequest{
				Group:     "test-group",
				Version:   "test-version",
				Resource:  "test-resource",
				Namespace: "test-namespace",
				DeleteOptions: metav1.DeleteOptions{
					DryRun: []string{"test-dryrun"},
				},
				ListOptions: metav1.ListOptions{
					LabelSelector: "test-selector",
				},
			},
			&pb.AgentMessage{
				Type:      taskqueue.ControllerTaskTypeGeneral,
				Initiator: "",
				Target:    "test-indicator",
				Command:   taskqueue.ControllerCmdDeleteResourceCollection,
				Params:    nil,
				ReplyToId: "test-id",
				Status:    1,
				Error:     "delete resource collection error",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCam := cio.NewClusterAgentMetrics("0ce1640b-153b-4732-9ee4-f8ea54368f4b")
			c := &Client{
				Stream:              tt.fields.Stream,
				OpClient:            tt.fields.OpClient,
				ClusterAgentMetrics: mockCam,
			}
			c.DeleteResourceCollection(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantOpRequest, tt.fields.OpClient.(*mockOperator).reqDeleteResourceCollection)

			if tt.wantStreamMsg != nil {
				if tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq != nil {
					sendMsgReq := tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq
					sendMsgReq.Id = ""
					sendMsgReq.Timestamp = nil
				}
				expectedMsgJsonBytes, err := json.Marshal(tt.wantStreamMsg)
				assert.NoError(t, err)
				actualMsgJsonBytes, err := json.Marshal(tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
				assert.NoError(t, err)

				assert.Equal(t, string(expectedMsgJsonBytes), string(actualMsgJsonBytes))
			} else {
				assert.Nil(t, tt.fields.Stream.(*mockTGControlPlaneService_ControlAgentCommandsClient).sendMsgReq)
			}

		})
	}
}

type mockOperator struct {
	interfaces.TGOperator
	reqDeleteResource           *data.DeleteResourceRequest
	deleteResourceErr           error
	reqDeleteResourceCollection *data.DeleteResourceCollectionRequest
	deleteResourceCollectionErr error
	reqGetResource              *data.GetResourceRequest
	getResourceErr              error
	unstructured                *unstructured.Unstructured
	reqListResource             *data.ListResourceRequest
	listResourceErr             error
	unstructuredList            *unstructured.UnstructuredList
}

func (mo *mockOperator) DeleteResource(ctx context.Context, req *data.DeleteResourceRequest) error {
	mo.reqDeleteResource = req
	return mo.deleteResourceErr
}

func (mo *mockOperator) DeleteResourceCollection(ctx context.Context, req *data.DeleteResourceCollectionRequest) error {
	mo.reqDeleteResourceCollection = req
	return mo.deleteResourceCollectionErr
}

func (mo *mockOperator) GetResource(ctx context.Context, req *data.GetResourceRequest) (*unstructured.Unstructured, error) {
	mo.reqGetResource = req
	return mo.unstructured, mo.getResourceErr
}

func (mo *mockOperator) ListResource(ctx context.Context, req *data.ListResourceRequest) (*unstructured.UnstructuredList, error) {
	mo.reqListResource = req
	return mo.unstructuredList, mo.listResourceErr
}

type mockTGControlPlaneService_ControlAgentCommandsClient struct {
	pb.TGControlPlaneService_ControlAgentCommandsClient
	sendErr    error
	sendMsgReq *pb.AgentMessage
}

func (m *mockTGControlPlaneService_ControlAgentCommandsClient) Send(msg *pb.AgentMessage) error {
	m.sendMsgReq = msg
	return m.sendErr
}
