package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) CreateBackup(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing create backup task")

	// func (client *TGOperatorClient) CreateWorkspace(namespace string, request *data.CreateWorkspaceRequest) (*data.Workspace, error) {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, nil, 1, "error reading workspace name")
		return
	}

	reqPayload, ok := params["request"]
	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.CreateBackupRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.CreateBackup(ctx, string(namespace), string(workspaceName), opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"backupName": []byte(result),
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateBackup, req.Initiator, respParams, 0, "")
}

func (c *Client) ListBackups(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupList, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupList, req.Initiator, nil, 1, "error reading workspace name")
		return
	}

	result, err := c.OpClient.ListBackups(ctx, string(namespace), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupList, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupList, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"backups": resp,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupList, req.Initiator, respParams, 0, "")
}

func (c *Client) RestoreBackup(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)

	log.Infof("Processing restore backup task")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, nil, 1, "error reading workspace name")
		return
	}

	reqPayload, ok := params["request"]
	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.CreateRestoreRequest{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, nil, 1, err.Error())
		return
	}

	result, err := c.OpClient.RestoreBackup(ctx, string(namespace), string(workspaceName), opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"restoreName": []byte(result),
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdRestoreBackup, req.Initiator, respParams, 0, "")
}

func (c *Client) DeleteBackup(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)

	log.Infof("Processing delete backup task")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteBackup, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteBackup, req.Initiator, nil, 1, "error reading workspace name")
		return
	}
	tag, ok := params["tag"]
	if !ok {
		log.Error("failed to read backup name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteBackup, req.Initiator, nil, 1, "error reading backup name")
		return
	}

	err := c.OpClient.DeleteBackup(ctx, string(namespace), string(workspaceName), string(tag))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteBackup, req.Initiator, nil, 1, err.Error())
		return
	}

	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdDeleteBackup, req.Initiator, nil, 0, "")
}

func (c *Client) GetBackupRestoreStatus(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)

	log.Infof("Processing get backup restore status task")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, nil, 1, "error reading workspace name")
		return
	}
	job, ok := params["job"]
	if !ok {
		log.Error("failed to read backup name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, nil, 1, "error reading backup name")
		return
	}

	result, err := c.OpClient.GetBackupRestoreStatus(ctx, string(namespace), string(workspaceName), string(job))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"status": resp,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupRestoreStatus, req.Initiator, respParams, 0, "")
}

func (c *Client) SetBackupSchedule(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing set backup schedule task")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 1, "error reading workspace name")
		return
	}

	reqPayload, ok := params["request"]
	if !ok {
		log.Error("failed to read request payload from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 1, "error reading payload")
		return
	}

	opRequest := &data.BackupSchedule{}
	err := json.Unmarshal(reqPayload, opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 1, err.Error())
		return
	}

	err = c.OpClient.SetBackupSchedule(ctx, string(namespace), string(workspaceName), opRequest)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 1, err.Error())
		return
	}

	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdSetBackupSchedule, req.Initiator, nil, 0, "")
}

func (c *Client) GetBackupSchedule(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing get backup schedule task")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupSchedule, req.Initiator, nil, 1, "error reading namespace")
		return
	}
	workspaceName, ok := params["workspaceName"]
	if !ok {
		log.Error("failed to read workspace name from params")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupSchedule, req.Initiator, nil, 1, "error reading workspace name")
		return
	}

	result, err := c.OpClient.GetBackupSchedule(ctx, string(namespace), string(workspaceName))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupSchedule, req.Initiator, nil, 1, err.Error())
		return
	}

	resp, err := json.Marshal(result)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupSchedule, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"schedule": resp,
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdGetBackupSchedule, req.Initiator, respParams, 0, "")
}
