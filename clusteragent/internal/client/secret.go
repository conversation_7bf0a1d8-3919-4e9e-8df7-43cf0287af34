package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) CreateKMSSecret(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing create kms secret")

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	secretName, ok := params["secretName"]
	if !ok {
		log.Error("failed to read secretName")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading secretName")
		return
	}

	roleARN, ok := params["roleARN"]
	if !ok {
		log.Error("failed to read roleARN")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading roleARN")
		return
	}

	region, ok := params["region"]
	if !ok {
		log.Error("failed to read region")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading region")
		return
	}

	keyID, ok := params["keyID"]
	if !ok {
		log.Error("failed to read keyID")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading keyID")
		return
	}

	externalID, ok := params["externalID"]
	if !ok {
		log.Error("failed to read externalID")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading externalID")
		return
	}

	encryptedDEK, ok := params["encryptedDEK"]
	if !ok {
		log.Error("failed to read encryptedDEK")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, "error reading encryptedDEK")
		return
	}

	err := c.OpClient.CreateKMSSecret(ctx, string(namespace), string(secretName), string(roleARN), string(region), string(keyID), string(externalID), encryptedDEK)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"secretName": []byte(secretName),
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateKMSSecret, req.Initiator, respParams, 0, "")
}
