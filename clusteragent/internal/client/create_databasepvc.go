package client

import (
	"context"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) CreateTGDatabsePVC(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing create tg database pvc")

	// func (client *TGRemoteOperator) CreateTGDatabsePVC(namespace string, tgDatabaseID string, storageClass string, size string) (pvcName string, err error) {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Error("failed to read namespace")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, nil, 1, "error reading namespace")
		return
	}

	tgDatabaseID, ok := params["tgDatabaseID"]
	if !ok {
		log.Error("failed to read tgDatabaseID payload")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, nil, 1, "error reading tgDatabaseID")
		return
	}

	storageClass, ok := params["storageClass"]
	if !ok {
		log.Error("failed to read storageClass payload")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, nil, 1, "error reading storageClass")
		return
	}

	size, ok := params["size"]
	if !ok {
		log.Error("failed to read size payload")
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, nil, 1, "error reading size")
		return
	}

	pvcName, err := c.OpClient.CreateTGDatabsePVC(ctx, string(namespace), string(tgDatabaseID), string(storageClass), string(size))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, nil, 1, err.Error())
		return
	}

	respParams := map[string][]byte{
		"pvcName": []byte(pvcName),
	}
	log.Info("succ")

	c.SendMsgToServer(ctx, taskqueue.ControllerTaskTypeGeneral, req.Id, taskqueue.ControllerCmdCreateTGDatabsePVC, req.Initiator, respParams, 0, "")
}
