package client

import (
	"context"
	"encoding/json"

	"github.com/tigergraph/cloud-universe/proto"
	"github.com/tigergraph/cloud-universe/utils/logger"
	tq "github.com/tigergraph/cloud-universe/utils/taskqueue"
)

func (c *Client) ListWorkspaces(ctx context.Context, req *proto.ControlMessage) {
	log := logger.L().WithContext(ctx)
	log.Infof("Processing list workspace")

	// (client *TGOperatorClient) ListWorkspaces(namespace string) (results []*data.Workspace, err error) {

	params := req.GetParams()
	namespace, ok := params["namespace"]
	if !ok {
		log.Errorf("failed to read param `namespace`")
		c.SendMsgToServer(ctx, tq.ControllerTaskTypeGeneral, req.Id, tq.ControllerCmdListWorkspaces, req.Initiator, nil, 1, "failed to read param `namespace`")
		return
	}

	// operator.ListWorkspaces(namespace)

	results, err := c.OpClient.ListWorkspaces(ctx, string(namespace))
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, tq.ControllerTaskTypeGeneral, req.Id, tq.ControllerCmdListWorkspaces, req.Initiator, nil, 1, err.Error())
		return
	}

	log.Info(results)

	resp, err := json.Marshal(results)
	if err != nil {
		log.Errorf(err.Error())
		c.SendMsgToServer(ctx, tq.ControllerTaskTypeGeneral, req.Id, tq.ControllerCmdListWorkspaces, req.Initiator, nil, 1, err.Error())
	}

	param := map[string][]byte{
		"results": resp,
	}

	c.SendMsgToServer(ctx, tq.ControllerTaskTypeGeneral, req.Id, tq.ControllerCmdListWorkspaces, req.Initiator, param, 0, "")
}
