package main

import (
	"flag"
	"fmt"

	"github.com/tigergraph/cloud-universe/clusteragent/config"
	"github.com/tigergraph/cloud-universe/clusteragent/internal/client"
	"github.com/tigergraph/cloud-universe/resource-manager/operator"
	ctxextractor "github.com/tigergraph/cloud-universe/utils/logger/ctxextractor"
	"github.com/tigergraph/cloud-universe/utils/logger/loggeriface"

	"github.com/tigergraph/cloud-universe/utils/logger"
)

var (
	Version string
	Conf    *config.Config // Stores all the configurations for the client
)

func main() {
	logger.ReplaceGlobal(logger.L().WithCtxExtractors([]loggeriface.CtxExtractor{ctxextractor.RequestId, ctxextractor.UserInfo}))
	log := logger.L()
	flag.Parse()
	Conf, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err.Error()))
	}

	opClient, err := operator.NewTGOperatorClient(&Conf.K8SConfig, &Conf.PodConfig)
	if err != nil {
		panic(fmt.Sprintf("failed to create operator client: %v", err.Error()))
	}

	log.Info("AuthServerAddr: ", Conf.AuthServerAddr)
	log.Info("GRPCServerAddr: ", Conf.GRPCServerAddr)
	log.Info("K8sEndpointAddr: ", Conf.K8SConfig.Endpoint)
	log.Info("Subdomain: ", Conf.K8SConfig.Subdomain)
	log.Info("Token: ", Conf.K8SConfig.Token)

	c, err := client.NewClient(Conf, opClient, Version)
	if err != nil {
		panic(fmt.Sprintf("failed to create client: %v", err.Error()))
	}

	client.ServeClient(c)
}
