package config

import (
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
	resConf "github.com/tigergraph/cloud-universe/resource-manager/config"
)

type Config struct {
	CloudProviderId string `yaml:"cloudproviderid"`
	OrgId           string `yaml:"orgid"`
	Token           string `yaml:"token"`
	AuthServerAddr  string `yaml:"authserveraddr"`
	GRPCServerAddr  string `yaml:"grpcserveraddr"`
	ExporterAddr    string `yaml:"exporteraddr"`
	MaxMsgSize      int    `yaml:"maxmsgsize"`

	AuthServerTimeOut int `yaml:"authservertimeout"`
	RetryCount        int `yaml:"retrycount"`
	RetryInterval     int `yaml:"retryinterval"`

	HeartbeatInterval int  `yaml:"heartbeatinterval"`
	NonSecure         bool `yaml:"nonsecure"`

	UpdateTimeOut int `yaml:"updatetimeout"`
	UpdateTicker  int `yaml:"updateticker"`

	MaxTransactionNum    int `yaml:"maxtransactionnum"`
	TransactionRateLimit int `yaml:"transactionratelimit"`
	MaintenanceLiveSpan  int `yaml:"maintenancelivespan"`

	K8SConfig resConf.K8SConfig `yaml:"k8sConfig"`
	PodConfig resConf.PodConfig `yaml:"podConfig"`

	NoK8SConnection bool `yaml:"noK8SConnection"`
	// those image tags are for CA to report versions to AGW
	ClusterAgentImageTag     string `yaml:"clusteragentimagetag"`
	AutoStartHandlerImageTag string `yaml:"autostarthandlerimagetag"`
	TGOperatorImageTag       string `yaml:"tgoperatorimagetag"`
}

func LoadConfig() (*Config, error) {

	// Paths to search the config file
	viper.AddConfigPath(".")
	viper.AddConfigPath("./client")
	viper.AddConfigPath("./cmd/client")
	viper.AddConfigPath("/etc/tg_agent/")

	// Paths for development only
	viper.AddConfigPath("..")
	viper.AddConfigPath("../client")
	viper.AddConfigPath("../cmd/client")

	// Set name of the config file
	viper.SetConfigName("cluster_agent_config")
	viper.SetConfigType("yaml")

	viper.AutomaticEnv()

	config := &Config{}

	//viper.SetConfigFile(path)
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}
	err := viper.Unmarshal(config)
	if err != nil {
		return nil, err
	}
	// Validate config.
	if err = validator.New().Struct(config); err != nil {
		return nil, fmt.Errorf("invalid config: %v", err)
	}

	return config, nil
}
