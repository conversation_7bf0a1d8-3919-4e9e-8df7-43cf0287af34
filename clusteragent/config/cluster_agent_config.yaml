AuthServerAddr: '127.0.0.1:1234'
GRPCServerAddr: '127.0.0.1:50051'
ExporterAddr: '127.0.0.1:9955'
HeartbeatInterval: 10
CloudProviderId: '0x00000001'
OrgId: '0x00000001'
Token: qwerty
MaxMsgSize: 1000
AuthServerTimeOut: 5
RetryCount: 5
RetryInterval: 5
UpdateTimeOut: 5
UpdateTicker: 5
MaxTransactionNum: 99
TransactionRateLimit: 99
MaintenanceLiveSpan: 99

k8sConfig:
  region: us-east-2
  endpoint: https://96AFFCFD0E2C00E953393CA78B6B12A1.gr7.us-east-2.eks.amazonaws.com
  token: eyqwerty
  subdomain: us-east-2.i.tgcloud-dev.com
  certARN: arn:aws:acm:us-east-1:022200819440:certificate/qwerty
  enableImagePullSecret: true
  imagePullSecret: eyqwerty
podConfig:
  enableImagePullSecret: true
  imagePullSecret: eyqwerty
  tigergraphConfig:
    ssoUrl: https://auth.tgcloud-dev.com/authorize
    clientId: clientid
    jwksUrl: https://auth.tgcloud-dev.com/.well-known/jwks.json
    clientSecret: ""
  tgAgentConfig:
    image: tginternal/cloud-agent:01291159
    auth0Domain: tgcloud-dev.auth0.com
    iamServerUrl: https://api.tgcloud-dev.com/iam
    sqsUrl: https://sqs.us-east-1.amazonaws.com/022200819440/agent-heatbeat
    m2mClientId: m2mclientid
    m2mClientSecret: m2mclientsecret
  efsPVCConfig:
    storageClass: efs-sc
    size: 10Gi
    mountPath: /efs-data
  license: eyqwertyuiop