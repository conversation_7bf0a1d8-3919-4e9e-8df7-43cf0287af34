openapi: 3.0.3
info:
  title: Consumption-based Resource Manager External API
  description: API for GSQL File Store
  version: v2
tags:
  - name: File
    description: File related APIs
  - name: Share
    description: Share related APIs
paths:
  /api/v2/files:
    get:
      summary: get files
      tags:
        - File
      responses:
        200:
          description: File listed successfully.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/FileSystem"
    post:
      summary: create files
      tags:
        - File
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateFileRequest"
      responses:
        200:
          description: File created successfully.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/File"

  /api/v2/files/{file_id}:
    get:
      summary: get detail of the file
      tags:
        - File
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/File"

    delete:
      summary: delete the file
      tags:
        - File
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"

  /api/v2/files/{file_id}/rename:
    post:
      summary: rename the file
      tags:
        - File
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RenameFileRequest"
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/File"

  /api/v2/files/{file_id}/save:
    post:
      summary: save the file
      tags:
        - File
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveFileRequest"
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/File"
  /api/v2/files/{file_id}/move:
    post:
      summary: move the file
      tags:
        - File
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveFileRequest"
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/File"

  /api/v2/files/{file_id}/share:
    get:
      summary: get detail of the file share
      tags:
        - Share
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/FileShares"

    post:
      summary: update file share settings
      tags:
        - Share
      parameters:
        - in: path
          name: file_id
          description: Identification for file
          required: true
          schema:
            type: string
            example: bdc5e998-be7e-4026-88d9-bafe11543fd
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateFileShareRequest"
      responses:
        200:
          description: successfully returned
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/GeneralResponse"
                  - type: object
                    properties:
                      Result:
                        $ref: "#/components/schemas/FileShares"

components:
  schemas:
    GeneralResponse:
      type: object
      properties:
        RequestID:
          example: req_jI0dXW3BvHDklEBv
          type: string
        Error:
          type: boolean
          example: true
        Message:
          type: string
        ErrorDetails:
          type: array
          items:
            $ref: "#/components/schemas/FormErrorDetails"
        Result:
          type: object

    FormErrorDetails:
      type: object
      properties:
        Type:
          type: string
        Field:
          type: string
        Reason:
          type: string

    CreateFileRequest:
      type: object
      properties:
        name:
          type: string
          example: file name
        is_folder:
          type: boolean
          example: true
        content:
          type: string
          example: use graph Social
        parent_id:
          type: string
          example: bdc5e998-be7e-4026-88d9-bafe11543fd0

    RenameFileRequest:
      type: object
      properties:
        name:
          type: string
          example: new file name

    SaveFileRequest:
      type: object
      properties:
        content:
          type: string
          example: use graph Social

    MoveFileRequest:
      type: object
      properties:
        parent_id:
          type: string
          nullable: true
          example: bdc5e998-be7e-4026-88d9-bafe11543fd0

    FileSystem:
      type: object
      properties:
        owned_files:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/File"
        shared_files:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/File"

    File:
      type: object
      properties:
        id:
          type: string
          example: bdc5e998-be7e-4026-88d9-bafe11543fd0
        created_at:
          type: string
          format: date-time
          example: "2019-12-17T01:14:20.737651Z"
        updated_at:
          type: string
          format: date-time
          example: "2019-12-17T01:26:22.630353Z"
        name:
          type: string
          example: file name
        org_id:
          type: string
        owner_email:
          type: string
        is_folder:
          type: boolean
          example: true
        content:
          type: string
          example: use graph Social
        parent_id:
          type: string
          example: bdc5e998-be7e-4026-88d9-bafe11543fd0
        permission:
          type: string
          example: view
        files:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/File"

    FileShares:
      type: array
      example: []
      items:
        allOf:
          - $ref: "#/components/schemas/FileShare"

    FileShare:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
          example: "2019-12-17T01:14:20.737651Z"
        updated_at:
          type: string
          format: date-time
          example: "2019-12-17T01:26:22.630353Z"
        file_store_id:
          type: string
          example: bdc5e998-be7e-4026-88d9-bafe11543fd0
        type:
          type: string
          enum: [user, org]
          example: user
        target:
          type: string
          example: <EMAIL>
        permission:
          type: string
          enum: [view, edit, owner]
          example: view

    AddUpdateShare:
      type: object
      properties:
        type:
          type: string
          enum: [user, org]
          example: user
        target:
          type: string
          example: <EMAIL>
        permission:
          type: string
          enum: [view, edit]
          example: view

    DeleteShare:
      type: object
      properties:
        type:
          type: string
          enum: [user, org]
          example: user
        target:
          type: string
          example: <EMAIL>

    UpdateFileShareRequest:
      type: object
      properties:
        add_shares:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/AddUpdateShare"
        update_shares:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/AddUpdateShare"
        delete_shares:
          type: array
          example: []
          items:
            allOf:
              - $ref: "#/components/schemas/DeleteShare"
