import os
import requests
import sys
import logging
import json
import csv
import re
import copy
from datetime import datetime, timedelta
from dateutil import parser
import psycopg2
from psycopg2 import sql
from psycopg2.extras import DictCursor
from typing import Optional, Dict, List, Any, Union, Tuple
from dateutil.relativedelta import relativedelta
from requests.exceptions import RequestException


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

# Constants and configurations
PATH_PROJECT_MAPPING = json.loads(os.environ.get('PATH_PROJECT_MAPPING', json.dumps({
    "TP": ["kafka", "cmd/", "cqrs", "zk", "etcd", "_infr_", "tg_shadower", "libgoutils.so", "ubuntu"],
    "GLE": ["gsql"],
    "CORE": ["bin/"],
    "APPS": ["gui/"]
})))

CELL_TEMPLATE = '{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"%s"}]}]}'
CELL_LINK_TEMPLATE = '{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"%s","marks":[{"type":"link","attrs":{"href":"%s"}}]}]}]}'
CELL_STRIKE_TEMPLATE = '{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"%s","marks":[{"type":"strike"}]}]}]}'

PROJECT_USER_MAPPING = json.loads(os.environ.get('PROJECT_USER_MAPPING', json.dumps({
    "APPS": {"assignee": "yuesen.xiong", "reporter": "chengrun.peng", "project_id": "18483", "component_id": "14711"},
    "TOOLS": {"assignee": "lin.yu", "reporter": "chengrun.peng", "project_id": "18483", "component_id": "14711"},
    "TP": {"assignee": "fan.yang", "reporter": "chengrun.peng", "project_id": "18438", "component_id": "14742"},
    "GLIVE": {"assignee": "runli.long", "reporter": "chengrun.peng", "project_id": "18438", "component_id": "14742"},
    "GLE": {"assignee": "dadong.wang", "reporter": "chengrun.peng", "project_id": "14601", "component_id": "14628"},
    "CORE": {"assignee": "jian.gong", "reporter": "chengrun.peng", "project_id": "12000", "component_id": "14588"},
    "QA": {"assignee": "chengrun.peng", "reporter": "chengrun.peng", "project_id": "16300", "component_id": "14542"}
})))

SEVERITY_MAPPING = {
    "high": ["high", "critical"],
    "medium": ["medium", "low"]
}

ATLASSIAN_API_URL = 'https://graphsql.atlassian.net/rest/api/3'

db_config = {
    "dbname": "security",
    "user": "security",
    "password": os.environ.get('SECURITY_PG_PASSWORD'),
    "host": os.environ.get('SECURITY_PG_HOST'),
    "port": 5432
}
class PostgreSQLManager:
    def __init__(self, dbname: str, user: str, password: str, host: str = 'localhost', port: int = 5432):
        """
        Initialize PostgreSQL database connection
        
        :param dbname: Database name
        :param user: Username
        :param password: Password
        :param host: Host address (default 'localhost')
        :param port: Port number (default 5432)
        """
        self.dbname = dbname
        self.user = user
        self.password = password
        self.host = host
        self.port = port
        self.connection = None
        self.connect()

    def connect(self) -> bool:
        """
        Connect to PostgreSQL database

        :return: Whether connection was successful
        """
        try:
            self.connection = psycopg2.connect(
                dbname=self.dbname,
                user=self.user,
                password=self.password,
                host=self.host,
                port=self.port,
                cursor_factory=DictCursor,
                connect_timeout=60
            )
            return True
        except psycopg2.Error as e:
            logging.error(f"Failed to connect to PostgreSQL database: {e}")
            return False

    def disconnect(self):
        """
        Disconnect from the database
        """
        if self.connection:
            self.connection.close()
            self.connection = None

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        Execute a query and return results

        :param query: SQL query string
        :param params: Query parameters
        :return: List of query results
        """
        results = []
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                if cursor.description:  # If there are results
                    columns = [desc[0] for desc in cursor.description]
                    results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                self.connection.commit()
        except psycopg2.Error as e:
            self.connection.rollback()
            logging.error(f"Query execution failed: {e}")
        return results

    def insert(self, table: str, data: Dict[str, Any]) -> Optional[int]:
        """
        Insert a record into a table

        :param table: Table name
        :param data: Data dictionary to insert {column: value}
        :return: ID of inserted row (if any), otherwise None
        """
        columns = data.keys()
        values = [data[column] for column in columns]

        query = sql.SQL("INSERT INTO {} ({}) VALUES ({}) RETURNING id").format(
            sql.Identifier(table),
            sql.SQL(', ').join(map(sql.Identifier, columns)),
            sql.SQL(', ').join(sql.Placeholder() * len(columns))
        )

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, values)
                result = cursor.fetchone()
                self.connection.commit()
                return result[0] if result else None
        except psycopg2.Error as e:
            self.connection.rollback()
            logging.error(f"Data insertion failed: {e}")
            return None

    def update(self, table: str, data: Dict[str, Any], condition: str, condition_params: Optional[tuple] = None) -> int:
        """
        Update records in a table

        :param table: Table name
        :param data: Dictionary of data to update {column: new_value}
        :param condition: WHERE condition clause
        :param condition_params: WHERE condition parameters
        :return: Number of affected rows
        """
        set_clause = sql.SQL(', ').join(
            [sql.SQL("{} = {}").format(sql.Identifier(k), sql.Placeholder()) for k in data.keys()]
        )

        query = sql.SQL("UPDATE {} SET {} WHERE {}").format(
            sql.Identifier(table),
            set_clause,
            sql.SQL(condition)
        )

        # Combine parameters: update values + condition parameters
        params = tuple(data.values()) + (condition_params if condition_params else ())

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                row_count = cursor.rowcount
                self.connection.commit()
                return row_count
        except psycopg2.Error as e:
            self.connection.rollback()
            logging.error(f"Data update failed: {e}")
            return 0

    def select_where(self, table: str, columns: Optional[List[str]] = None, 
                condition: Optional[str] = None, params: Optional[Tuple] = None,
                return_format: str = "dict") -> Union[List[Dict[str, Any]], List[List[Any]]]:
        """
        Execute a SELECT query with conditions and return results

        :param table: Table name
        :param columns: List of column names to query (None means all columns)
        :param condition: WHERE condition clause (without WHERE keyword)
        :param params: Condition parameters (optional)
        :param return_format: Return format, either "dict" or "list"
        :return: Query result list, format depends on return_format parameter
                "dict" - List[Dict[str, Any]] (default)
                "list" - List[List[Any]]
        """
        # Build SELECT part
        if columns:
            select_part = sql.SQL(', ').join(map(sql.Identifier, columns))
        else:
            select_part = sql.SQL('*')
        
        # Build base query
        query = sql.SQL("SELECT {} FROM {}").format(
            select_part,
            sql.Identifier(table)
        )

        # Add WHERE condition
        if condition:
            query = sql.SQL("{} WHERE {}").format(query, sql.SQL(condition))

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                if cursor.description:  # If there are results
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    
                    if return_format == "list":
                        # Return List[List] format
                        return [list(row) for row in rows]
                    else:
                        # Default return List[Dict] format
                        return [dict(zip(columns, row)) for row in rows]
                return []
        except psycopg2.Error as e:
            self.connection.rollback()
            logging.error(f"Conditional query execution failed: {e}")
            return []

db = PostgreSQLManager(**db_config)

def get_next_month_date():
    """Get the date for the same day next month as a string."""
    current_date = datetime.now()
    next_month_date = current_date + relativedelta(months=1)
    return next_month_date.strftime('%Y-%m-%d')

def get_atlassian_session():
    """Get an authenticated session for Atlassian."""
    session = requests.session()
    session.auth = (os.environ.get('JIRA_USERNAME') , os.environ.get('JIRA_API_TOKEN'))
    return session

def get_account_id_by_name(user_name):
    """Get Atlassian account ID by username."""
    session = get_atlassian_session()
    response = session.get(f'{ATLASSIAN_API_URL}/user/search?query={user_name}').json()
    if response:
       return response[0]['accountId']
    return None

def download_file(url):
    """Download the file content from the given URL."""
    try:
        response = requests.get(url, verify=False)
        response.raise_for_status()
        return response.text
    except requests.exceptions.RequestException as error:
        logging.error(f"Download file error: {error}")
        return None

def is_within_days(time_str, days):
    """Check if the given time string is within the last 'days' days."""
    try:
        timestamp = datetime.fromisoformat(time_str[:-1])
    except ValueError as e:
        timestamp = datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S.%fZ')
    now = datetime.now()
    one_month_ago = now - timedelta(days=days)
    return one_month_ago <= timestamp <= now

def get_exist_issues_num(results):
    count = 0
    for result in results:
        is_fix = result[-1]
        if not is_fix:
            count += 1
    return count

def create_or_update_jira_ticket_issue(project_key, results, version, severity, ticket_key=None):
    """Create Jira ticket for provided project and results."""
    session = get_atlassian_session()
    title = f'[Security] ({severity}) Security scan result for TG version {version} - {datetime.now().strftime("%Y-%m-%d")}'
    labels = ["snyk_issue", f"security_{version}", f"tg_{version}", f"{severity}_severity"]

    for result in results:
        is_fix = result[-1]
        publication_time = result[-2]
        if (not is_within_days(publication_time, 30) and severity == 'high') or (not is_within_days(publication_time, 90) and severity == 'medium'):
            if not is_fix:
                labels.append("Release_Stopper")
                break

    table_content = [generate_table_headers(["Path", "Severity", "Module", "Rule Link", "Publication Time"])]

    for result in results:
        is_fix = result[-1]
        table_content.append(generate_table_row(result[1:6], is_fix))

    payload = {
        "fields": {
            "description": generate_description_content(table_content, version, get_exist_issues_num(results), project_key),
            "issuetype": {"id": "1"},
            "labels": labels,
            "priority": {"id": "2"},
            "project": {"id": PROJECT_USER_MAPPING[project_key]['project_id']},
            "reporter": {"id": get_account_id_by_name(PROJECT_USER_MAPPING[project_key]['reporter'])},
            "duedate": get_next_month_date(),
            "components": [{"id": PROJECT_USER_MAPPING[project_key]['component_id']}],
        }
    }

    if ticket_key is None:
        payload['fields']['summary'] = title
        ticket_key = create_new_ticket(session, payload, version, project_key, results)
    else:
        ticket = get_ticket_by_key(ticket_key)
        update_existing_ticket(session, ticket, payload, project_key, version, results)
    return ticket_key

def fetch_existing_ticket(version, severity, project_key):
    """Fetch existing JIRA tickets related to the given version and project key."""
    tickets = get_ticket_key_by_version_and_project(version, severity, project_key)
    if tickets:
        return tickets[-1]
    return None

def generate_csv_file(project_key, severity, results, version):
    headers = ['title', 'path', 'severity', 'module', 'rule_link', 'publication_time', 'is_fix']
    file_name = f'{project_key}_{severity}_{version}.csv'
    save_results = copy.deepcopy(results)
    with open(file_name, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)
        for result in save_results:
            if len(result) == 8:
                del(result[5])
            writer.writerow(result[:7])
    logging.info(f'CSV file {file_name} created for {project_key} version {version} with severity {severity}')
    return file_name

def add_attachment_to_jira(session, ticket_key, file_path):
    response = session.get(f'{ATLASSIAN_API_URL}/issue/{ticket_key}?fields=attachment')
    response.raise_for_status()
    attachments = response.json().get('fields', {}).get('attachment', [])
    for attachment in attachments:
        try:
            del_response = session.delete(f'{ATLASSIAN_API_URL}/attachment/{attachment["id"]}')
            del_response.raise_for_status()
            logging.info(f'Deleted attachment: {attachment["filename"]}')
        except RequestException as e:
            logging.warning(f'Failed to delete attachment {attachment["id"]}: {str(e)}')
    logging.info(f'Adding attachment to JIRA ticket {ticket_key}')
    response = session.post(f'{ATLASSIAN_API_URL}/issue/{ticket_key}/attachments', headers={'Accept': 'application/json', "X-Atlassian-Token": "no-check"}, files={'file': (file_path, open(file_path,"rb"), "application-type")})
    logging.info(response.text)

def update_existing_ticket(session, ticket, payload, project_key, version, results):
    """Handle updating an existing JIRA ticket."""
    ticket_key = ticket['key']
    ticket_status = ticket['fields']['status']['name']
    logging.info(f'Found {ticket_key} for version {version} in {project_key}')

    # Update JIRA ticket
    response = session.put(f'{ATLASSIAN_API_URL}/issue/{ticket_key}', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
    logging.info(response.text)
    if response.text:
        if response.json().get('errorMessages') and response.json().get('errorMessages')[0] == 'CONTENT_LIMIT_EXCEEDED':
            logging.info(f'The content of the issue is too large, skip description')
            payload['fields']['description'] = generate_description_content([{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Please see attached CSV for all issues"}]}]}]}], version, get_exist_issues_num(results), project_key)
            response = session.put(f'{ATLASSIAN_API_URL}/issue/{ticket_key}', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})

    # update attachment to JIRA ticket
    add_attachment_to_jira(session, ticket_key, generate_csv_file(project_key, severity, results, version))

    # If the ticket is resolved or closed, reopen it
    if ticket_status in ['Resolved', 'Closed']:
        transitions_url = f'{ATLASSIAN_API_URL}/issue/{ticket_key}/transitions'
        transitions_response = session.get(transitions_url, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
        transitions = transitions_response.json().get('transitions', [])
        for transition in transitions:
            if transition['name'] == 'Incomplete Fix':
                payload = {'transition': {'id': transition['id']}}
                response = session.post(f'{ATLASSIAN_API_URL}/issue/{ticket_key}/transitions', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
                logging.info(response.text)
                break

def create_issue_link(ticket_key, qa_key):
    session = get_atlassian_session()
    """Create a link between two JIRA issues."""
    payload = {
        "type": {"name": "parent"},
        "inwardIssue": {"key": ticket_key},
        "outwardIssue": {"key": qa_key}
    }
    response = session.post(f'{ATLASSIAN_API_URL}/issueLink', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
    logging.info(response.text)

def insert_results_to_db(ticket_key, results, version):
    """Insert results into the database."""
    for result in results:
        db.insert('security', {
            'title': result[0],
            'path': result[1],
            'severity': result[2],
            'module': result[3],
            'cve': result[5],
            'rule_link': result[4],
            'publication_time': result[6],
            'ticket_key': ticket_key,
            'version': version
        })
    logging.info(f'Inserted results into DB for ticket {ticket_key} and version {version}')

def create_new_ticket(session, payload, version, project_key, results):
    """Handle creating new JIRA ticket."""
    payload['fields']['assignee'] = {"id": get_account_id_by_name(PROJECT_USER_MAPPING[project_key]['assignee'])}
    response = session.post(f'{ATLASSIAN_API_URL}/issue', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
    logging.info(response.text)
    if response.text:
        if response.json().get('errorMessages') and response.json().get('errorMessages')[0] == 'CONTENT_LIMIT_EXCEEDED':
            logging.info(f'The content of the issue is too large, skip description')
            payload['fields']['description'] = generate_description_content([{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Please see attached CSV for all security issues"}]}]}]}], version, get_exist_issues_num(results), project_key)
            response = session.post(f'{ATLASSIAN_API_URL}/issue', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
            logging.info(response.text)
    ticket_key = response.json().get('key')
    if not ticket_key:
        logging.error("Failed to create ticket.")
    else:
        logging.info(f'Created new ticket {ticket_key} for version {version} in {project_key}')
        insert_results_to_db(ticket_key, results, version)
        add_attachment_to_jira(session, ticket_key, generate_csv_file(project_key, severity, results, version))

    return ticket_key

def is_valid_iso_datetime(time_str):
    pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$'
    return bool(re.fullmatch(pattern, time_str))

def generate_table_headers(headers):
    """Generate standard table headers for JIRA ticket."""
    return {"type": "tableRow", "content": [json.loads(CELL_TEMPLATE % header) for header in headers]}

def generate_table_row(data, is_fix):
    """Generate a table row from given data."""
    row = []
    for field in data:
        if is_valid_iso_datetime(field):
            field = parser.isoparse(field).strftime('%Y-%m-%d')
        if not field.startswith('http'):
            try:
                cell_content = json.loads(CELL_TEMPLATE % field) if not is_fix else json.loads(CELL_STRIKE_TEMPLATE % field)
            except Exception as e:
                cell_content = json.loads(CELL_TEMPLATE % json.loads(field)) if not is_fix else json.loads(CELL_STRIKE_TEMPLATE % json.loads(field))
        else:
            cell_content = json.loads(CELL_LINK_TEMPLATE % (field.split('/')[-1], field.lower().replace('nvd.nist.gov/vuln/detail', 'avd.aquasec.com/nvd'))) if not is_fix else json.loads(CELL_STRIKE_TEMPLATE % field)
        row.append(cell_content)
    return {"type": "tableRow", "content": row}

def generate_description_content(table_content, version, issues_count, project_key):
    """Generate formatted description content for JIRA ticket."""
    return {
        "content": [
            {
                "type": "heading",
                "attrs": {"level": 4},
                "content": [{"type": "text", "text": f"Found {issues_count} security issues for TG version {version} in {project_key}. Click the Rule Link to see how to fix issues. (Must fix Critical/High severity security issues older than 30 days, Medium severity security issues older than 90 days)"}]
            },
            {
                "type": "heading",
                "attrs": {"level": 3},
                "content": [{"type": "text", "text": "Snyk Report Link", "marks": [{"type": "link", "attrs": {"href": f"https://app.snyk.io/org/tigergraph-product-3.9.2/reporting?context[page]=issues-detail&project_target=tigergraph%252F{version}&project_origin=cli&target_ref=[%22docker-image%257Ctigergraph%252F{version}%22]&v=1"}}]}]
            },
            {
                "type": "heading",
                "attrs": {"level": 3},
                "content": [{"type": "text", "text": "Trivy Report Link", "marks": [{"type": "link", "attrs": {"href": f"https://ftp.graphtiger.com/security_scan/trivy_scan_{version}_latest.txt"}}]}]
            },
            {"type": "table", "attrs": {"isNumberColumnEnabled": False, "layout": "center", "width": 1080, "displayMode": "default"}, "content": table_content}
        ],
        "type": "doc",
        "version": 1
    }

def get_ticket_key_by_version_and_project(version, severity, project_key=None):
    """Fetch JIRA ticket keys by version and project key."""
    session = get_atlassian_session()
    base_jql = f'labels = "tg_{version}" AND labels = "security_{version}" AND summary ~ "({severity}) Security scan result for TG version {version}"'
    jql = f'project = {project_key} AND {base_jql}' if project_key else base_jql
    url = f'{ATLASSIAN_API_URL}/search?jql={jql}&fields=key,status'
    response = session.get(url).json()
    if 'issues' in response.keys():
        return response['issues']
    return None

def get_ticket_by_key(ticket_key):
    """Fetch JIRA ticket details by ticket key."""
    session = get_atlassian_session()
    url = f'{ATLASSIAN_API_URL}/issue/{ticket_key}'
    response = session.get(url).json()
    if 'key' in response.keys():
        return response
    return None

def parse_datetime(date_str):
    try:
        return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S.%fZ')
    except ValueError:
        return datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ')

def parse_trivy_report(result, serverity):
    new_results = list()
    for r in result['Results']:
        if r['Class'] in ['lang-pkgs', 'os-pkgs']:
            target = r['Target']
            if 'Vulnerabilities' in r.keys():
                issues = r['Vulnerabilities']
                for issue in issues:
                    severity = issue['Severity']
                    if severity.lower() in SEVERITY_MAPPING[serverity]:
                        cve_list = issue['CweIDs'] if 'CweIDs' in issue.keys() else issue['VulnerabilityID']
                        title = issue['Title'] if 'Title' in issue.keys() else issue['PkgID']
                        module_name = issue['PkgName']
                        target_file = issue['PkgPath'] if 'PkgPath' in issue.keys() else target
                        if 'ubuntu' in target:
                            target_file = issue['PkgIdentifier']['PURL']
                        publication_time = issue['PublishedDate'] if 'PublishedDate' in issue.keys() else ''
                        rule_link = f'https://avd.aquasec.com/nvd/{issue["VulnerabilityID"].lower()}'
                        new_results.append([title, target_file, severity.lower(), module_name, rule_link, json.dumps(cve_list), publication_time, False])
    return sorted(new_results, key=lambda x: parse_datetime(x[-2]))

def parse_snyk_report(result, serverity):
    """Parse Snyk report and extract necessary data."""
    new_results = []
    for app in result['applications']:
        if not app['vulnerabilities']:
            continue
        for issue in app['vulnerabilities']:
            if issue['severity'] in SEVERITY_MAPPING[serverity]:
                new_results.append([
                    issue['title'], app['targetFile'], issue['severity'].lower(), 
                    issue['moduleName'] if 'moduleName' in issue else issue['packageName'], f'https://security.snyk.io/vuln/{issue["id"]}',
                    json.dumps(issue['identifiers']) if 'identifiers' in issue else '', issue['publicationTime'], False
                ])
    return sorted(new_results, key=lambda x: parse_datetime(x[-2]))

def update_unresolved_tickets_to_resolved(ticket, version, severity):
    """Update unresolved JIRA tickets to resolved."""
    ticket_key = ticket['key']
    session = get_atlassian_session()
    if ticket['fields']['status']['name'] not in ['Resolved', 'Closed']:
        transitions_url = f'{ATLASSIAN_API_URL}/issue/{ticket_key}/transitions'
        transitions_response = session.get(transitions_url, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
        transitions = transitions_response.json().get('transitions', [])
        for transition in transitions:
            if transition['name'] in ['Verified', 'Won\'t Do']:
                logging.info(f'Updating ticket {ticket_key} to Resolved')
                issues = get_tickets_issues(ticket_key, version)
                create_or_update_jira_ticket_issue(ticket_key.split('-')[0], issues, version, severity, ticket_key)
                payload = {'transition': {'id': transition['id']}}
                response = session.post(f'{ATLASSIAN_API_URL}/issue/{ticket_key}/transitions', json=payload, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
                logging.info(response.text)
                response = session.post(f'https://graphsql.atlassian.net/rest/api/2/issue/{ticket_key}/comment', json={"body": "All issues have been fixed."}, headers={'Content-Type': 'application/json', 'Accept': 'application/json'})
                logging.info(response.text)
                break

def check_all_fixed_by_ticket(version, severity):
    """
    Check if all records under each ticket_key are marked as fixed
    Returns: List containing ticket_key and whether all are fixed status
    """
    query = """
        SELECT 
            ticket_key,
            bool_and(is_fix) AS all_fixed
        FROM 
            public.security
        WHERE 
            ticket_key IS NOT NULL and version = '%s' and severity = ANY(ARRAY%s)
        GROUP BY 
            ticket_key
        ORDER BY 
            ticket_key;
    """
    return db.execute_query(query % (version, SEVERITY_MAPPING[severity]))

def get_tickets_issues(ticket_key, version):
    """
    Get issues related to a specific ticket key and version.
    """
    return db.select_where("security", ["title", "path", "severity", "module", "rule_link", "publication_time", "is_fix"], "ticket_key = '%s' and version = '%s'" % (ticket_key, version), return_format="list")

def determine_project_key(file_name, module):
    """Determine project key based on file name."""
    for project, paths in PATH_PROJECT_MAPPING.items():
        for path in paths:
            if path in file_name:
                if project == 'CORE' and module in ['stdlib', 'github.com/golang-jwt/jwt/v4']:
                    return "TP"
                return project
    return "QA"

def update_exist_tickets(version, severity):
    """Update existing tickets based on their status and whether all issues are fixed."""
    ticket_dict = check_all_fixed_by_ticket(version, severity)
    logging.info(ticket_dict)
    for t in ticket_dict:
        ticket_key = t['ticket_key']
        if t['all_fixed'] == True:
            ticket = get_ticket_by_key(ticket_key)
            if ticket:
                update_unresolved_tickets_to_resolved(ticket, version, severity)
        else:
            project_key = ticket_key.split('-')[0]
            issues = get_tickets_issues(ticket_key, version)
            create_or_update_jira_ticket_issue(project_key, issues, version, severity, ticket_key)

def is_tmp_gsqld_path(path):
    """
    Check if the path contains the pattern '.tmp_tg_dbs_gsqld' followed by a hash value

    Args:
        path (str): The path string to check

    Returns:
        bool: True if pattern is found, False otherwise
    """
    # Regex pattern to match '.tmp_tg_dbs_gsqld' followed by 32 hex characters
    pattern = r'\.tmp_tg_dbs_gsqld_[0-9a-f]{32}'
    return bool(re.search(pattern, path))

def main(version, severity):
    """Main function to process Snyk report and create/update JIRA tickets."""
    snyk_report_url = f'https://ftp.graphtiger.com/security_scan/snyk_scan_{version}_latest.json'
    trivy_report_url = f'https://ftp.graphtiger.com/security_scan/trivy_scan_{version}_latest.json'
    snyk_report = json.loads(download_file(snyk_report_url))
    trivy_report = json.loads(download_file(trivy_report_url))
    trivy_result = parse_trivy_report(trivy_report, severity)
    snyk_results = parse_snyk_report(snyk_report, severity)
    snyk_results.extend(trivy_result)
    project_results_mapping = {}
    for result in snyk_results:
        project_key = determine_project_key(result[1], result[3])
        if project_key not in project_results_mapping:
            project_results_mapping[project_key] = []
        project_results_mapping[project_key].append(result)

    # Check if the parent ticket already exists
    new_version = False
    qa_ticket = fetch_existing_ticket(version, severity, 'QA')
    if not qa_ticket:
        # Create a new ticket for QA
        qa_ticket_key = create_or_update_jira_ticket_issue('QA', snyk_results, version, severity)
        new_version = True
    else:
        qa_ticket_key = qa_ticket['key']
    if not qa_ticket_key:
        logging.error("Failed to create or fetch QA ticket.")
        return
    all_issues = db.select_where("security", ["id"], "version = '%s' and is_fix = false and severity = ANY(ARRAY%s)" % (version, SEVERITY_MAPPING[severity]))
    unresolve_issues = list()
    for project_key, results in project_results_mapping.items():
        new_issues = list()
        for result in results:
            path = result[1]
            module = result[3]
            cve = result[5]
            if new_version:
                condition = "version = '%s' and module = '%s' and path = '%s' and cve = '%s' and is_fix = false and ticket_key not like '%s' and severity = ANY(ARRAY%s)" % (version, module, path, cve, 'QA-%', SEVERITY_MAPPING[severity])
            else:
                condition = "version = '%s' and module = '%s' and cve = '%s' and is_fix = false and severity = ANY(ARRAY%s)" % (version, module, cve, SEVERITY_MAPPING[severity])
                if is_tmp_gsqld_path(path):
                    condition += " and path like '%%.tmp_tg_dbs_gsqld\_%%'"
                else:
                    condition += " and path = '%s'" % path
            exist_issues = db.select_where("security", ["id"], condition)
            if len(exist_issues) == 0:
                new_issues.append(result)
            if new_version:
                exist_issues = db.select_where("security", ["id"], "version = '%s' and module = '%s' and path = '%s' and cve = '%s' and is_fix = false and severity = ANY(ARRAY%s)" % (version, module, path, cve, SEVERITY_MAPPING[severity]))
            unresolve_issues.extend(exist_issues)
        if new_issues:
            if project_key != 'QA':
                issue_key = create_or_update_jira_ticket_issue(project_key, new_issues, version, severity)
                create_issue_link(qa_ticket_key, issue_key)
            if not new_version:
                insert_results_to_db(qa_ticket_key, new_issues, version)

    # if a issue in all_issues, but not in unresolve_issues, update is_fixed to true
    for issue in all_issues:
        if issue not in unresolve_issues:
            logging.info("Update issue %s to fixed" % issue['id'])
            db.update("security", {"is_fix": True}, "id = %s" % issue['id'])

    update_exist_tickets(version, severity)


if __name__ == '__main__':
    args = sys.argv
    version = args[1]
    severity = args[2]
    main(version, severity)
    db.disconnect()
