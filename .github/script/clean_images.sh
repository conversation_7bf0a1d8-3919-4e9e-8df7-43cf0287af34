#!/bin/bash

# Uncomment if you want to run it locally
# rm -rf tg_images.txt  # Remove the tg_images.txt file if it exists
# tsh kube ls --format json | jq -r '.[].kube_cluster_name' | while read cluster_name; do
#     if [[ $cluster_name == tg-* ]]; then  # If the cluster name starts with 'tg-', proceed
#         echo "Logging into cluster: $cluster_name"
#         tsh kube login $cluster_name --as teleport-eng-user  # Login to the cluster
#         # Get all tg CR (Custom Resource) information
#         kubectl get tg -A -o=jsonpath='{.items[*].spec.sidecarContainers[?(@.name=="tgagent")].image}' | tr ' ' '\n' | while read image; do
#             echo $image >> tg_images.txt  # Append the image name to tg_images.txt
#         done
#         ca_image=$(kubectl get deployment cluster-agent-clusteragent -o=jsonpath='{.spec.template.spec.containers[0].image}')
#         echo $ca_image >> tg_images.txt
#         autostart_image=$(kubectl get deployment autostart-handler-autostarthandler -o=jsonpath='{.spec.template.spec.containers[0].image}')
#         echo $autostart_image >> tg_images.txt
#     else
#         echo "Cluster name $cluster_name does not start with 'tg', continuing"
#         continue  # Skip if the cluster name doesn't start with 'tg-'
#     fi
# done

declare -a used_tg_images_array  # Declare an array to store the used tg images

# Read the tg_images.txt file and store each line's content in the array
while IFS= read -r image; do
    used_tg_images_array+=("$image")
done < tg_images.txt

# Get the date for one month ago
if [[ "$(uname)" == "Darwin" ]]; then
    one_month_ago=$(date -u -v-1m +"%Y-%m-%dT%H:%M:%SZ")
    one_month_ago_timestamp=$(date -u -j -f "%Y-%m-%dT%H:%M:%SZ" "$one_month_ago" +"%s")
else
    one_month_ago=$(date -u --date="1 month ago" +"%Y-%m-%dT%H:%M:%SZ")
    one_month_ago_timestamp=$(date -d "$one_month_ago" +"%s")
fi

# Set the target Docker Hub repository
repo_list=("tginternal/cloud-agent" "tginternal/cloud-cluster-agent" "tginternal/cloud-auto-start-handler")

for repo in "${repo_list[@]}"; do
    echo "Checking repository: $repo"
    # Get all image tags from the specified Docker Hub repository
    hub-tool tag ls --all "$repo" --format=json | jq -r '.[] | "\(.Name) \(.LastUpdated)"' | while read -r tag lastUpdated; do
    lastUpdated_cleaned=$(echo "$lastUpdated" | sed 's/\.[0-9]*Z$/Z/')  # Clean up the timestamp format

    if [[ "$(uname)" == "Darwin" ]]; then
        lastUpdated_timestamp=$(date -u -j -f "%Y-%m-%dT%H:%M:%SZ" "$lastUpdated_cleaned" +"%s")  # Convert the cleaned timestamp to Unix timestamp
    else
        lastUpdated_timestamp=$(date -d "$lastUpdated_cleaned" +"%s")  # Convert the cleaned timestamp to Unix timestamp
    fi

    if [[ "$lastUpdated_timestamp" -lt "$one_month_ago_timestamp" ]]; then  # If the tag is older than one month
        if echo "${used_tg_images_array[@]}" | grep -qw "$tag"; then  # Check if the tag is in the array
            echo "$tag is in use. Skipping deletion."
        else
            echo "Deleting tag: $tag, LAST UPDATE: $lastUpdated"
            hub-tool tag rm $tag -f  # Uncomment this line to actually delete the tag
        fi
    else
        echo "Tag $tag is not older than one month. Skipping deletion. LAST UPDATE: $lastUpdated"
    fi
    done
done

echo "Script completed."
