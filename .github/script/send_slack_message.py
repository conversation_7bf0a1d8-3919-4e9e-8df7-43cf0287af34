import requests
import json
import os
from slack_sdk import WebClient

# Define your Slack bot token
slack_bot_token = os.environ['SLACK_BOT_TOKEN']

# Define the user ID to send the message to
client = WebClient(token=os.environ['SLACK_BOT_TOKEN'])
user = os.environ['SLACK_USER']
branch = os.environ['branch']
github_run_id = os.environ['github_run_id']
github_actor = os.environ['github_actor']
response = client.users_lookupByEmail(email=user.strip())
user_id = response['user']['id']


slack_message = {
    "channel": user_id,
    "blocks": [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": "Hey, we have a new controller build report!",
                "emoji": True
            }
        },
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*Branch:*\n{ branch }"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*Actor:*\n{ github_actor }"
                },
                {
                    "type": "mrkdwn",
                    "text": f"*SHA(modHeader):*\n x-test-env: cloud-universe#dev-{ branch }"
                },
            ]
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Github PR link:* <https://github.com/tigergraph/cloud-universe/actions/runs/{ github_run_id }|View Github Action>"
            }
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*You can enter controller container by teleport:* <https://tigergraph.teleport.sh/web/cluster/tigergraph.teleport.sh/resources?search=cicd-controller-dev-{ branch }|View Teleport>"
            }
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*You can check controller monitoring by grafana:* <https://grafana-internal.tgcloud-dev.com/d/FDB061FMz/tgcloud-controller-metrics?orgId=1&refresh=30s&var-envtag=service:service-{ branch }|View Metrics>"
            }
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*You can check controller logs by kibana:* <https://kibana-internal.tgcloud-dev.com/app/discover#/?_a=(filters:!((query:(match_phrase:(__branchName__:{ branch })))))|View Logs>"
            }
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*How to customize the environment configuration:* <https://graphsql.atlassian.net/wiki/x/dIBWwg>"
            }
        }
    ]
}
# Convert the message to a JSON string
slack_message_json = json.dumps(slack_message)

# Define the headers for the request
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {slack_bot_token}'
}

# Send the message to Slack
response = requests.post(
    'https://slack.com/api/chat.postMessage',
    data=slack_message_json,
    headers=headers
)

# Check the response
if response.status_code != 200:
    raise ValueError(
        f'Request to slack returned an error {response.status_code}, '
        f'the response is:\n{response.text}'
    )
