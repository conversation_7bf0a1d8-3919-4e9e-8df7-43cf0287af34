name: Parse Snyk Result
on:
  workflow_dispatch:
  schedule:
    - cron: "0 2 * * *" #UTC

permissions:
  pull-requests: write
  contents: write
  checks: write
  issues: read
  id-token: write

jobs:
  parse-snyk-result:
    name: Parse Snyk Result
    runs-on: macos-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
        cache: 'pip'
    - run: |
        pip install -r .github/script/requirements.txt
        pip install psycopg2-binary

    - name: Install Brew And Connect RWC VPN
      run: |
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        brew install macosvpn
        sudo macosvpn create -l RWC -e ************** -u chengrun.peng -p ${{ secrets.RWC_PASSWORD }} -s itravelledaroundtheworldin1day
        networksetup -connectpppoeservice "RWC"
        sleep 10

    - name: <PERSON><PERSON>an Report And Create Jira Ticket
      env:
        SNYK_CC_USER: ${{ vars.SNYK_CC_USER }}
        PATH_PROJECT_MAPPING: ${{ vars.PATH_PROJECT_MAPPING }}
        PROJECT_USER_MAPPING: ${{ vars.PROJECT_USER_MAPPING }}
        JIRA_USERNAME: ${{ vars.JIRA_USERNAME }}
        JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
        SECURITY_PG_HOST: ${{ vars.SECURITY_PG_HOST }}
        SECURITY_PG_PASSWORD: ${{ secrets.SECURITY_PG_PASSWORD }}
      run: |
        cd .github/script
        echo "${{ vars.SNYK_SCAN_VERSIONS }}" | tr ',' '\n' | while read -r version; do
          python3 snyk_parser.py ${version} high
          python3 snyk_parser.py ${version} medium
        done
