name: Run end-to-end test

on:
  workflow_call:
    inputs:
      header:
        description: 'header value to route the test traffic'
        type: string
        required: false
  workflow_dispatch:
    inputs:
      header:
        description: 'header value to route the test traffic'
        type: string
        required: false

permissions:
  id-token: write
  contents: read
  issues: read
  checks: write
  pull-requests: write

env:
  AWS_S3_BUCKET: tgcloud-test-v4
  AWS_REGION: "us-west-1"

jobs:
  run-api-test:
    environment: dev-new
    name: Run API test
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v4
    - name: Run API test
      env: 
        HEADER: ${{ inputs.header }}
      run: |
        echo HEADER: ${{ env.HEADER }}
        if [ -n "${HEADER}" ];then
          bash tests/run_cloud_api.sh smokerun "${HEADER}"
        else
          bash tests/run_cloud_api.sh smokerun
        fi

    - name: configure aws credentials
      if: always()
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Generate API Allure report and upload it to S3
      if: always()
      run: |
        wget --no-verbose -O /tmp/allure-commandline.zip https://repo.maven.apache.org/maven2/io/qameta/allure/allure-commandline/2.19.0/allure-commandline-2.19.0.zip && unzip /tmp/allure-commandline.zip
        mkdir tests/allure-report
        allure-2.19.0/bin/allure generate tests/alluredir/cloud_api -o tests/allure-report
        aws s3 sync tests/allure-report s3://${{ env.AWS_S3_BUCKET }}/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api --follow-symlinks --delete
        echo "### API Test Report URL:" >> $GITHUB_STEP_SUMMARY
        echo "http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        cp -r /tmp/e2e/log tests/ 2>/dev/null || :
        cp -r /tmp/tools_e2e tests/log/ 2>/dev/null || :
        if [ -n "$(find tests/junit -maxdepth 1 -name 'test-results*.xml' 2>/dev/null)" ]; then
            echo "Files matching 'test-results*.xml' found in tests/junit directory."
            ls -l tests/junit/
        else
            echo "No files matching 'test-results*.xml' found in tests/log directory."
            mkdir -p tests/junit
            touch tests/junit/test-results.xml
        fi

    - name: Comment in pull request
      if: always()
      uses: thollander/actions-comment-pull-request@v2
      with:
        message: |
          ### API Test Report URL(visit in VPN):
          http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html

    - name: Upload API test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: cloud-test-api
        path: |
          tests/environment.properties
          tests/prepare_env_log*
          tests/junit/test-results*.xml
          tests/latest_logs/
          tests/alluredir/
          tests/data/log/*.log
          tests/log/
