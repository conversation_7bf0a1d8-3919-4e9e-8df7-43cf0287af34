name: On Release

env:
  AWS_REGION: us-east-1
  SHA: ${{ github.sha }}
  ECR_REPOSITORY: tgcloudv4
  ECS_SERVICE: tgcloud_prod
  ECS_AGW_SERVICE: agent_gateway_prod
  ECS_CLUSTER: TGCloud-prod
  ECS_TASK_DEFINITION: tgcloud_prod
  ECS_AGW_DEFINITION: agent_gateway_prod
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

# Permission can be added at job level or workflow level
permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout
  issues: write     # This is required for manual-approval

on:
  release:
    types: released

jobs:
  manual-approval:
    name: Approval Release
    runs-on: ubuntu-latest
    steps:
      - name: Manual Approval for Release
        uses: trstringer/manual-approval@v1
        with:
          issue-title: "[Prod] Deploying (sha: ${{ github.sha }}, tag: ${{ github.event.release.tag_name }})"
          minimum-approvals: 1
          secret: ${{ secrets.github_token }}
          approvers: ${{ vars.DEPLOYMENT_APPROVERS }}
  
  release:
    name: Release
    environment: production
    needs: manual-approval
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Download task definition
        run: |
            image=${{ vars.ECR_REGISTRY }}/$ECR_REPOSITORY:${SHA::6}
            echo "image=${image}" >> $GITHUB_ENV
            echo "sha=${SHA::6}" >> $GITHUB_ENV
            aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEFINITION }} --query taskDefinition > task-definition.json
            agw_image=${{ vars.ECR_REGISTRY }}/cloud-agent-gateway:${SHA::6}
            echo "agw_image=${agw_image}" >> $GITHUB_ENV
            aws ecs describe-task-definition --task-definition ${{ env.ECS_AGW_DEFINITION }} --query taskDefinition > agw-task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: controller
          image: ${{ env.image }}
          environment-variables: |
            ENV=prod
            SHA=${{ env.sha }}

      - name: Deploy Amazon ECS task definition for Production
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Fill in the AGW image ID in the Amazon ECS task definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        id: agw-task-def
        with:
          task-definition: agw-task-definition.json
          container-name: agw
          image: ${{ env.agw_image }}

      - name: Deploy Amazon AGW ECS task definition for Production
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.agw-task-def.outputs.task-definition }}
          service: ${{ env.ECS_AGW_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

  verify:
    name: Verify Production Environment
    needs: release
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Run test in Staging Environment
        run: echo "Run test in Staging Environment, TBD"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Override the latest tag of the controller image
        env:
          ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
        run: |
          docker pull $ECR_REGISTRY/$ECR_REPOSITORY:${SHA::6}
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:${SHA::6} $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [verify]
    if: always()
    steps:
      - if: needs.verify.result == 'success'
        run: |
          echo "publish_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.verify.result == 'skipped' || needs.verify.result == 'failure'
        run: |
          echo "publish_status=:red_circle:" >> $GITHUB_ENV

      - name: Slack Message
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "<${{ github.event.release.tag_name }}> On-Released Result ${{ env.publish_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Release Version:*\n<${{ github.event.release.html_url }}|${{github.event.release.tag_name}}>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ github.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "plain_text",
                  "text": "Auto Verify status: ${{ env.publish_status }}, Report TBD",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Deploy Environment:* <https://portal.tgcloud.io|View Environment>"
                  },
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.CLOUD_DEPLOY_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )