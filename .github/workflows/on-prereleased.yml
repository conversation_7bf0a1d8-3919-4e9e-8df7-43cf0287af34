name: On Pre-Released

env:
  AWS_REGION: us-east-1
  SHA: ${{ github.sha }}
  ECR_REPOSITORY: tgcloudv4
  ECS_STAGING_SERVICE: tgcloud_staging_prod
  ECS_CLUSTER: TGCloud-prod
  ECS_TASK_STAGING_DEFINITION: tgcloud_staging_prod
  CLOUD_FORMATION_BUCKET: tgcloud-cloudformation-prod
  TERRAFORM_BUCKET: tgcloud-provider-terraform-prod
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

# Permission can be added at job level or workflow level
permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout
  issues: write     # This is required for manual-approval

on:
  release:
    types: prereleased

jobs:
  setup:
    name: Build Docker image
    environment: production
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1
    
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install dependencies
        run: |
          pip install PyYAML
          pip install jinja2-cli

      - name: Replace Config
        run: |
          cd docker/production
          jinja2 BILLING_MANAGER_CONFIG.yml --format=yaml --strict -D DBDSN=${{ secrets.DBDSN }} -D AMBERFLO_KEY=${{ secrets.AMBERFLO_KEY }} -o billing-manager.yaml
          jinja2 CONTROLLER_CONFIG.yml --format=yaml --strict -D ADMIN_API_KEY=${{ secrets.ADMIN_API_KEY }} -D ES_INTERNAL_URL=${{ vars.ES_INTERNAL_URL }}  -D PROM_PASSWORD=${{ secrets.PROM_PASSWORD }} -D KMS_KEY_ID=${{ secrets.KMS_KEY_ID }} -D GOOGLE_PRIVATE_KEY="${{ secrets.GOOGLE_PRIVATE_KEY }}" -D GOOGLE_PRIVATE_KEY_ID=${{ secrets.GOOGLE_PRIVATE_KEY_ID }} -D UDF_ACCESS_TOKEN=${{ secrets.UDF_ACCESS_TOKEN }} -D AUTH_WEBHOOK=${{ secrets.AUTH_WEBHOOK }} -D AUTO_START=${{ secrets.AUTO_START }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D STRIPE_SECRET=${{ secrets.STRIPE_SECRET }} -D SOLUTION_PK="${{ secrets.SOLUTION_PK }}" -D DB_PASSWORD=${{ secrets.DB_PASSWORD }} -D DBDSN=${{ secrets.DBDSN }} -D GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }} -o config
          jinja2 RESOURCE_MANAGER_CONFIG.yml --format=yaml --strict -D AUTO_START=${{ secrets.AUTO_START }} -D PULL_SECRET=${{ secrets.PULL_SECRET }} -D LICENSE_TG_00=${{ secrets.LICENSE_TG_00 }} -D LICENSE_TG_0=${{ secrets.LICENSE_TG_0 }} -D LICENSE_TG_1=${{ secrets.LICENSE_TG_1 }} -D LICENSE_TG_2=${{ secrets.LICENSE_TG_2 }} -D LICENSE_TG_4_16=${{ secrets.LICENSE_TG_4_16 }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D K8S_CLUSTER_TOKEN=${{ secrets.K8S_CLUSTER_TOKEN }} -D DBDSN=${{ secrets.DBDSN }} -o resource-manager.yaml
          jinja2 FILEBEAT.yml --format=yaml --strict -D ES_HOST=$(echo ${{ vars.ES_INTERNAL_URL }} | sed 's#^http://##' ) -o filebeat.yml

      - name: Get Controller Binary from assets
        uses: robinraju/release-downloader@v1.10
        with:
          releaseId: ${{ github.event.release.id }}
          fileName: controller
          out-file-path: release

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
        run: |
          cd docker/production
          cp -r ../../release/controller .
          cp -r ../../deployment/portal/transfer/teleport/setup_teleport.sh .
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:${SHA::6} .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:${SHA::6}
          echo $ECR_REGISTRY/$ECR_REPOSITORY:${SHA::6}

      - name: Get AGW Image tag
        run: |
          IMAGE=${{ vars.ECR_REGISTRY }}/cloud-agent-gateway:${SHA::6}
          echo "AGW_IMAGE=$IMAGE" >> $GITHUB_ENV

      - name: Build and Push AGW Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./agentgateway/docker/Dockerfile.agentgateway
          push: true
          tags: |
              ${{ env.AGW_IMAGE }}

      - name: configure aws us-east credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-east-1

      - name: Get Terraform Scripts from assets
        uses: robinraju/release-downloader@v1.10
        with:
          releaseId: ${{ github.event.release.id }}
          fileName: terraform.tar.gz
          out-file-path: release

      - name: Upload CloudFormation to S3
        run: |
          cd terraform/cloudformation
          jinja2 byoc_customer_role.yaml --format=yaml --strict -D DP_ACCOUNT_ID=${{ vars.DP_ACCOUNT_ID }} -D CP_ACCOUNT_ID=${{ vars.CP_ACCOUNT_ID }} -o new_byoc_customer_role.yaml
          aws s3 cp new_byoc_customer_role.yaml "s3://${{env.CLOUD_FORMATION_BUCKET}}/byoc_customer_role.yaml" --follow-symlinks --no-progress --cache-control max-age=0

      - name: Upload Terraform Scripts to S3
        run: |
          aws s3 cp release/terraform.tar.gz s3://${{ env.TERRAFORM_BUCKET }}/aws/${SHA::6}/terraform.tar.gz --follow-symlinks --no-progress
          aws s3 cp release/terraform.tar.gz s3://${{ env.TERRAFORM_BUCKET }}/aws/${{ vars.UAT_DEPLOY_BRANCH }}/terraform.tar.gz --follow-symlinks --no-progress


  manual-approval:
    name: Approval Staging Deploy
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Manual Approval for Staging
        uses: trstringer/manual-approval@v1
        with:
          issue-title: "[Staging] Deploying (sha: ${{ github.sha }}, tag: ${{ github.event.release.tag_name }})"
          minimum-approvals: 1
          secret: ${{ secrets.github_token }}
          approvers: ${{ vars.DEPLOYMENT_APPROVERS }}

  deploy-staging:
    name: Deploy Staging Environment
    environment: production
    needs: manual-approval
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Download ECS Task Definition
        run: |
          image=${{ vars.ECR_REGISTRY }}/$ECR_REPOSITORY:${SHA::6}
          echo "image=${image}" >> $GITHUB_ENV
          echo "sha=${SHA::6}" >> $GITHUB_ENV
          aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_STAGING_DEFINITION }} --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: controller
          image: ${{ env.image }}
          environment-variables: |
            ENV=staging
            SHA=${{ env.sha }}

      - name: Deploy Amazon ECS task definition for Staging Environment
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_STAGING_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
  
      - name: Slack Notification
        run: echo "slack notify, TBD"
  
  run-test:
    name: Run test in Staging Environment
    needs: deploy-staging
    runs-on: ubuntu-latest
    steps:
      - name: Test
        run: |
          echo "auto test, TBD"
  
  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [run-test]
    if: always()
    steps:
      - if: needs.run-test.result == 'success'
        run: |
          echo "publish_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.run-test.result == 'skipped' || needs.run-test.result == 'failure'
        run: |
          echo "publish_status=:red_circle:" >> $GITHUB_ENV
    
      - name: Slack Message
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "<${{ github.event.release.tag_name }}> On-PreReleased Result ${{ env.publish_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Release Version:*\n<${{ github.event.release.html_url }}|${{github.event.release.tag_name}}>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ github.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "plain_text",
                  "text": "API Test status: ${{ env.publish_status }}, Report TBD",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Deploy Environment:* <https://portal-staging.tgcloud.io|View Environment>"
                  },
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.CLOUD_DEPLOY_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )