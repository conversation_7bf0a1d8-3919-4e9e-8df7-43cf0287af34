name: Add Merge Label

on:
  pull_request:
    types: [closed]
    branches:
      - main
      - release_*

jobs:
  on-merge:
    name: Add Merged label in JIRA ticket
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    steps:
      - name: Add Merged label
        uses: tigergraph/github-actions/add-merge-label@main
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          jira_user: ${{ vars.JIRA_USERNAME }}
          jira_token: ${{ secrets.JIRA_API_TOKEN }}
          pr_title_regex: ${{ vars.PR_TITLE_REGEX_PATTERN }}

      - name: Remove Status PR label
        uses: actions-ecosystem/action-remove-labels@v1
        with:
          github_token: ${{ secrets.GST_BOT_TOKEN }}
          labels: |
            status: abandoned :wastebasket:
            status: blocked :lock:
            status: in progress :hammer:
            status: on hold :no_entry:
            status: review needed :raising_hand_man:
            status: revision needed :thinking:

      - name: Add Complete Status PR label
        uses: actions-ecosystem/action-add-labels@v1
        with:
          github_token: ${{ secrets.GST_BOT_TOKEN }}
          labels: |
            status: completed :ok_hand: