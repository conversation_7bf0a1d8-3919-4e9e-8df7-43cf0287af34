name: Auto Double Merge 

on:
  pull_request:
    types: ["closed"]
    branches:
      - release_*

permissions:
  contents: write
  pull-requests: write

jobs:
  get-double-merge-info:
    name: Get Double Merge Info
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    outputs:
      matrix: ${{ steps.get_merge_branches.outputs.matrix }}
      approved: ${{ steps.get_merge_branches.outputs.approved }}
    steps:
      - name: Fetch All Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get Double Merge Branch
        id: get_merge_branches
        run: |
          matches=$(echo "${{ github.event.pull_request.title }}" | grep -oP "${{ vars.PR_TITLE_REGEX_PATTERN }}" || true)
          if [[ -z $matches ]]; then
            errorMessage="Invalid PR title format. Please follow the correct format: \"([)ticket-number(]) type(scope): description;\" \nExample: TOOLS-1234 feat(login): add forgot password link;"
            echo "Failed:$errorMessage"
            exit 1
          else
            echo "Matching strings: $matches"
            IFS=$'\n'
            for match in $matches; do
                ticket=$(echo "$match" | grep -m 1 -oP "[a-zA-Z]+-\d+" | awk 'NR==1{print $1}')
                response=$(curl -s -u "${{ vars.JIRA_USERNAME }}:${{ secrets.JIRA_API_TOKEN }}" -H "Content-Type: application/json" "https://graphsql.atlassian.net/rest/api/latest/issue/$ticket")
                approved=$(echo $response | jq -r '.fields.customfield_12555.[0].value')
                branches=$(echo $response | jq -r '.fields.customfield_12554' | jq -c)
                echo "branches: $branches"
                echo "approved: $approved"
                if [[ $branches == "null" ]]; then
                  echo "Auto Port Branches not found in JIRA ticket, skip"
                  exit 1
                fi
                echo "matrix=$branches" >> $GITHUB_OUTPUT
                echo "approved=$approved" >> $GITHUB_OUTPUT
            done
          fi

  double-merge:
    needs: get-double-merge-info
    runs-on: ubuntu-latest
    name: Auto Double Merge
    strategy:
      matrix: 
        branch: ${{ fromJson(needs.get-double-merge-info.outputs.matrix) }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GST_BOT_TOKEN }}

      - name: Echo Params
        run : |
          echo ${{ matrix.branch }}
          echo "${{ toJson(github.event.pull_request.assignees) }}"
          echo ${{ needs.get-double-merge-info.outputs.approved }}

      - name: Create new double merge branch 
        env:
          github_token: ${{ secrets.GST_BOT_TOKEN }}
        run: |
          git checkout ${{ github.base_ref }}
          git checkout ${{ matrix.branch }}
          git pull origin ${{ matrix.branch }}
          git checkout -b ${{ github.head_ref }}-to-${{ matrix.branch }}
          git config --global user.email "<EMAIL>"
          git config --global user.name "gstbot"
          git push origin ${{ github.head_ref }}-to-${{ matrix.branch }}
          COMMIT_LIST=("${{ github.event.pull_request.merge_commit_sha }}")
          if [ -z "$COMMIT_LIST" ]; then
            echo "No commits found to cherry-pick between ${{ github.base_ref }} and ${{ github.head_ref }}."
            exit 0
          fi
          for COMMIT_ID in $COMMIT_LIST; do
            echo "Cherry-picking commit $COMMIT_ID..."
            git cherry-pick $COMMIT_ID -m 1 || {
              echo "Cherry-pick failed for commit $COMMIT_ID. Conflicts need to be resolved manually."
              git cherry-pick --abort
              git commit --allow-empty -m "Empty commit to bypass cherry-pick failure, Please handle conflict manual."
              git push --set-upstream origin ${{ github.head_ref }}-to-${{ matrix.branch }}
            }
          done
          git push --set-upstream origin ${{ github.head_ref }}-to-${{ matrix.branch }}

      - name: Create Double Merge PR
        env:
          GH_TOKEN: ${{ secrets.GST_BOT_TOKEN }}
        run: |
          gh pr create -a ${{ github.event.pull_request.assignees[0].login }} --base ${{ matrix.branch }} -b 'Created automatically by gstbot. Based PR: ${{ github.event.pull_request.html_url }}' \
           -l 'cicd: auto merge :bulb:' -l 'cicd: run it test :arrow_forward:' -l 'status: review needed :raising_hand_man:' -l 'type: robot generation :soon:' -r gstbot -t '${{ github.event.pull_request.title }}'

      - name: Auto Approved
        if: ${{ needs.get-double-merge-info.outputs.approved }} == 'Approved'
        env:
          GH_TOKEN: ${{ secrets.CICD_GITHUB_TOKEN }}
        run: |
            gh pr review --approve