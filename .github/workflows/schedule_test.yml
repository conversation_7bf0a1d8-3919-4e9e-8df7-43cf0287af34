name: Schedule Integrate Test

on:
  schedule:
    - cron: "0 1,9 * * *" #UTC
  workflow_dispatch:

env:
  AWS_REGION : "us-west-1"
  AWS_S3_BUCKET: tgcloud-test-v4
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

permissions:
  id-token: write
  contents: write
  checks: write
  pull-requests: write

jobs:
  api-test:
    name: API Test
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Run API test
        run: |
          cd tests/
          bash run.sh -E https://savanna.tgcloud.io -M betasomke_test -C org=qe-release-test

      - name: configure aws credentials
        if: always()
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Generate API Allure report and upload it to S3
        if: always()
        run: |
          wget --no-verbose -O /tmp/allure-commandline.zip https://repo.maven.apache.org/maven2/io/qameta/allure/allure-commandline/2.19.0/allure-commandline-2.19.0.zip && unzip /tmp/allure-commandline.zip
          mkdir tests/allure-report
          allure-2.19.0/bin/allure generate tests/alluredir/cloud_api -o tests/allure-report
          aws s3 sync tests/allure-report s3://${{ env.AWS_S3_BUCKET }}/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api --follow-symlinks --delete
          echo "### API Test Report URL:" >> $GITHUB_STEP_SUMMARY
          echo "http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cp -r /tmp/e2e/log tests/ 2>/dev/null || :
          cp -r /tmp/tools_e2e tests/log/ 2>/dev/null || :
          if [ -n "$(find tests/junit -maxdepth 1 -name 'test-results*.xml' 2>/dev/null)" ]; then
            echo "Files matching 'test-results*.xml' found in tests/junit directory."
            ls -l tests/junit/
          else
            echo "No files matching 'test-results*.xml' found in tests/log directory."
            mkdir -p tests/junit
            touch tests/junit/test-results.xml
          fi

      - name: Upload API test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cloud-test-api
          path: |
            tests/environment.properties
            tests/prepare_env_log*
            tests/junit/test-results*.xml
            tests/latest_logs/
            tests/alluredir/
            tests/data/log/*.log
            tests/log/

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [api-test]
    if: always()
    steps:
      - if: needs.api-test.result == 'failure'      
        name: Send Slack Message
        run: |
            slackMessage='{
                "blocks": [
                {
                    "type": "header",
                    "text": {
                    "type": "plain_text",
                    "text": "Beta Schedule Integrate Test Result :red_circle:",
                    "emoji": true
                    }
                },
                {
                    "type": "section",
                    "fields": [
                    {
                        "type": "mrkdwn",
                        "text": "*Branch:*\n${{ env.BRANCH_NAME }}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": "*Sha:*\n${{ github.sha }}"
                    }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                    "type": "plain_text",
                    "text": "API Test status: :red_circle:",
                    "emoji": true
                    }
                },
                {
                    "type": "section",
                    "text": {
                    "type": "mrkdwn",
                    "text": "<http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html|View API Test Report>"
                    }
                },
                {
                    "type": "section",
                    "text": {
                    "type": "mrkdwn",
                    "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action> <!here>"
                    }
                }
                ]
            }'
            curl -X POST ${{ secrets.CLOUD_ALERT_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
            --data @<(cat <<EOF
            $slackMessage
            EOF
            )