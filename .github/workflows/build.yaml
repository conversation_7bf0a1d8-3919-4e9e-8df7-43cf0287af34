name: Cloud build

env:
  AWS_S3_BUCKET: tgcloud-test-v4
  CLOUD_FORMATION_BUCKET: tgcloud-cloudformation
  TERRAFORM_BUCKET: tgcloud-provider-terraform
  AWS_REGION: us-west-1
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SHA: ${{ github.event.pull_request.head.sha || github.sha }}

on:
  pull_request:
    types: ["opened", "synchronize", "reopened", "ready_for_review", "edited"]
    branches: [ "main", "release_*" ]
    paths-ignore:
      - "*.md"
      - "deployment/*"

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      keepenv:
        description: 'Keep the env after test finished'
        type: boolean
        required: false
        default: false

permissions:
  pull-requests: write
  contents: write
  checks: write
  issues: read
  id-token: write

concurrency:
  group: '${{ github.workflow }} - ${{ github.head_ref || github.ref }}'
  cancel-in-progress: true

jobs:
  automerge:
    name: Enable automerge on PR
    runs-on: ubuntu-latest
    if: >- 
      contains(github.event.pull_request.labels.*.name, 'cicd: auto merge :bulb:')
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4
      - name: Enable Pull Request Automerge
        run: gh pr merge --squash --auto ${{ github.event.pull_request.number }}
        env:
          GH_TOKEN: ${{ secrets.GST_BOT_TOKEN }}

  check-dependencies:
    runs-on: ubuntu-latest
    name: Check PR Dependencies
    outputs:
      build-tgagent: ${{ steps.changes.outputs.agent }}
      upload-cloudformation: ${{ steps.changes-cloudformation.outputs.cloudformation }}
      build-agw: ${{ steps.agw-changes.outputs.agw }}
      build-ca: ${{ steps.ca-changes.outputs.ca }}
      build-autostart: ${{ steps.autostart-changes.outputs.autostart }}
      build-obs: ${{ steps.obs-changes.outputs.obs }}
      tf-changed: ${{ steps.tf-changes.outputs.tf }}
      ls-changed: ${{ steps.ls-changes.outputs.ls }}
    steps:
    - uses: gregsdennis/dependencies-action@main
      env:
        GITHUB_TOKEN: ${{ secrets.CICD_GITHUB_TOKEN }}

    - name: Check Path Agent Changed
      uses: dorny/paths-filter@v3
      id: changes
      with:
        filters: |
          agent:
            - 'agent/**'
            - 'Dockerfile'

    - name: Check Path CloudFormation Changed
      uses: dorny/paths-filter@v3
      id: changes-cloudformation
      with:
        filters: |
          cloudformation:
            - 'terraform/cloudformation/**'

    - name: Check Path AGW Changed
      uses: dorny/paths-filter@v3
      id: agw-changes
      with:
        filters: |
          agw:
            - 'agentgateway/**'
            - 'terraform/**'
            - 'resource-manager/cloud_provider/**'
            - 'common/**'
            - 'utils/**'

    - name: Check Path CA Changed
      uses: dorny/paths-filter@v3
      id: ca-changes
      with:
        filters: |
          ca:
            - 'clusteragent/**'
            - 'common/**'
            - 'utils/**'
            - 'resource-manager/**'
            - 'Dockerfile.clusteragent'
    
    - name: Check Path Autostart Changed
      uses: dorny/paths-filter@v3
      id: autostart-changes
      with:
        filters: |
          autostart:
            - 'autostarthandler/**'
            - 'common/**'
            - 'utils/**'
            - 'Dockerfile.autostart'
    
    - name: Check Path OBS Changed
      uses: dorny/paths-filter@v3
      id: obs-changes
      with:
        filters: |
          obs:
            - 'observability/**'

    - name: Check Path Terraform Changed
      uses: dorny/paths-filter@v3
      id: tf-changes
      with:
        filters: |
          tf:
            - 'terraform/cloud-provider/**'
            - 'terraform/helm-charts/**'

    - name: Check Path License Changed
      uses: dorny/paths-filter@v3
      id: ls-changes
      with:
        filters: |
          ls:
            - 'licenseserver/**'

  pre-merge-check:
    name: Check PR
    runs-on: ubuntu-latest
    steps:
      - name: Clear old comments
        uses: maheshrayas/action-pr-comment-delete@v2.0
        with:
          github_token: '${{ secrets.GITHUB_TOKEN }}'
          org: tigergraph
          repo: cloud-universe
          user: 'github-actions[bot]'
          issue: '${{github.event.number}}'

      - name: Validate PR
        if: github.event_name == 'pull_request' && github.event.pull_request
        uses: tigergraph/github-actions/pre-merge-check@main
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          jira_user: ${{ vars.JIRA_USERNAME }}
          jira_token: ${{ secrets.JIRA_API_TOKEN }}
          pr_title_regex: ${{ vars.PR_TITLE_REGEX_PATTERN }}

      - name: Check JIRA Ticket Status
        uses: tigergraph/github-actions/check-ticket-status@main
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          jira_user: ${{ vars.JIRA_USERNAME }}
          jira_token: ${{ secrets.JIRA_API_TOKEN }}
          pr_title_regex: ${{ vars.PR_TITLE_REGEX_PATTERN }}

      - name: Check if run IT test
        if: >-
          always() &&
          !contains(github.event.pull_request.labels.*.name, 'cicd: run it test :arrow_forward:') 
        uses: thollander/actions-comment-pull-request@v2
        with:
          message: |
            'miss `cicd: run it test` :arrow_forward: PR Label, you must set this label to run IT test before you merge.'

  upload-cloudformation:
    name: Upload CloudFormation
    needs: check-dependencies
    runs-on: ubuntu-latest
    environment: dev-new
    if: needs.check-dependencies.outputs.upload-cloudformation == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-east-1

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install dependencies
        run: |
          pip install PyYAML
          pip install jinja2-cli

      - name: Upload to S3
        run: |
          cd terraform/cloudformation
          jinja2 byoc_customer_role.yaml --format=yaml --strict -D DP_ACCOUNT_ID=${{ vars.DP_ACCOUNT_ID }} -D CP_ACCOUNT_ID=${{ vars.CP_ACCOUNT_ID }} -o new_byoc_customer_role.yaml
          aws s3 cp new_byoc_customer_role.yaml "s3://${{env.CLOUD_FORMATION_BUCKET}}/byoc_customer_role.yaml" --follow-symlinks --no-progress --cache-control max-age=0

  build-tgagent-image:
    name: Build TGAgent docker image
    needs: check-dependencies
    runs-on: ubuntu-latest
    environment: dev-new
    if: needs.check-dependencies.outputs.build-tgagent == 'true'
    outputs:
      tg_image: ${{steps.get-image.outputs.TG_IMAGE}}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=tginternal/cloud-agent:${SHA::6}
        echo "TG_IMAGE=$IMAGE" >> $GITHUB_ENV
        echo "TG_IMAGE=$IMAGE" >> $GITHUB_OUTPUT

    - name: Write secret to file
      run: |
        SECRET_FILE=$(mktemp)
        echo ${{ secrets.SECRET_ENCRYPTION_KEY }} > "$SECRET_FILE"
        chmod 600 "$SECRET_FILE"
        echo "SECRET_FILE=$SECRET_FILE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: |
            ${{ env.TG_IMAGE }}
            tginternal/cloud-agent:cloud-dev
        secret-files: |
            secret_encryption_key=${{ env.SECRET_FILE }}

  build-ca-image:
    name: Build CA docker image
    needs: check-dependencies
    runs-on: ubuntu-latest
    environment: dev-new
    if: needs.check-dependencies.outputs.build-ca == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=tginternal/cloud-cluster-agent:${SHA::6}
        echo "CA_IMAGE=$IMAGE" >> $GITHUB_ENV

    - name: Write secret to file
      run: |
        SECRET_FILE=$(mktemp)
        echo ${{ secrets.SECRET_ENCRYPTION_KEY }} > "$SECRET_FILE"
        chmod 600 "$SECRET_FILE"
        echo "SECRET_FILE=$SECRET_FILE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile.clusteragent
        push: true
        tags: |
            ${{ env.CA_IMAGE }}
        secret-files: |
            secret_encryption_key=${{ env.SECRET_FILE }}
  
  build-autostart-image:
    name: Build Autostart docker image
    needs: check-dependencies
    runs-on: ubuntu-latest
    environment: dev-new
    if: needs.check-dependencies.outputs.build-autostart == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=tginternal/cloud-auto-start-handler:${SHA::6}
        echo "AUTOSTART_IMAGE=$IMAGE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile.autostart
        push: true
        tags: |
            ${{ env.AUTOSTART_IMAGE }}

  build-agent-gateway-image:
    name: Build AGW docker image
    needs: check-dependencies
    environment: dev-new
    runs-on: ubuntu-latest
    if: needs.check-dependencies.outputs.build-agw == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=${{ steps.login-ecr.outputs.registry }}/cloud-agent-gateway:${SHA::6}
        echo "AGW_IMAGE=$IMAGE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./agentgateway/docker/Dockerfile.agentgateway
        push: true
        tags: |
            ${{ env.AGW_IMAGE }}

  build-license-server-image:
    name: Build License server image
    needs: check-dependencies
    environment: dev-new
    runs-on: ubuntu-latest
    if: needs.check-dependencies.outputs.ls-changed == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
        cache: 'pip'

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=${{ steps.login-ecr.outputs.registry }}/license-server:${SHA::6}
        echo "LS_IMAGE=$IMAGE" >> $GITHUB_ENV
        pip install PyYAML
        pip install jinja2-cli
        cd licenseserver
        jinja2 LICENSESERVER_CONFIG.yaml --format=yaml --strict -D LICENSE_SMTP_USERNAME=${{ vars.LICENSE_SMTP_USERNAME }} -D LICENSE_SMTP_FROM=${{ vars.LICENSE_SMTP_FROM }} -D LICENSE_SMTP_PASSWORD=${{ secrets.LICENSE_SMTP_PASSWORD }} -D DB_HOST=${{ vars.DB_HOST }} -D DB_PASSWORD=${{ secrets.DB_PASSWORD }} -D LICENSE_AES_KEY=${{ secrets.LICENSE_AES_KEY }} -D LICENSE_AES_IV=${{ secrets.LICENSE_AES_IV }} -D LICENSE_JWT_PRIVATE_KEY_B64=${{ secrets.LICENSE_JWT_PRIVATE_KEY_B64 }} -o licenseserverconfig.yaml

    - name: Build and Push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./licenseserver/Dockerfile
        push: true
        build-args: |
          GITHUB_TOKEN=${{ secrets.GST_BOT_TOKEN }}
          GITHUB_USER=gstbot
        tags: |
            ${{ env.LS_IMAGE }}

  build-obs-image:
    name: Build Obs docker image
    needs: check-dependencies
    environment: dev-new
    runs-on: ubuntu-latest
    if: needs.check-dependencies.outputs.build-obs == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        IMAGE=${{ steps.login-ecr.outputs.registry }}/metric-proxy:${SHA::6}
        echo "OBS_IMAGE=$IMAGE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./observability/Dockerfile
        push: true
        tags: |
            ${{ env.OBS_IMAGE }}

  upload-tf:
    name: Upload terraform to S3
    needs: check-dependencies
    environment: dev-new
    runs-on: ubuntu-latest
    if: needs.check-dependencies.outputs.tf-changed == 'true'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: us-east-1

    - name: Upload to S3
      run: |
        cd terraform
        tar -czvf terraform.tar.gz ./*
        aws s3 cp terraform.tar.gz s3://${{ env.TERRAFORM_BUCKET }}/aws/${SHA::6}/terraform.tar.gz --follow-symlinks --no-progress

  golang:
    name: Build Go
    runs-on: ubuntu-20.04
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy

      - name: Build Go targets
        run: make go-targets -j5

      - name: Upload Go executables
        uses: actions/upload-artifact@v4
        with:
          name: go-targets
          path: release

  unit-test:
    name: Unit Test
    runs-on: ubuntu-20.04
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 200

      - name: Get go diff lines
        id: get-diff
        if: github.event_name == 'pull_request'
        uses: technote-space/get-diff-action@v6
        with:
          PATTERNS: |
            **/*.go
            !**/*_test.go
          GET_FILE_DIFF: true
          SET_ENV_NAME_LINES: 'GO_DIFF_LINES'
          SET_ENV_NAME_INSERTIONS: 'GO_INSERTIONS_LINES'
          SET_ENV_NAME_DELETIONS: 'GO_DELETIONS_LINES'

      - name: Check go diff lines
        if: github.event_name == 'pull_request'
        run: |
          if [ ${{ env.GO_DIFF_LINES }} -le ${{ vars.INC_COVERAGE_DIFF_LINES }} ]; then
            echo "go diff lines is less than ${{ vars.INC_COVERAGE_DIFF_LINES }}, skip inc unit gate"
            echo "NEED_INC_COV=false" >> $GITHUB_ENV
          else
            echo "NEED_INC_COV=true" >> $GITHUB_ENV
          fi

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy

      - name: Set up Python 3.10
        if: env.NEED_INC_COV == 'true'
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Go make proto
        run: make proto

      - name: configure aws credentials
        if: always()
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Unit test
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          go get github.com/vakenbolt/go-test-report
          go install github.com/vakenbolt/go-test-report
          export GOEXPERIMENT=nocoverageredesign
          echo 'You can check UT report in: https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/test_report.html'
          go test -v -json -short -coverpkg ./... -coverprofile=coverage.prof ./... > ut.json

      - name: Install gocov and diff_cover
        if: env.NEED_INC_COV == 'true'
        run: |
          go install github.com/axw/gocov/gocov@v1.1.0
          go install github.com/AlekSi/gocov-xml@latest
          python -m pip install --upgrade pip
          pip install diff_cover

      - name: Generate inc coverage report
        if: env.NEED_INC_COV == 'true'
        run: |
          gocov convert coverage.prof | gocov-xml > gocov.xml
          python .github/workflows/gen_coverage_badge/parse_coverage_xml.py
          exclude_args=$(awk '{printf "\"%s\" ", $0}' .covignore)
          command="diff-cover coverage.xml --compare-branch=${{ github.event.pull_request.base.sha }} --json-report increment_coverage_report.json --html-report increment_coverage_report.html --exclude ${exclude_args[*]}"
          eval $command
          total_num_lines=$(cat increment_coverage_report.json | jq -r '.total_num_lines')
          if [[ "$total_num_lines" != "0" ]]; then
            echo "NEED_REPORT=true" >> $GITHUB_ENV
            total_percent_covered=$(cat increment_coverage_report.json | jq -r '.total_percent_covered')
            echo "ICC=${total_percent_covered}" >> $GITHUB_ENV
            if [[ ${total_percent_covered} -le ${{ vars.INC_COVERAGE_THRESHOLD }} ]]; then
              echo "go diff lines coverage is ${total_percent_covered}%, below gate ${{ vars.INC_COVERAGE_THRESHOLD }}%, plaese supplement some unit test case to improve coverage."
              exit 1
            fi
          fi

      - name: Generate coverage report
        if: always()
        run: |
          go install github.com/cancue/covreport@latest
          covreport -i ./coverage.prof -o coverage.html -cutlines 80,50
          go tool cover -func=./coverage.prof -o coverage.txt
          num=$(grep ^total: coverage.txt | awk '{print substr($3, 1, length($3)-1)}')
          echo "CC=${num}" >> $GITHUB_ENV
          mkdir coverage
          cp coverage.* coverage/
          if [ -e increment_coverage_report.json ]; then
            cp -f increment_coverage_report.* coverage/
          fi

      - name: Generate coverage badge
        uses: ./.github/workflows/gen_coverage_badge
        if: always()
        with:
          coverage: ${{ env.CC }}
          path: coverage/badges/coverage-statements.svg

      - name: Generate increment coverage badge
        uses: ./.github/workflows/gen_coverage_badge
        if: always() && env.NEED_REPORT == 'true'
        with:
          coverage: ${{ env.ICC }}
          path: coverage/badges/inc-coverage-statements.svg

      - name: Upload coverage
        if: always()
        run: |
          REPORT_COMMENT=./temp_message
          aws s3 sync coverage/badges s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/ut --follow-symlinks --delete
          echo "#### Full Unit Test Coverage:" >> ${REPORT_COMMENT}
          echo "![coverage](http://${{ env.AWS_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage-statements.svg)" >> ${REPORT_COMMENT}

          aws s3 sync coverage s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut --follow-symlinks --delete
          echo "#### Full Coverage Report(visit in VPN):" >> ${REPORT_COMMENT}
          echo "https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage.html" >> ${REPORT_COMMENT}

          cat ut.json | go-test-report
          aws s3 cp test_report.html s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/test_report.html --follow-symlinks --no-progress
          echo "#### Unit Test Report(visit in VPN):" >> ${REPORT_COMMENT}
          echo "https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/test_report.html" >> ${REPORT_COMMENT}

          if [[ -e "coverage/increment_coverage_report.html" && -n "${{ env.NEED_REPORT }}" ]];then
            echo "#### Increment Unit Test Coverage:" >> ${REPORT_COMMENT}
            echo "![coverage](http://${{ env.AWS_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/ut/inc-coverage-statements.svg)" >> ${REPORT_COMMENT}

            echo "#### Increment Coverage Report(visit in VPN):" >> ${REPORT_COMMENT}
            echo "https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/increment_coverage_report.html" >> ${REPORT_COMMENT}
          fi
          MESSAGE=$(cat ${REPORT_COMMENT})
          while IFS= read -r line; do
            echo "$line" >> "$GITHUB_STEP_SUMMARY"
          done < "$REPORT_COMMENT"

          echo "MESSAGE<<EOF" >> $GITHUB_ENV
          echo "$MESSAGE" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Comment in pull request
        if: always() && github.event_name == 'pull_request'
        uses: thollander/actions-comment-pull-request@v2
        with:
          message: |
            ${{ env.MESSAGE }}

      - name: Quality gate - coverage should be above threshold
        if: >- 
          always() && 
          github.event_name == 'pull_request' && 
          env.GO_DELETIONS_LINES < env.GO_INSERTIONS_LINES && 
          env.NEED_INC_COV == 'true'
        run: |
          aws s3api get-object --bucket ${{ env.AWS_S3_BUCKET }} --key cloud-universe-coverage/${{ github.base_ref }}/ut/coverage.txt coverage-threshold.txt
          threshold=$(grep ^total: coverage-threshold.txt | awk '{print substr($3, 1, length($3)-1)}')
          if (( $(echo "${{ env.CC }} < $threshold" |bc -l) )); then
              echo "UT code coverage is below threshold: ${{ env.CC }} < $threshold. Please add more unit test."
              exit 1
          fi
          echo "OK. Current UT code coverage is equal or above the threshold: ${{ env.CC }} >= $threshold"

      - name: Upload coverage report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ut-coverage
          path: coverage.*

      - name: Upload inc coverage report
        if: always() && ${{ env.NEED_REPORT }}
        uses: actions/upload-artifact@v4
        with:
          name: inc-ut-coverage
          path: increment_coverage_report.*

  lint:
    name: Lint
    runs-on: ubuntu-20.04
    if: github.event_name == 'pull_request'
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'
          cache: false

      - name: Go mod tidy
        run: |
          make tidy
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"

      - name: Go make proto
        run: make proto

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: v1.64.2
          args: --out-format=colored-line-number --issues-exit-code=1 -c .golangci.yaml
          only-new-issues: true
          skip-cache: true

  deploy:
    name: Deploy IT test env
    uses: ./.github/workflows/create_dev_env.yml
    if: >-
      contains(github.event.pull_request.labels.*.name, 'cicd: run it test :arrow_forward:')
    secrets: inherit

  runtest:
    needs: deploy
    name: Run IT test
    uses: ./.github/workflows/run_test.yml
    with:
      header: "cloud-universe#e2e-${{ github.head_ref || github.ref_name }}"
    secrets: inherit

  clean-test-resource:
    name: Clean API test resource
    needs: runtest
    if: >-
      always() &&
      contains(github.event.pull_request.labels.*.name, 'cicd: run it test :arrow_forward:') 
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Clean API test resource
        run: |
          bash tests/run_cloud_api.sh clean_resource "cloud-universe#e2e-${{ github.head_ref || github.ref_name }}"

  clean-env:
    name: Clean Test Env
    needs: clean-test-resource
    if: >-
      always() &&
      !contains(github.event.pull_request.labels.*.name, 'cicd: keep env :snowflake:') && !inputs.keepenv
    uses: ./.github/workflows/clean_dev_env.yml
    secrets: inherit
