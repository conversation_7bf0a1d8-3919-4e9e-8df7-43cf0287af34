name: <PERSON><PERSON> Snyk <PERSON>an Schedule

on:
  workflow_dispatch:
  schedule:
    - cron: "0 20 * * *" #UTC

permissions:
  pull-requests: write
  contents: write
  checks: write
  issues: read
  id-token: write

jobs:
  trigger-synk-scan:
    name: Tri<PERSON> Snyk <PERSON>an
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Snyk <PERSON>an
        run: |
          echo "${{ vars.SNYK_SCAN_VERSIONS }}" | tr ',' '\n' | while read -r version; do
            curl -X POST https://zulip.graphtiger.com/api/v1/messages \
            -u ${{ secrets.ZULIP_TOKEN }} \
            --data-urlencode type=direct \
            --data-urlencode 'to=[214]' \
            --data-urlencode "content=wip -docker_scan -b tg_${version}_dev -bb pcr-test"
          done
