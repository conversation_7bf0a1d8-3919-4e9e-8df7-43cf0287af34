name: Clean Docker Hub Cloud Images
on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"

permissions:
  id-token: write
  contents: read

jobs:
  get-clusters:
    name: "Get Clusters"
    outputs:
        CLUSTERS: ${{ steps.get_clusters.outputs.CLUSTERS }}
    runs-on: ubuntu-latest
    steps:
      - name: Fetch Teleport binaries
        uses: teleport-actions/setup@v1
        with:
          version: 16.4.13

      - name: Fetch credentials using Machine ID
        id: auth
        uses: teleport-actions/auth@v2
        with:
            proxy: tigergraph.teleport.sh:443
            token: github-token
            certificate-ttl: 1h

      - name: Fetch Kubernetes clusters from Teleport
        id: get_clusters
        run: |
            clusters=$(tsh kube ls --format=json | jq -r '[.[] | select(.kube_cluster_name | startswith("tg-")) | .kube_cluster_name]' | jq -c)
            echo "CLUSTERS=$clusters" >> $GITHUB_OUTPUT

  fetch-images:
    name: Fetch Images
    needs: get-clusters
    runs-on: ubuntu-latest
    strategy:
      matrix:
        cluster: ${{ fromJson(needs.get-clusters.outputs.CLUSTERS) }}
    steps:
      - name: Fetch Teleport binaries
        uses: teleport-actions/setup@v1
        with:
          version: 16.4.13

      - name: Fetch credentials using Machine ID
        uses: teleport-actions/auth-k8s@v2
        with:
          proxy: tigergraph.teleport.sh:443
          token: github-token
          kubernetes-cluster: ${{ matrix.cluster }}
          certificate-ttl: 1h

      - name: Set up kubectl
        uses: azure/setup-kubectl@v4

      - name: Get Images from the Kubernetes Cluster
        id: get_images
        run: |
            # Get all tg CR (Custom Resource) information
            kubectl get tg -A -o=jsonpath='{.items[*].spec.sidecarContainers[?(@.name=="tgagent")].image}' | tr ' ' '\n' | while read image; do
                echo $image >> tg_images.txt  # Append the image name to tg_images.txt
            done
            ca_image=$(kubectl get deployment cluster-agent-clusteragent -o=jsonpath='{.spec.template.spec.containers[0].image}')
            echo $ca_image >> tg_images.txt
            autostart_image=$(kubectl get deployment autostart-handler-autostarthandler -o=jsonpath='{.spec.template.spec.containers[0].image}')
            echo $autostart_image >> tg_images.txt
            images=$(cat tg_images.txt)
            echo $images > images-${{ matrix.cluster }}.txt

      - name: Upload blob report to GitHub Actions Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: images-${{ matrix.cluster }}.txt
          path: images-${{ matrix.cluster }}.txt
          retention-days: 1

  clean-images:
    needs: [get-clusters, fetch-images]
    name: Clean Dcoekr Hub Images
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download blob reports from GitHub Actions Artifacts
      uses: actions/download-artifact@v4
      with:
        path: images
        pattern: images-*
        merge-multiple: true

    - name: Get Images from fetch-images output
      run: |
        ls -l images
        CLUSTERS='${{ needs.get-clusters.outputs.CLUSTERS }}'
        for cluster in $(echo "$CLUSTERS" | jq -r '.[]'); do
            images=$(cat images/images-${cluster}.txt)
            echo $images >> tg_images.txt
        done
        cat tg_images.txt
        cp -r tg_images.txt .github/script

    - name: Set up Docker
      uses: docker/setup-docker-action@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.24.2'

    - name: Install Hub Tool
      run: |
        GO111MODULE=on go get github.com/docker/hub-tool
        GO111MODULE=on go install github.com/docker/hub-tool
        sudo apt-get update && sudo apt-get install -y expect

    - name: Create expect script with secret password
      run: |
        echo '#!/usr/bin/expect -f' > login.exp
        echo 'set timeout -1' >> login.exp
        echo 'spawn hub-tool login ${{ vars.DOCKERHUB_USERNAME }}' >> login.exp
        echo 'expect "Password:"' >> login.exp
        echo 'send "${{ secrets.DOCKERHUB_TOKEN }}\r"' >> login.exp
        echo 'expect eof' >> login.exp

    - name: Run expect script
      run: |
          export PATH=$(go env GOPATH)/bin:$PATH
          chmod +x login.exp
          ./login.exp

    - name: Clean Images
      run: |
        cd .github/script
        bash clean_images.sh