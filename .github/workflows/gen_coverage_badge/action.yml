name: 'Generate coverage badge'
description: 'Generate coverage badge'
inputs:
  coverage:
    description: 'Coverage data'
    required: true
  path:
    description: 'svg output path'
    required: true

runs:
  using: "composite"
  steps:
    - name: Print parameters
      shell: bash
      run: |
        echo "*********************************************************************"
        echo "*     Coverage is ${{ inputs.coverage}}"
        echo "*     Path is ${{ inputs.path}}"
        echo "*********************************************************************"

    - name: mkdir
      shell: bash
      run: |
        dir=$(dirname "${{ inputs.path}}")
        mkdir -p "${dir}"

    - name: Generate coverage badge
      uses: emibcn/badge-action@v2.0.3
      with:
        label: 'coverage'
        status: ${{ inputs.coverage }}%
        color: ${{
          inputs.coverage > 90 && 'green'              ||
          inputs.coverage > 80 && 'yellow,green'       ||
          inputs.coverage > 70 && 'yellow'             ||
          inputs.coverage > 60 && 'orange,yellow'      ||
          inputs.coverage > 50 && 'orange'             ||
          inputs.coverage > 40 && 'red,orange'         ||
          inputs.coverage > 30 && 'red,red,orange'     ||
          inputs.coverage > 20 && 'red,red,red,orange' ||
          'red' }}
        path: ${{ inputs.path}}
