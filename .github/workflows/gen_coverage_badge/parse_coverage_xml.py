import xml.etree.ElementTree as ET

def fill_missing_lines(element):
    """
    fill the missling line in covearage.xml
    """
    if element.tag == 'lines':
        line_elements = element.findall(".//line")
        if len(line_elements) == 0:
            return
        new_lines = []
        for i in range(len(line_elements)-1):
            current_line = line_elements[i]
            next_line = line_elements[i+1]

            current_number = int(current_line.attrib["number"])
            next_number = int(next_line.attrib["number"])

            current_hits = int(current_line.attrib["hits"])
            next_hits = int(next_line.attrib["hits"])

            new_lines.append(current_line)

            # If the line number is not consecutive, but the hits are all 1, the middle line is completed
            if next_number - current_number > 1 and current_hits == next_hits == 1:
                for missing_number in range(current_number + 1, next_number):
                    missing_line = ET.Element("line", number=str(missing_number), hits="1")
                    new_lines.append(missing_line)

        # Add a final line element
        new_lines.append(line_elements[-1])

        # Replace the lines element
        element.clear()
        element.extend(new_lines)

    # Recursively process the child elements
    for child in element:
        fill_missing_lines(child)

if __name__ == '__main__':
    tree = ET.parse('gocov.xml')
    root = tree.getroot()
    fill_missing_lines(root)
    # save
    output_file_path = "coverage.xml"
    with open(output_file_path, "w") as output_file:
        output_file.write(ET.tostring(root, encoding="unicode"))

    print(f"XML saved to {output_file_path}")
