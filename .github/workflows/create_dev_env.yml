name: Create dev working environment

on:
  pull_request:
    types: [labeled, synchronize]
  workflow_call:
    inputs:
      externall_call:
        description: 'To distinguish workflow_call from regular push'
        type: boolean
        required: false
        default: true

env:
  AWS_REGION : "us-east-1" #Change to reflect your Region
  ECR_REPOSITORY: tgcloudv4
  ECS_TASK_DEFINITION: tgcloud_dev
  ECS_CLUSTER: TGCloud-dev
  SHA: ${{ github.event.pull_request.head.sha || github.sha }}
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SECRET_ENCRYPTION_KEY: ${{ secrets.SECRET_ENCRYPTION_KEY }}

# Permission can be added at job level or workflow level    
permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout

concurrency:
  group: 'create dev env - ${{ github.workflow }} - ${{ github.head_ref || github.ref }} - ${{ github.event.label.name }}'
  cancel-in-progress: true

jobs:
  setup:
    name: Setup dev env
    environment: dev-new
    if: >-
      inputs.externall_call ||
      (
        (github.event.pull_request.state == 'open' || github.event.pull_request.draft) &&
        (
          (github.event.action == 'synchronize' && contains(join(github.event.pull_request.labels.*.name, '|'), 'dev env :flashlight:') && !contains(github.event.pull_request.labels.*.name, 'cicd: no update :no_entry_sign:')) ||
          (github.event.action == 'labeled' && github.event.label.name == 'cicd: dev env :flashlight:')
        )
      )
    runs-on: ubuntu-latest
    outputs:
      image: ${{steps.build-image.outputs.myimage}}
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install dependencies
        run: |
          pip install PyYAML
          pip install jinja2-cli

      - name: Copy Code
        run: |
          git config --unset http.https://github.com/.extraheader
          mkdir -p ../cloud-universe-bak
          cp -r . ../cloud-universe-bak

      - name: Replace Config
        run: |
          cd docker/dev
          jinja2 BILLING_MANAGER_CONFIG.yml --format=yaml --strict -D DBDSN=${{ secrets.LOCAL_DBDSN }} -D AMBERFLO_KEY=${{ secrets.AMBERFLO_KEY }} -o billing-manager.yaml
          jinja2 CONTROLLER_CONFIG.yml --format=yaml --strict -D ADMIN_API_KEY=${{ secrets.ADMIN_API_KEY }} -D ES_INTERNAL_URL=${{ vars.ES_INTERNAL_URL }} -D PROM_PASSWORD=${{ secrets.PROM_PASSWORD }} -D KMS_KEY_ID=${{ secrets.KMS_KEY_ID }} -D GOOGLE_PRIVATE_KEY="${{ secrets.GOOGLE_PRIVATE_KEY }}" -D GOOGLE_PRIVATE_KEY_ID=${{ secrets.GOOGLE_PRIVATE_KEY_ID }} -D UDF_ACCESS_TOKEN=${{ secrets.UDF_ACCESS_TOKEN }} -D AUTH_WEBHOOK=${{ secrets.AUTH_WEBHOOK }} -D AUTO_START=${{ secrets.AUTO_START }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D STRIPE_SECRET=${{ secrets.STRIPE_SECRET }} -D SOLUTION_PK="${{ secrets.SOLUTION_PK }}" -D DB_PASSWORD=${{ secrets.DB_PASSWORD }}  -D DBDSN=${{ secrets.LOCAL_DBDSN }} -D GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }} -o config
          jinja2 RESOURCE_MANAGER_CONFIG.yml --format=yaml --strict -D AUTO_START=${{ secrets.AUTO_START }} -D K8S_WEST_CLUSTER_TOKEN=${{ secrets.K8S_WEST_CLUSTER_TOKEN }} -D PULL_SECRET=${{ secrets.PULL_SECRET }} -D LICENSE=${{ secrets.LICENSE }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D K8S_CLUSTER_TOKEN=${{ secrets.K8S_CLUSTER_TOKEN }} -D DBDSN=${{ secrets.LOCAL_DBDSN }} -o resource-manager.yaml
          jinja2 FILEBEAT.yml --format=yaml --strict -D ES_HOST=$(echo ${{ vars.ES_INTERNAL_URL }} | sed 's#^http://##' ) -o filebeat.yml

      - name: Replace actor email in controller config
        run: |
          email=$(echo '${{ vars.SLACK_USER_MAPPING }}' | jq -r '. | ."${{ github.triggering_actor }}"')
          echo "actor email: $email"
          if [ -n "$email" ]; then
            yq -i ".cloud.aws.ses.notifyees.cloudteam = \"$email\"" docker/dev/config
          fi

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy

      - name: Build Go Targets
        run: |
          if [ "${{ inputs.externall_call }}" = "true" ]; then
            export BRANCH_NAME=e2e-${{ env.BRANCH_NAME }}
          fi
          make go-targets -j5

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: init build
        id: build-image
        run: |
          cd docker/dev
          cp -r ../../deployment/portal/transfer/teleport/setup_teleport.sh .
          cp -r ../../release/controller .
          cp -r ../../deployment/code_coverage/collect_data.sh .
          cp -r ../../../cloud-universe-bak cloud-universe
          if [ "${{ inputs.externall_call }}" = "true" ]; then
            IMAGE=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:e2e-${SHA::6}
          else
            IMAGE=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:${SHA::6}
          fi
          echo "IMAGE=$IMAGE" >> $GITHUB_ENV
          echo "myimage=$(echo $IMAGE | base64 -w0 | base64 -w0)" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build, tag, and push image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          push: true
          pull: true
          tags: ${{ env.IMAGE }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache
          cache-to: type=registry,image-manifest=true,oci-mediatypes=true,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache,mode=max
          context: docker/dev/.
          file: docker/dev/Dockerfile

  deploy:
    name: Deploy
    needs: setup
    environment: dev-new
    runs-on: ubuntu-latest
    steps:
      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Check Deploy Type
        run: |
          if [ "${{ inputs.externall_call }}" = "true" ]; then
            echo "SERVICE_NAME=e2e-${{ env.BRANCH_NAME }}" >> $GITHUB_ENV
            echo "DEPLOY_TYPE=e2e" >> $GITHUB_ENV
            echo "TARGET_GROUP_PREFIX=e2e" >> $GITHUB_ENV
          else
            echo "SERVICE_NAME=service-${{ env.BRANCH_NAME }}" >> $GITHUB_ENV
            echo "DEPLOY_TYPE=dev" >> $GITHUB_ENV
            echo "TARGET_GROUP_PREFIX=cicd" >> $GITHUB_ENV
          fi

      - name: Check if ECS service exists
        id: check_service
        run: |
          TARGET_GROUP_NAME=$(echo "${{ env.TARGET_GROUP_PREFIX }}-${{ env.BRANCH_NAME }}" | sed 's/_/-/g')
          echo "TARGET_GROUP_NAME=$TARGET_GROUP_NAME" >> $GITHUB_ENV
          FAIL_REASON=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.SERVICE_NAME }} --query "failures[0].reason")
          if [ "$FAIL_REASON" = "\"MISSING\"" ]; then
            echo "NEED_CREATE=true" >> $GITHUB_ENV
            exit 0
          fi
          SERVICE_STATUS=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.SERVICE_NAME }} --query "services[0].status")
          if [ "$SERVICE_STATUS" = "\"INACTIVE\"" ]; then
            echo "NEED_CREATE=true" >> $GITHUB_ENV
          else
            echo "NEED_UPDATE=true" >> $GITHUB_ENV
          fi

      - name: Download task definition
        run: |
            image=$(echo ${{ needs.setup.outputs.image }} | base64 -di | base64 -di)
            echo "image=${image}" >> $GITHUB_ENV
            aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEFINITION }} --query taskDefinition > task-definition.json

      - name: Use Local DB
        if: >-
          contains(github.event.pull_request.labels.*.name, 'cicd: local DB :floppy_disk:') && env.DEPLOY_TYPE == 'dev'
        run: |
          echo "DEV_DB=false" >> $GITHUB_ENV
          AccessPointId=$(aws efs describe-access-points --file-system-id ${{ vars.EFS_ID }} --query 'AccessPoints[?RootDirectory.Path==`/service-${{ env.BRANCH_NAME }}`].AccessPointId' --output text)
          if [ -z "$AccessPointId" ];then
            AccessPointId=$(aws efs create-access-point --file-system-id ${{ vars.EFS_ID }} --root-directory "Path=/service-${{ env.BRANCH_NAME }},CreationInfo={OwnerUid=0,OwnerGid=11,Permissions=775}" --query AccessPointId --output text)
          fi
          echo "AccessPointId=$AccessPointId" >> $GITHUB_ENV

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: controller
          image: ${{ env.image }}
          environment-variables: |
            BRANCH=${{ env.BRANCH_NAME }}
            CONSULIP=${{ vars.CONSUL_IP }}
            DEV_DB_HOST=${{ vars.DB_HOST }}
            DEV_DB=${{ env.DEV_DB }}
            DEPLOY_TYPE=${{ env.DEPLOY_TYPE }}
            DEV_DBDSN=${{ secrets.DBDSN }}

      - name: Configure AWS ELB Target Group
        if: env.NEED_CREATE == 'true'
        id: create-target-group
        uses: icalia-actions/aws-configure-elb-target-group@v0.0.3
        with:
          name: ${{ env.TARGET_GROUP_NAME }}
          port: "80"
          protocol: HTTP
          target-type: ip
          vpc-id: ${{ vars.VPC_ID }}
          health-check-path: /api/echo

      - name: Create ECS task-definition
        id: create-task-definition
        run: |
          NEW_TASK_DEFINITION=$(cat "${{ steps.task-def.outputs.task-definition }}" | jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)')
          if [ "${{ env.DEV_DB }}" == "false" ];then
            NEW_TASK_DEFINITION=$(echo $NEW_TASK_DEFINITION | jq '.volumes |= map(. = {
              "name": "cloud-data-deploy",
              "efsVolumeConfiguration": {
                  "fileSystemId": "${{ vars.EFS_ID }}",
                  "rootDirectory": "/",
                  "transitEncryption": "ENABLED",
                  "authorizationConfig": {
                      "accessPointId": "${{ env.AccessPointId }}",
                      "iam": "DISABLED"
                  }
              }
            })')
          else
            NEW_TASK_DEFINITION=$(echo $NEW_TASK_DEFINITION | jq '.volumes |= map(. = {
              "name": "cloud-data-deploy",
              "host": {}
            })')
          fi
          NEW_TASK_ARN=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEFINITION" | jq -r '.taskDefinition.taskDefinitionArn')
          echo "NEW_TASK_ARN=$NEW_TASK_ARN" >> $GITHUB_ENV

      - name: Configure AWS ELB Rule
        uses: icalia-actions/aws-configure-elb-rule@v0.0.1
        if: env.NEED_CREATE == 'true'
        with:
          listener: ${{ vars.ELB_LISTENER }}
          conditions: |
            - Field: http-header
              HttpHeaderConfig:
                HttpHeaderName: "x-test-env"
                Values:
                  - "*cloud-universe#${{ env.DEPLOY_TYPE }}-${{ env.BRANCH_NAME }}*"
            - Field: path-pattern
              PathPatternConfig:
                Values:
                  - "/api*"
          actions: |
            - Type: forward
              TargetGroupArn: ${{ steps.create-target-group.outputs.target-group-arn }}
          tags: |
            - Key: envtag
              Value: cloud-universe--${{ env.DEPLOY_TYPE }}-${{ env.BRANCH_NAME }}
            - Key: environment
              Value: cicd

      - name: Create or update ECS service
        if: env.NEED_CREATE == 'true'
        timeout-minutes: 10
        run: |
          LOAD_BALANCERS_JSON="{\"loadBalancers\": [{\"targetGroupArn\": \"${{ steps.create-target-group.outputs.target-group-arn }}\", \"containerName\": \"controller\", \"containerPort\": 5001}]}"
          NETWORK_CONFIGURATION_JSON="awsvpcConfiguration={subnets=[\"subnet-0d0b523ba9a78865a\",\"subnet-0951b97b2ca4548cc\",\"subnet-05237ce2c849bbc7f\"],securityGroups=[\"sg-048fd56a6866d7d00\"],assignPublicIp=ENABLED}"
          aws ecs create-service --cluster ${{ env.ECS_CLUSTER }} --service-name ${{ env.SERVICE_NAME }} --task-definition ${{ env.NEW_TASK_ARN }} --desired-count 1 --network-configuration "$NETWORK_CONFIGURATION_JSON" --launch-type "FARGATE" --cli-input-json "$LOAD_BALANCERS_JSON"
          aws ecs wait services-stable --cluster ${{ env.ECS_CLUSTER }}  --services ${{ env.SERVICE_NAME }}

      - name: Update ECS service
        if: env.NEED_UPDATE == 'true'
        id: update-service
        timeout-minutes: 10
        run: |
          aws ecs update-service --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.SERVICE_NAME }} --task-definition ${{ env.NEW_TASK_ARN }}  --desired-count 1
          aws ecs wait services-stable --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.SERVICE_NAME }}

      - name: Check out code into the source code repository
        if: inputs.externall_call != true
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        if: inputs.externall_call != true
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'
          cache-dependency-path: '.github/script/requirements.txt'

      - name: Install python dependencies
        if: inputs.externall_call != true
        run: |
          python -m pip install --upgrade pip
          pip install -r .github/script/requirements.txt

      - name: Get Slack user
        if: inputs.externall_call != true
        id: get-slack-user-id
        env:
          USER_MAPPING: ${{ vars.SLACK_USER_MAPPING }}
        run: |
          slack_user=$(echo ${USER_MAPPING} | jq -r '. | ."${{ github.actor }}"')
          echo "SLACK_USER=$slack_user" >> $GITHUB_ENV

      - name: Echo task information
        if: inputs.externall_call != true
        env:
          github_actor: ${{ github.actor }}
          branch: ${{ env.BRANCH_NAME }}
          github_run_id: ${{ github.run_id }}
          SLACK_USER: ${{ env.SLACK_USER }}
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        run: |
          echo "###  HTTP Header during testing:" >> $GITHUB_STEP_SUMMARY
          echo "####  x-test-env : cloud-universe#${{ env.DEPLOY_TYPE }}-${{ env.BRANCH_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo "###  AWS cli command to enter container :" >> $GITHUB_STEP_SUMMARY
          TASK_ARN=$(aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }} --service-name ${{ env.SERVICE_NAME }} --query "taskArns[0]")
          command="aws ecs execute-command --cluster ${{ env.ECS_CLUSTER }} --container controller --task \"$(basename $TASK_ARN) --interactive --command \"/bin/bash\""
          echo $command >> $GITHUB_STEP_SUMMARY
          python ./.github/script/send_slack_message.py
