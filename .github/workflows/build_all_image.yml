name: Build All image

env:
  AWS_REGION: us-west-1

on:
  workflow_dispatch:
  schedule:
    - cron: "0 14 * * *" #UTC

permissions:
  pull-requests: write
  contents: write
  checks: write
  issues: read
  id-token: write

jobs:
  upload-tf:
    name: Upload terraform to S3
    environment: dev-new
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: us-east-1

    - name: Upload to S3
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        cd terraform
        tar -czvf terraform.tar.gz ./*
        aws s3 cp terraform.tar.gz s3://tgcloud-provider-terraform/aws/$SHORT_SHA/terraform.tar.gz --follow-symlinks --no-progress

  build-tgagent-image:
    name: Build TGAgent docker image
    runs-on: ubuntu-latest
    environment: dev-new
    outputs:
      tg_image: ${{steps.get-image.outputs.TG_IMAGE}}
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=tginternal/cloud-agent:${SHORT_SHA::6}
        echo "TG_IMAGE=$IMAGE" >> $GITHUB_ENV
        echo "TG_IMAGE=$IMAGE" >> $GITHUB_OUTPUT

    - name: Write secret to file
      run: |
        SECRET_FILE=$(mktemp)
        echo ${{ secrets.SECRET_ENCRYPTION_KEY }} > "$SECRET_FILE"
        chmod 600 "$SECRET_FILE"
        echo "SECRET_FILE=$SECRET_FILE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: |
            ${{ env.TG_IMAGE }}
            tginternal/cloud-agent:cloud-dev
        secret-files: |
            secret_encryption_key=${{ env.SECRET_FILE }}

  build-ca-image:
    name: Build CA docker image
    runs-on: ubuntu-latest
    environment: dev-new
    outputs:
      sha: ${{steps.get-image.outputs.sha}}
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=tginternal/cloud-cluster-agent:${SHORT_SHA::6}
        echo "CA_IMAGE=$IMAGE" >> $GITHUB_ENV
        echo "sha=${SHORT_SHA::6}" >> $GITHUB_OUTPUT

    - name: Write secret to file
      run: |
        SECRET_FILE=$(mktemp)
        echo ${{ secrets.SECRET_ENCRYPTION_KEY }} > "$SECRET_FILE"
        chmod 600 "$SECRET_FILE"
        echo "SECRET_FILE=$SECRET_FILE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile.clusteragent
        push: true
        tags: |
            ${{ env.CA_IMAGE }}
        secret-files: |
            secret_encryption_key=${{ env.SECRET_FILE }}
  
  build-autostart-image:
    name: Build AutoStart docker image
    runs-on: ubuntu-latest
    environment: dev-new
    outputs:
      sha: ${{steps.get-image.outputs.sha}}
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ vars.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=tginternal/cloud-auto-start-handler:${SHORT_SHA::6}
        echo "AUTOSTART_IMAGE=$IMAGE" >> $GITHUB_ENV
        echo "sha=${SHORT_SHA::6}" >> $GITHUB_OUTPUT

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./Dockerfile.autostart
        push: true
        tags: |
            ${{ env.AUTOSTART_IMAGE }}

  build-agent-gateway-image:
    name: Build AGW docker image
    strategy:
      matrix:
        environment: [production, dev-new]
    environment: ${{ matrix.environment }}
    runs-on: ubuntu-latest
    outputs:
      sha: ${{steps.get-image.outputs.sha}}
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=${{ steps.login-ecr.outputs.registry }}/cloud-agent-gateway:${SHORT_SHA::6}
        echo "AGW_IMAGE=$IMAGE" >> $GITHUB_ENV
        echo "sha=${SHORT_SHA::6}" >> $GITHUB_OUTPUT

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./agentgateway/docker/Dockerfile.agentgateway
        push: true
        tags: |
            ${{ env.AGW_IMAGE }}

  build-license-server-image:
    name: Build License server docker image
    strategy:
      matrix:
        environment: [production, uat]
    environment: ${{ matrix.environment }}
    runs-on: ubuntu-latest
    outputs:
      sha: ${{steps.get-image.outputs.sha}}
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
        cache: 'pip'

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=${{ steps.login-ecr.outputs.registry }}/license-server:${SHORT_SHA::6}
        echo "LS_IMAGE=$IMAGE" >> $GITHUB_ENV
        pip install PyYAML
        pip install jinja2-cli
        cd licenseserver
        jinja2 LICENSESERVER_CONFIG.yaml --format=yaml --strict -D LICENSE_SMTP_USERNAME=${{ vars.LICENSE_SMTP_USERNAME }} -D LICENSE_SMTP_FROM=${{ vars.LICENSE_SMTP_FROM }} -D LICENSE_SMTP_PASSWORD=${{ secrets.LICENSE_SMTP_PASSWORD }} -D DB_HOST=${{ vars.DB_HOST }} -D DB_PASSWORD=${{ secrets.DB_PASSWORD }} -D LICENSE_AES_KEY=${{ secrets.LICENSE_AES_KEY }} -D LICENSE_AES_IV=${{ secrets.LICENSE_AES_IV }} -D LICENSE_JWT_PRIVATE_KEY_B64=${{ secrets.LICENSE_JWT_PRIVATE_KEY_B64 }} -o licenseserverconfig.yaml

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./licenseserver/Dockerfile
        push: true
        build-args: |
          GITHUB_TOKEN=${{ secrets.GST_BOT_TOKEN }}
          GITHUB_USER=gstbot
        tags: |
          ${{ env.LS_IMAGE }}

  update_uat:
    name: Update UAT AGW
    environment: uat
    runs-on: ubuntu-latest
    needs: [build-agent-gateway-image]
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-east-1

      - name: Download task definition
        run: |
          agw_image=${{ vars.ECR_REGISTRY }}/cloud-agent-gateway:${{ needs.build-agent-gateway-image.outputs.sha }}
          echo "agw_image=${agw_image}" >> $GITHUB_ENV
          aws ecs describe-task-definition --task-definition agent_gateway_uat --query taskDefinition > agw-task-definition.json

      - name: Fill in the AGW image ID in the Amazon ECS task definition
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        id: agw-task-def
        with:
          task-definition: agw-task-definition.json
          container-name: agw
          image: ${{ env.agw_image }}

      - name: Deploy Amazon AGW ECS task definition for UAT
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.agw-task-def.outputs.task-definition }}
          service: agent_gateway_uat
          cluster: TGCloud-uat
          wait-for-service-stability: true

  update_uat_tgagent:
    needs: [build-tgagent-image, update_uat]
    name: Update UAT tgAgentImage Config
    uses: tigergraph/cloud-universe/.github/workflows/update_db_config.yml@main
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    with:
      environment: uat
      configKey: tgAgentImage
      configValue: '{"value": "${{ needs.build-tgagent-image.outputs.tg_image }}"}'
    secrets: inherit

  update_uat_byoc_config:
    needs: [build-ca-image, update_uat, upload-tf]
    name: Update UAT ByocConfig
    uses: tigergraph/cloud-universe/.github/workflows/update_db_config.yml@main
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    with:
      environment: uat
      configKey: byocConfig
      configValue: '{"env":"uat","esHost":"es-uat.tgcloud-dev.com","agwAddr":"us-west-1-uat.agw.tgcloud-dev.com:443","agwToken":"qwerty","baseDomain":"tgcloud-dev.com","systemOrgID":"org_DXEi8MYrA9HXOUQB","controllerURL":"https://api-v4-uat.tgcloud-dev.com/controller","teleportToken":"095dfb48de1ffc6d5309455ffc650cb9","autoStartToken":"f72ea3e40984f1067ed300b6f2a478ff","route53RoleARN":"arn:aws:iam::022200819440:role/route53-role","metricsProxyHost":"metrics-proxy-uat.tgcloud-dev.com","tfTemplateVersion":"${{ needs.build-ca-image.outputs.sha }}","tokenTTLInSeconds":1209600,"prometheusPassword":"QeY3NFQxu5qx","prometheusUsername":"tgcloud_observability","tgOperatorImageTag":"${{ vars.TG_OPERATOR_IMAGE }}","controlPlaneRoleARN":"arn:aws:iam::590183795677:role/security-group-role","clusterAgentImageTag":"${{ needs.build-ca-image.outputs.sha }}","clusterAgentHelmVersion":"","autoStartHandlerImageTag":"${{ vars.AUTO_START_IMAGE }}","clusterAgentReplicaCount":1,"prometheusRemoteWriteURL":"https://prom-uat.tgcloud-dev.com/api/v1/write","cpReconcilerDisableDryRun":true,"autoStartHandlerHelmVersion":"","clusterAgentImagePullPolicy":"IfNotPresent","autoStartHandlerReplicaCount":1,"clusterAgentHeartbeatInterval":10,"autoStartHandlerImagePullPolicy":"IfNotPresent"}'
    secrets: inherit

  update_uat_component_versions:
    needs: [build-ca-image, update_uat, upload-tf]
    name: Update UAT componentVersions Config
    runs-on: ubuntu-latest
    environment: uat
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Update UAT componentVersions Config
        run: |
          curl -X POST "https://api-v4-uat.tgcloud-dev.com/controller/admin/cloud_providers/upgrade/f24efb64-7834-4718-9a28-aef9549cef52" \
          -H "Authorization: ApiKey ${{ secrets.ADMIN_API_KEY }}" \
          -H "Content-Type: application/json" \
          -d '{
            "components": [
              {
                "name": "clusteragent",
                "version": "${{ needs.build-ca-image.outputs.sha }}"
              },
              {
                "name": "autostarthandler",
                "version": "${{ vars.AUTO_START_IMAGE }}"
              },
              {
                "name": "tgoperator",
                "version": "${{ vars.TG_OPERATOR_IMAGE }}"
              },
              {
                "name": "tftemplateversion",
                "version": "${{ needs.build-ca-image.outputs.sha }}"
              }
            ]
          }'

  build-obs-image:
    name: Build OBS docker image
    strategy:
      matrix:
        environment: [production, dev-new]
    environment: ${{ matrix.environment }}
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code (manual trigger)
      if: github.event_name == 'workflow_dispatch'
      uses: actions/checkout@v4

    - name: Checkout code
      uses: actions/checkout@v4
      if: github.event_name != 'workflow_dispatch'
      with:
        ref: ${{ vars.UAT_DEPLOY_BRANCH }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ vars.EXECUTE_ROLE }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get Image tag
      id: get-image
      run: |
        SHORT_SHA=$(git rev-parse --short=6 HEAD)
        IMAGE=${{ steps.login-ecr.outputs.registry }}/metric-proxy:${SHORT_SHA::6}
        echo "OBS_IMAGE=$IMAGE" >> $GITHUB_ENV

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./observability/Dockerfile
        push: true
        tags: |
            ${{ env.OBS_IMAGE }}
