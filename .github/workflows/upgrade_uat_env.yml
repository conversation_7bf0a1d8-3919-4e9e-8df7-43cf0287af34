name: Upgrade UAT environment

env:
  AWS_REGION: us-east-1
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SHA: ${{ github.sha }}
  ECR_REPOSITORY: tgcloudv4-uat
  ECS_SERVICE: tgcloud_uat                    # set this to your Amazon ECS service name
  ECS_CLUSTER: TGCloud-uat                     # set this to your Amazon ECS cluster name
  ECS_TASK_DEFINITION: tgcloud_uat                   # set this to the family name of Amazon ECS task definition
  AWS_S3_BUCKET: tgcloud-test-v4

# Permission can be added at job level or workflow level
permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout

on:
  workflow_run:
    workflows: [Post Merge Test]
    branches: [main, release_*]
    types: [completed]
  workflow_dispatch:
  schedule:
    - cron: "30 14 * * *" #UTC

jobs:
  setup:
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
    name: Build Go
    environment: uat
    runs-on: ubuntu-latest
    outputs:
      image: ${{steps.build-image.outputs.myimage}}
    steps:
      - name: Check out code into the source code repository(manual trigger)
        uses: actions/checkout@v4
        if: github.event_name == 'workflow_dispatch'

      - name: Check out code into the source code repository
        uses: actions/checkout@v4
        if: github.event_name != 'workflow_dispatch'
        with:
          ref: ${{ vars.UAT_DEPLOY_BRANCH }}

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install dependencies
        run: |
          pip install PyYAML
          pip install jinja2-cli

      - name: Replace Config
        run: |
          cd docker/uat
          jinja2 BILLING_MANAGER_CONFIG.yml --format=yaml --strict -D DBDSN=${{ secrets.DBDSN }} -D AMBERFLO_KEY=${{ secrets.AMBERFLO_KEY }} -o billing-manager.yaml
          jinja2 CONTROLLER_CONFIG.yml --format=yaml --strict -D ADMIN_API_KEY=${{ secrets.ADMIN_API_KEY }} -D ES_INTERNAL_URL=${{ vars.ES_INTERNAL_URL }}  -D PROM_PASSWORD=${{ secrets.PROM_PASSWORD }} -D KMS_KEY_ID=${{ secrets.KMS_KEY_ID }} -D GOOGLE_PRIVATE_KEY="${{ secrets.GOOGLE_PRIVATE_KEY }}" -D GOOGLE_PRIVATE_KEY_ID=${{ secrets.GOOGLE_PRIVATE_KEY_ID }} -D UDF_ACCESS_TOKEN=${{ secrets.UDF_ACCESS_TOKEN }} -D AUTH_WEBHOOK=${{ secrets.AUTH_WEBHOOK }} -D AUTO_START=${{ secrets.AUTO_START }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D STRIPE_SECRET=${{ secrets.STRIPE_SECRET }} -D SOLUTION_PK="${{ secrets.SOLUTION_PK }}" -D DB_PASSWORD=${{ secrets.DB_PASSWORD }}  -D DBDSN=${{ secrets.DBDSN }} -D GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }} -o config
          jinja2 RESOURCE_MANAGER_CONFIG.yml --format=yaml --strict -D AUTO_START=${{ secrets.AUTO_START }} -D PULL_SECRET=${{ secrets.PULL_SECRET }} -D LICENSE=${{ secrets.LICENSE }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D K8S_CLUSTER_TOKEN=${{ secrets.K8S_CLUSTER_TOKEN }} -D DBDSN=${{ secrets.DBDSN }} -o resource-manager.yaml
          jinja2 FILEBEAT.yml --format=yaml --strict -D ES_HOST=$(echo ${{ vars.ES_INTERNAL_URL }} | sed 's#^http://##' ) -o filebeat.yml

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy

      - name: Build Go Targets
        env:
          BRANCH_NAME: ${{ vars.UAT_DEPLOY_BRANCH }}
        run: make go-targets -j5

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: init build
        id: build-image
        run: |
          cd docker/uat
          cp -r ../../deployment/portal/transfer/teleport/setup_teleport.sh .
          cp -r ../../release/controller .
          IMAGE=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:${SHA::6}
          echo "IMAGE=$IMAGE" >> $GITHUB_ENV
          echo "myimage=$(echo $IMAGE | base64 -w0 | base64 -w0)" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build, tag, and push image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          push: true
          pull: true
          tags: ${{ env.IMAGE }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache
          cache-to: type=registry,image-manifest=true,oci-mediatypes=true,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache,mode=max
          context: docker/uat/.
          file: docker/uat/Dockerfile

  deploy:
    name: Deploy
    environment: uat
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Download task definition
        run: |
            image=$(echo ${{ needs.setup.outputs.image }} | base64 -di | base64 -di)
            echo "image=${image}" >> $GITHUB_ENV
            aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEFINITION }} --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: controller
          image: ${{ env.image }}
          environment-variables: |
            BRANCH=${{ env.BRANCH_NAME }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

  api-test:
    name: API Test
    needs: deploy
    environment: uat
    runs-on: ubuntu-22.04
    steps:
      - name: Check out code into the source code repository(manual trigger)
        uses: actions/checkout@v4
        if: github.event_name == 'workflow_dispatch'

      - name: Check out code into the source code repository
        uses: actions/checkout@v4
        if: github.event_name != 'workflow_dispatch'
        with:
          ref: ${{ vars.UAT_DEPLOY_BRANCH }}

      - name: Run API test
        run: |
          cd tests/
          sudo bash run.sh -E https://portal.tgcloud-dev.com -M smokerun

      - name: configure aws credentials
        if: always()
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Generate API Allure report and upload it to S3
        if: always()
        run: |
          wget --no-verbose -O /tmp/allure-commandline.zip https://repo.maven.apache.org/maven2/io/qameta/allure/allure-commandline/2.19.0/allure-commandline-2.19.0.zip && unzip /tmp/allure-commandline.zip
          mkdir tests/allure-report
          allure-2.19.0/bin/allure generate tests/alluredir/cloud_api -o tests/allure-report
          aws s3 sync tests/allure-report s3://${{ env.AWS_S3_BUCKET }}/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api --follow-symlinks --delete
          echo "### API Test Report URL:" >> $GITHUB_STEP_SUMMARY
          echo "http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cp -r /tmp/e2e/log tests/ 2>/dev/null || :
          cp -r /tmp/tools_e2e tests/log/ 2>/dev/null || :
          if [ -n "$(find tests/junit -maxdepth 1 -name 'test-results*.xml' 2>/dev/null)" ]; then
              echo "Files matching 'test-results*.xml' found in tests/junit directory."
              ls -l tests/junit/
          else
              echo "No files matching 'test-results*.xml' found in tests/log directory."
              mkdir -p tests/junit
              touch tests/junit/test-results.xml
          fi

      - name: Upload API test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cloud-test-api
          path: |
            tests/environment.properties
            tests/prepare_env_log*
            tests/junit/test-results*.xml
            tests/latest_logs/
            tests/alluredir/
            tests/data/log/*.log
            tests/log/

  performance-test:
    name: Performance Tests
    needs: deploy
    environment: uat
    runs-on: ubuntu-latest
    steps:
      - run: |
          echo "Performance Tests, TBD"

  security-scan:
    name: Security Scan
    needs: deploy
    environment: uat
    runs-on: ubuntu-latest
    steps:
      - run: |
          echo "Security Scan, TBD"

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [api-test]
    if: always() && (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch')
    steps:
      - if: needs.api-test.result == 'success'
        run: |
          echo "api_test_status=:large_green_circle:" >> $GITHUB_ENV
          echo "sha=${SHA::6}" >> $GITHUB_ENV

      - if: needs.api-test.result == 'skipped' || needs.api-test.result == 'failure'
        run: |
          echo "api_test_status=:red_circle:" >> $GITHUB_ENV
          echo "sha=${SHA::6}" >> $GITHUB_ENV

      - name: Slack Message
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "<Controller> Upgrade UAT Env Result ${{ env.api_test_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ env.BRANCH_NAME }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ github.sha }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Operator tag:*\n${{ vars.TG_OPERATOR_IMAGE }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*AutoStart tag:*\n${{ vars.AUTO_START_IMAGE }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*CA/TGAGENT/AGW tag:*\n${{ env.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "plain_text",
                  "text": "API Test status: ${{ env.api_test_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html|View API Test Report>"
                }
              },
              {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Deploy Environment:* <https://portal.tgcloud-dev.com|View Environment>"
                  },
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.CLOUD_DEPLOY_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )
