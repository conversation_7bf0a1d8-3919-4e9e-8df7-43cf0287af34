name: Post Merge Test

on:
  push:
    branches:
      - main
      - release_*
  schedule:
    - cron: "0 14 * * *" #UTC
  workflow_dispatch:

env:
  AWS_REGION : "us-east-1"
  ECR_REPOSITORY: tgcloudv4
  AWS_S3_BUCKET: tgcloud-test-v4
  ECS_TASK_DEFINITION: tgcloud_dev
  ECS_CLUSTER: TGCloud-dev
  ECS_SERVICE: tgcloud_dev
  ECS_COV_SERVICE: service-cov-main
  SHA: ${{ github.sha }}
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SECRET_ENCRYPTION_KEY: ${{ secrets.SECRET_ENCRYPTION_KEY }}

# Permission can be added at job level or workflow level    
permissions:
  id-token: write
  contents: write
  issues: write
  checks: write
  pull-requests: write

jobs:
  setup:
    name: Build
    environment: dev-new
    runs-on: ubuntu-20.04
    outputs:
      image: ${{ steps.build-image.outputs.myimage }}
      BUILD_NUMBER: ${{ steps.build-go.outputs.BUILD_NUMBER }}
    steps:
      - name: Generate build number
        id: get-buildnumber-step
        if: github.event_name != 'schedule' && github.event_name != 'workflow_dispatch'
        uses: onyxmueller/build-tag-number@v1
        with:
          token: ${{secrets.GITHUB_TOKEN}}
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy

      - name: Build Go Targets
        id: build-go
        if: github.event_name != 'schedule' && github.event_name != 'workflow_dispatch'
        env:
          BUILD_NUMBER: ${{ steps.get-buildnumber-step.outputs.build_number }}
        run: |
          make go-targets -j5
          echo "BUILD_NUMBER=${{ steps.get-buildnumber-step.outputs.build_number }}" >> $GITHUB_OUTPUT

      - name: Build Go Targets With Cover
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        id: build-go-cov
        run: |
          make proto
          make build-with-cover

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install dependencies
        run: |
          pip install PyYAML
          pip install jinja2-cli

      - name: Replace Config
        run: |
          cd docker/dev
          jinja2 BILLING_MANAGER_CONFIG.yml --format=yaml --strict -D DBDSN=${{ secrets.DBDSN }} -D AMBERFLO_KEY=${{ secrets.AMBERFLO_KEY }} -o billing-manager.yaml
          jinja2 CONTROLLER_CONFIG.yml --format=yaml --strict -D ADMIN_API_KEY=${{ secrets.ADMIN_API_KEY }} -D ES_INTERNAL_URL=${{ vars.ES_INTERNAL_URL }}  -D PROM_PASSWORD=${{ secrets.PROM_PASSWORD }} -D KMS_KEY_ID=${{ secrets.KMS_KEY_ID }} -D GOOGLE_PRIVATE_KEY="${{ secrets.GOOGLE_PRIVATE_KEY }}" -D GOOGLE_PRIVATE_KEY_ID=${{ secrets.GOOGLE_PRIVATE_KEY_ID }} -D UDF_ACCESS_TOKEN=${{ secrets.UDF_ACCESS_TOKEN }} -D AUTH_WEBHOOK=${{ secrets.AUTH_WEBHOOK }} -D AUTO_START=${{ secrets.AUTO_START }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D STRIPE_SECRET=${{ secrets.STRIPE_SECRET }} -D SOLUTION_PK="${{ secrets.SOLUTION_PK }}" -D DB_PASSWORD=${{ secrets.DB_PASSWORD }}  -D DBDSN=${{ secrets.DBDSN }} -D GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }}  -o config
          jinja2 RESOURCE_MANAGER_CONFIG.yml --format=yaml --strict -D AUTO_START=${{ secrets.AUTO_START }} -D K8S_WEST_CLUSTER_TOKEN=${{ secrets.K8S_WEST_CLUSTER_TOKEN }} -D PULL_SECRET=${{ secrets.PULL_SECRET }} -D LICENSE=${{ secrets.LICENSE }} -D AUTH0_SECRET=${{ secrets.AUTH0_SECRET }} -D M2M_TOKEN=${{ secrets.M2M_TOKEN }} -D K8S_CLUSTER_TOKEN=${{ secrets.K8S_CLUSTER_TOKEN }} -D DBDSN=${{ secrets.DBDSN }} -o resource-manager.yaml
          jinja2 FILEBEAT.yml --format=yaml --strict -D ES_HOST=$(echo ${{ vars.ES_INTERNAL_URL }} | sed 's#^http://##' ) -o filebeat.yml

      - name: Upload Go executables
        uses: actions/upload-artifact@v4
        with:
          name: go-targets
          path: release

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: init build
        id: build-image
        run: |
          cd docker/dev
          cp -r ../../deployment/portal/transfer/teleport/setup_teleport.sh .
          cp -r ../../release/controller .
          cp -r ../../deployment/code_coverage/collect_data.sh .
          mkdir -p cloud-universe
          IMAGE=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:${SHA::6}
          echo "IMAGE=$IMAGE" >> $GITHUB_ENV
          echo "myimage=$(echo $IMAGE | base64 -w0 | base64 -w0)" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build, tag, and push image to Amazon ECR
        uses: docker/build-push-action@v6
        with:
          push: true
          pull: true
          tags: ${{ env.IMAGE }}
          cache-from: type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache
          cache-to: type=registry,image-manifest=true,oci-mediatypes=true,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:buildcache,mode=max
          context: docker/dev/.
          file: docker/dev/Dockerfile

  unit-test:
    name: Unit Test
    environment: dev-new
    runs-on: ubuntu-latest
    outputs:
      CC: ${{ steps.coverage-report.outputs.CC }}
    steps:
      - name: Check out code into the source code repository
        uses: actions/checkout@v4

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Go mod tidy
        run: |
          git config --global url."https://${{ secrets.CICD_GITHUB_TOKEN }}@github.com".insteadOf "https://github.com"
          make tidy
          make proto

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: us-west-1

      - name: Unit test
        run: |
          go get github.com/vakenbolt/go-test-report
          go install github.com/vakenbolt/go-test-report
          export GOEXPERIMENT=nocoverageredesign
          go test -v -json -short -coverpkg ./... -coverprofile=coverage.prof ./... > ut.json

      - name: Upload UT report
        if: always()
        run: |
          cat ut.json | go-test-report
          aws s3 cp test_report.html s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/test_report.html --follow-symlinks --no-progress
          echo 'You can check UT report in: https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/test_report.html'

      - name: Generate coverage report
        id: coverage-report
        run: |
          go install github.com/cancue/covreport@latest
          covreport -i ./coverage.prof -o coverage.html -cutlines 80,50
          go tool cover -func=./coverage.prof -o coverage.txt
          num=$(grep ^total: coverage.txt | awk '{print substr($3, 1, length($3)-1)}')
          echo "CC=${num}" >> $GITHUB_ENV
          echo "CC=${num}" >> $GITHUB_OUTPUT
          mkdir coverage
          cp coverage.* coverage/

      - name: Generate coverage badge
        uses: ./.github/workflows/gen_coverage_badge
        with:
          coverage: ${{ env.CC }}
          path: coverage/badges/coverage-statements.svg

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: ut-coverage
          path: coverage

  deploy:
    name: Deploy
    environment: dev-new
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Download task definition
        run: |
            image=$(echo ${{ needs.setup.outputs.image }} | base64 -di | base64 -di)
            echo "image=${image}" >> $GITHUB_ENV
            aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEFINITION }} --query taskDefinition > task-definition.json

      - name: Get the ECS service data
        run: |
          if [ "${{ github.event_name }}" = "schedule" ] || [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "service=${{ env.ECS_COV_SERVICE }}" >> $GITHUB_ENV
            echo "branch=cov-main" >> $GITHUB_ENV
          else
            echo "service=${{ env.ECS_SERVICE }}" >> $GITHUB_ENV
            echo "branch=${{ env.BRANCH_NAME }}" >> $GITHUB_ENV
          fi

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: controller
          image: ${{ env.image }}
          environment-variables: |
            BRANCH=${{ env.branch }}
            DEV_DB=true
            DEPLOY_TYPE=cicd
            DEV_DBDSN=${{ secrets.DBDSN }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 10
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.service }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

  api-test:
    name: API Test
    needs: deploy
    environment: dev-new
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4

      - name: Run API test
        run: |
          if [ "${{ github.event_name }}" = "schedule" ] || [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            bash tests/run_cloud_api.sh post_merge_run "cloud-universe#dev-cov-main"
          else
            bash tests/run_cloud_api.sh smokerun
          fi

      - name: configure aws credentials
        if: always()
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Generate API Allure report and upload it to S3
        if: always()
        run: |
          wget --no-verbose -O /tmp/allure-commandline.zip https://repo.maven.apache.org/maven2/io/qameta/allure/allure-commandline/2.19.0/allure-commandline-2.19.0.zip && unzip /tmp/allure-commandline.zip
          mkdir tests/allure-report
          allure-2.19.0/bin/allure generate tests/alluredir/cloud_api -o tests/allure-report
          aws s3 sync tests/allure-report s3://${{ env.AWS_S3_BUCKET }}/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api --follow-symlinks --delete
          echo "### API Test Report URL:" >> $GITHUB_STEP_SUMMARY
          echo "http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cp -r /tmp/e2e/log tests/ 2>/dev/null || :
          cp -r /tmp/tools_e2e tests/log/ 2>/dev/null || :
          if [ -n "$(find tests/junit -maxdepth 1 -name 'test-results*.xml' 2>/dev/null)" ]; then
              echo "Files matching 'test-results*.xml' found in tests/junit directory."
              ls -l tests/junit/
          else
              echo "No files matching 'test-results*.xml' found in tests/log directory."
              mkdir -p tests/junit
              touch tests/junit/test-results.xml
          fi

      - name: Upload API test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cloud-test-api
          path: |
            tests/environment.properties
            tests/prepare_env_log*
            tests/junit/test-results*.xml
            tests/latest_logs/
            tests/alluredir/
            tests/data/log/*.log
            tests/log/
  
  upload-test:
    name: Upload Test Result
    needs: api-test
    runs-on: ubuntu-latest
    if: always()
    environment: dev-new
    outputs:
      IT_CC: ${{ steps.caculate-coverage.outputs.IT_CC }}
    steps:
      - uses: actions/checkout@v4
      - name: Download UT test log
        uses: actions/download-artifact@v4
        with:
          name: ut-coverage
          path: coverage

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Upload UT coverage report
        run: |
          aws s3 sync coverage/badges s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/ut --follow-symlinks --delete
          echo "#### Full Unit Test Coverage:" >> ${GITHUB_STEP_SUMMARY}
          echo "![coverage](http://${{ env.AWS_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage-statements.svg)" >> ${GITHUB_STEP_SUMMARY}

          aws s3 sync coverage s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut --follow-symlinks --delete
          echo "#### Full Coverage Report(visit in VPN):" >> ${GITHUB_STEP_SUMMARY}
          echo "https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage.html" >> ${GITHUB_STEP_SUMMARY}

      - name: Update UT coverage badge
        run: |
          aws s3 sync coverage/badges s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-badges/${{ env.BRANCH_NAME }}/ut --follow-symlinks --delete
          aws s3 sync coverage s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ env.BRANCH_NAME }}/ut --follow-symlinks --delete

      - name: Set up Go
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'

      - name: Go make proto
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        run: make proto

      - name: Caculate integration test code coverage
        id: caculate-coverage
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        run: |
          TASK_ARN=$(aws ecs list-tasks --cluster ${{ env.ECS_CLUSTER }} --service-name ${{env.ECS_COV_SERVICE}} --query "taskArns[0]" --output=text)
          aws ecs execute-command --cluster ${{ env.ECS_CLUSTER }} --container controller --task $TASK_ARN --interactive --command "bash collect_data.sh ${{ env.AWS_S3_BUCKET }} ${{ env.SHA }}"
          aws s3api get-object --bucket ${{ env.AWS_S3_BUCKET }} --key coverdata/${{ env.SHA }}/coverdata.tar.gz coverdata.tar.gz
          tar xzvf coverdata.tar.gz
          mkdir -p it-coverage
          go tool covdata textfmt -i=covdatafiles -o it-coverage/coverage.prof
          cmd=""
          IFS=',' read -ra files <<< "${{ vars.IT_COVERAGE_IGNORE_FILES }}"
          for file in "${files[@]}"; do
            cmd+="-e $file "
          done
          echo "$cmd"
          eval "grep -v $cmd it-coverage/coverage.prof > it-coverage/filter_coverage.prof"
          go install github.com/cancue/covreport@latest
          covreport -i it-coverage/filter_coverage.prof -o it-coverage/coverage.html -cutlines 80,50
          go tool cover -func=it-coverage/filter_coverage.prof -o it-coverage/coverage.txt

          num=$(grep ^total it-coverage/coverage.txt | awk '{print substr($3, 1, length($3)-1)}')
          echo "IT_CC=${num}" >> $GITHUB_ENV
          echo "IT_CC=${num}" >> $GITHUB_OUTPUT

      - name: Generate IT coverage badge
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        uses: ./.github/workflows/gen_coverage_badge
        with:
          coverage: ${{ env.IT_CC }}
          path: it-coverage/badges/coverage-statements.svg

      - name: Upload coverage
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        run: |
          aws s3 sync it-coverage/badges s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/it --follow-symlinks --delete
          echo "#### Integration Test Coverage:" >> $GITHUB_STEP_SUMMARY
          echo "![coverage](http://${{ env.AWS_S3_BUCKET }}.s3-website-us-west-1.amazonaws.com/cloud-universe-badges/${{ github.run_id }}-${{ github.run_attempt }}/it/coverage-statements.svg)" >> $GITHUB_STEP_SUMMARY

          aws s3 sync it-coverage s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/it --follow-symlinks --delete
          echo "#### IT Coverage Report(visit in VPN):" >> $GITHUB_STEP_SUMMARY
          echo "https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/it/coverage.html" >> $GITHUB_STEP_SUMMARY

      - name: Update IT coverage badge in main
        if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
        run: |
          aws s3 sync it-coverage/badges s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-badges/main/it --follow-symlinks --delete
          aws s3 sync it-coverage s3://${{ env.AWS_S3_BUCKET }}/cloud-universe-coverage/main/it --follow-symlinks --delete

  create-release:
    name: Create Release Draft
    needs: [setup]
    if: always() && github.event_name != 'schedule' && github.event_name != 'workflow_dispatch' && github.ref_name == vars.UAT_DEPLOY_BRANCH
    outputs:
      html_url: ${{ steps.create-draft.outputs.html_url }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/download-artifact@v4
        name: Download Go Targets
        with:
          name: go-targets
          path: release

      - name: Package Terrafrom Scipts
        run: |
          cd terraform
          tar -czvf terraform.tar.gz ./*

      - uses: ncipollo/release-action@v1.14.0
        name: Create Release Draft
        id: create-draft
        with:
          artifacts: "release/controller, terraform/terraform.tar.gz"
          replacesArtifacts: false
          draft: true
          generateReleaseNotes: true
          body: "build-number: ${{ needs.setup.outputs.BUILD_NUMBER }} \n sha: ${{ github.sha }}"
          commit: ${{ github.sha }}
          tag: Controller-${{ needs.setup.outputs.BUILD_NUMBER }}

      - name: Delete drafts older than 4 week
        uses: hugo19941994/delete-draft-releases@v1.0.1
        with:
          threshold: 30d
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_GITHUB_TOKEN }}

  slack-message:
    name: Send slack message
    runs-on: ubuntu-latest
    needs: [api-test, upload-test, create-release, setup, unit-test]
    if: always() && github.event_name != 'schedule' && github.event_name != 'workflow_dispatch' && github.ref_name == vars.UAT_DEPLOY_BRANCH
    steps:
      - if: needs.create-release.result == 'success'
        run: |
          echo "publish_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.create-release.result == 'skipped' || needs.create-release.result == 'failure'
        run: |
          echo "publish_status=:red_circle:" >> $GITHUB_ENV

      - if: needs.api-test.result == 'success'
        run: |
          echo "api_test_status=:large_green_circle:" >> $GITHUB_ENV
  
      - if: needs.api-test.result == 'skipped' || needs.api-test.result == 'failure'
        run: |
          echo "api_test_status=:red_circle:" >> $GITHUB_ENV
          echo "publish_status=:red_circle:" >> $GITHUB_ENV

      - name: Slack Message
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "<Controller_${{ needs.setup.outputs.BUILD_NUMBER }}> Post Merge Test Result ${{ env.publish_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ env.BRANCH_NAME }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Actor:*\n${{ github.actor }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Release Version:*\n<${{ needs.create-release.outputs.html_url }}|Controller_${{ needs.setup.outputs.BUILD_NUMBER }}>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ github.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Full UT Code Coverage*: ${{ needs.unit-test.outputs.CC }}%   <https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage.html|View Full Code Coverage Report>"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "plain_text",
                  "text": "API Test status: ${{ env.api_test_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html|View API Test Report>"
                }
              },
              {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Deploy Environment:* <https://test-portal.tgcloud-dev.com|View Environment>"
                  },
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.CLOUD_DEPLOY_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )

  cov-slack-message:
    name: Send slack cov message
    runs-on: ubuntu-latest
    needs: [api-test, upload-test, setup, unit-test]
    if: always() && (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch')
    steps:
      - if: needs.api-test.result == 'success'
        run: |
          echo "api_test_status=:large_green_circle:" >> $GITHUB_ENV

      - if: needs.api-test.result == 'skipped' || needs.api-test.result == 'failure'
        run: |
          echo "api_test_status=:red_circle:" >> $GITHUB_ENV
          echo "MENTION=<@U037NPFMAES>" >> $GITHUB_ENV

      - name: Slack Message
        run: |
          slackMessage='{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "(Daily Run) Controller Cov Test Result ${{ env.api_test_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n${{ env.BRANCH_NAME }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sha:*\n${{ github.sha }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Full UT Code Coverage*: ${{ needs.unit-test.outputs.CC }}%   <https://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/cloud-universe-coverage/${{ github.run_id }}-${{ github.run_attempt }}/ut/coverage.html|View Full Code Coverage Report>"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "plain_text",
                  "text": "API Test status: ${{ env.api_test_status }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Full IT Code Coverage*: ${{ needs.upload-test.outputs.IT_CC }}%   <http://${{ env.AWS_S3_BUCKET }}.s3.us-west-1.amazonaws.com/allure-report/${{ github.run_id }}-${{ github.run_attempt }}/api/index.html|View API Test Report> ${{ env.MENTION }}"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "<https://github.com/tigergraph/cloud-universe/actions/runs/${{ github.run_id }}|View Github Action>"
                }
              }
            ]
          }'
          curl -X POST ${{ secrets.CLOUD_DEPLOY_SLACK_APP_WEBHOOK_URL }} -H 'Content-type:application/json' \
          --data @<(cat <<EOF
          $slackMessage
          EOF
          )
