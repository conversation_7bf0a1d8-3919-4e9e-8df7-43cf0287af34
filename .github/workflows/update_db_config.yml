name: Update Database Config
on:
  workflow_call:
    inputs:
      environment:
        description: 'The environment you want to update configurations'
        type: string
      configKey:
        description: 'The config key you want to update'
        required: true
        type: string
      configValue:
        description: 'Fill in the values you want to update (json format)'
        required: true
        type: string
      branch:
        description: 'Fill in the branch you want to set up'
        default: main
        type: string

  workflow_dispatch:
    inputs:
      environment:
        description: 'Choose the environment you want to update configurations'
        type: environment
      configKey:
        description: 'Choose the config key you want to update'
        required: true
        type: choice
        options:
          - addOnsMetaList
          - requestAddOnsMetaList
          - tgVersions
          - workspaceTypes
          - gsqlReservedKeywords
          - tgAgentImage
          - componentVersions
      configValue:
        description: 'Fill in the values you want to update (json format)'
        required: true
        type: string
      branch:
        description: 'Fill in the branch you want to set up'
        default: main
        type: string

permissions:
  # The "id-token: write" permission is required or Machine ID will not be able to authenticate with the cluster.
  id-token: write
  contents: read

jobs:
  update-db-config:
    environment: ${{ inputs.environment }}
    name: Update Database Config
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Fetch Teleport binaries
        uses: teleport-actions/setup@v1
        with:
          version: 16.4.7

      - name: Fetch credentials using Machine ID
        id: auth
        uses: teleport-actions/auth@v1
        with:
          proxy: tigergraph.teleport.sh:443
          token: github-token
          certificate-ttl: 1h

      - name: Escaping the input value
        run: |
          escaped_json=$(echo '${{inputs.configValue}}' | sed 's/"/\\"/g')
          echo "escaped_json=$escaped_json" >> $GITHUB_ENV

      - name: Update Configurations
        id: update
        run: |
          if [[ "${{inputs.configKey}}" != "componentVersions" ]]; then
            SQL_COMMAND="INSERT INTO configurations VALUES(now(),now(),'${{inputs.configKey}}','${{env.escaped_json}}','${{inputs.branch}}');"
          else
            SQL_COMMAND="UPDATE cloud_provider_resources set component_versions='${{env.escaped_json}}' WHERE deleted_at is null;"
          fi
          echo $SQL_COMMAND
          tsh status
          escaped_json=$(echo "$SQL_COMMAND" | sed 's/"/\\"/g')
          tsh ssh ubuntu@${{ vars.DB_NAME }} << EOF
          echo "$escaped_json" | psql ${{secrets.DBDSN}}
          exit
          EOF
