name: Clean dev working environment

on:
  pull_request:
    types: [unlabeled, closed]
  workflow_call:
    inputs:
      externall_call:
        description: 'To distinguish workflow_call from regular push'
        type: boolean
        required: false
        default: true

env:
  AWS_REGION : "us-east-1" #Change to reflect your Region
  ECS_CLUSTER: TGCloud-dev
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

# Permission can be added at job level or workflow level
permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout

jobs:
  setup:
    environment: dev-new
    name: Clean dev env
    if: >-
      github.event.label.name == 'cicd: dev env :flashlight:' || (github.event_name == 'pull_request' && github.event.action == 'closed') || inputs.externall_call
    runs-on: ubuntu-latest
    steps:
      - name: Check Deploy Type
        run: |
          if [ "${{ inputs.externall_call }}" = "true" ]; then
            echo "SERVICE_NAME=e2e-${{ env.<PERSON><PERSON><PERSON>_NAME }}" >> $GITHUB_ENV
            echo "TARGET_GROUP_PREFIX=e2e" >> $GITHUB_ENV
          else
            echo "SERVICE_NAME=service-${{ env.BRANCH_NAME }}" >> $GITHUB_ENV
            echo "TARGET_GROUP_PREFIX=cicd" >> $GITHUB_ENV
          fi

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.EXECUTE_ROLE }}
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}

      - name: Check if ECS service exists
        id: check_service
        run: |
          TARGET_GROUP_NAME=$(echo ${{ env.TARGET_GROUP_PREFIX }}-${{ env.BRANCH_NAME }} | sed 's/_/-/g')
          echo "TARGET_GROUP_NAME=$TARGET_GROUP_NAME" >> $GITHUB_ENV
          FAIL_REASON=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.SERVICE_NAME }} --query "failures[0].reason")
          if [ "$FAIL_REASON" = "\"MISSING\"" ]; then
            echo "CLEAN=false" >> $GITHUB_ENV
            exit 0
          fi
          SERVICE_STATUS=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.SERVICE_NAME }} --query "services[0].status")
          if [ "$SERVICE_STATUS" = "\"INACTIVE\"" ]; then
            echo "CLEAN=false" >> $GITHUB_ENV
          else
            echo "CLEAN=true" >> $GITHUB_ENV
          fi

      - name: Get AWS ELB Target Group
        id: get-target-group
        if: env.CLEAN == 'true'
        uses: icalia-actions/aws-configure-elb-target-group@v0.0.3
        with:
          name: ${{ env.TARGET_GROUP_NAME }}

      - name: Delete ELB rule
        if: env.CLEAN == 'true'
        run: |
          echo ${{ steps.get-target-group.outputs.target-group-arn }}
          RULE_ARNS=$(aws elbv2 describe-rules --listener-arn ${{ vars.ELB_LISTENER }} --output text --query "Rules[].{RuleArn: RuleArn, Actions:Actions[?TargetGroupArn=='${{ steps.get-target-group.outputs.target-group-arn }}'].TargetGroupArn }" | grep -B 1 ACTIONS | grep -v ACTIONS)
          for RULE_ARN in $RULE_ARNS; do
            aws elbv2 delete-rule --rule-arn "$RULE_ARN"
          done

      - name: Delete AWS ECS Service
        if: env.CLEAN == 'true'
        uses: icalia-actions/aws-delete-ecs-service@v0.0.1
        with:
          name: ${{ env.SERVICE_NAME }}
          cluster: ${{ env.ECS_CLUSTER }}

      - name: Delete AWS ELB Target Group
        if: env.CLEAN == 'true'
        uses: icalia-actions/aws-delete-elb-target-group@v0.0.1
        with:
          target-group: ${{ env.TARGET_GROUP_PREFIX }}-${{ env.BRANCH_NAME }}

      - name: Fetch Teleport binary
        if: env.CLEAN == 'true' && !inputs.externall_call
        uses: teleport-actions/setup@v1
        with:
          version: 16.4.7

      - name: Fetch credentials using Machine ID
        if: env.CLEAN == 'true' && !inputs.externall_call
        uses: teleport-actions/auth@v1
        with:
          proxy: tigergraph.teleport.sh:443
          token: github-token
          certificate-ttl: 1h

      - name: Update configurations
        if: env.CLEAN == 'true' && !inputs.externall_call
        run: |
          SQL_COMMAND="DELETE FROM configurations WHERE branch='${{ env.BRANCH_NAME }}';"
          echo $SQL_COMMAND
          tsh status
          tsh ssh ubuntu@${{ vars.DB_NAME }} << EOF
          echo "$SQL_COMMAND" | psql ${{secrets.DBDSN}}
          exit
          EOF

      - name: Remove EFS Access point
        if: env.CLEAN == 'true' && !inputs.externall_call
        run: |
          AccessPointId=$(aws efs describe-access-points --file-system-id ${{ vars.EFS_ID }} --query 'AccessPoints[?RootDirectory.Path==`/service-${{ env.BRANCH_NAME }}`].AccessPointId' --output text)
          if [ -n "$AccessPointId" ];then
            echo $AccessPointId
            aws efs delete-access-point --access-point-id $AccessPointId
          fi