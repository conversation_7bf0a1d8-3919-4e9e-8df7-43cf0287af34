package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/google/gops/agent"
	"github.com/hashicorp/go-multierror"
	"github.com/tigergraph/cloud-universe/controller"
	"github.com/tigergraph/cloud-universe/controller/observability"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/version"
	"golang.org/x/sync/errgroup"
)

type flags struct {
	GenConfigTemplate bool
	Version           bool
	ConfigPath        string
}

var flg flags

func main() {
	log := logger.L()
	defer log.Sync()
	flag.BoolVar(&flg.GenConfigTemplate, "template", false, "generate config template")
	flag.BoolVar(&flg.Version, "version", false, "display version")
	flag.StringVar(&flg.ConfigPath, "config", "./config", "the path of config file")
	flag.Parse()
	if flg.GenConfigTemplate {
		confPath := flg.ConfigPath + ".example"
		log.Infof("Generating config file at path %v", confPath)
		if err := os.WriteFile(confPath, []byte((&controller.Config{}).YAMLString()), 0600); err != nil {
			log.Fatalf("Failed to write file [path=%v]: %v", confPath, err)
		}
		log.Infof("Done.")
		return
	}

	if flg.Version {
		fmt.Println(version.Version())
		return
	}

	if err := agent.Listen(agent.Options{
		ShutdownCleanup: true,
		Addr:            ":5002",
	}); err != nil {
		log.Fatal(err)
	}

	ctrl, err := controller.New(flg.ConfigPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	log.Infof("Config:\n%v", ctrl.Config.YAMLString())
	if err = ctrl.Init(); err != nil {
		log.Fatalf("Failed to initialize controller service: %v", err)
	}
	mainCtx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	g, gCtx := errgroup.WithContext(mainCtx)
	g.Go(func() error {
		return ctrl.Serve()
	})

	obs := observability.NewObserver("")
	g.Go(func() error {
		return obs.Serve()
	})
	g.Go(func() error {
		<-gCtx.Done()
		log.Info("Received exit signal, sending an exit request for existing server...")
		ctrlErr := ctrl.Destroy()
		return multierror.Append(ctrlErr, obs.Close())
	})

	if err = g.Wait(); err != nil {
		log.Fatalf("Controller service exited with error: %v", err)
	}
	log.Info("Controller service exited.")
}
