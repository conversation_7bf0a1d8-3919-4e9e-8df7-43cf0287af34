#!/bin/bash

# Parse arguments
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift # Remove --run from processing
        ;;
    esac
done

# Database connection string
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else
    echo "DBURL: $DBURL"
fi

# UUID regex pattern
UUID_PATTERN="^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"

# Function to format the creation timestamp
format_timestamp() {
    local timestamp=$1
    date -d "$timestamp" '+%Y-%m-%d %H:%M:%S'
}

# Get all namespaces with creation timestamp and sort by creation timestamp
namespace_info=$(kubectl get ns -o custom-columns=":metadata.name,:metadata.creationTimestamp" --no-headers | sort -k2)

# Collect all namespace IDs
namespace_ids=()
declare -A namespace_map
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    read -r namespace timestamp <<< "$line"
    
    if [[ $namespace =~ $UUID_PATTERN ]]; then
        namespace_ids+=("'$namespace'")
        namespace_map["$namespace"]="$timestamp"
    fi
done <<< "$namespace_info"

# If we have namespace IDs, query them all at once
if [ ${#namespace_ids[@]} -gt 0 ]; then
    # Join namespace IDs with commas for IN clause
    namespace_ids_str=$(IFS=,; echo "${namespace_ids[*]}")
    
    # Single query to get all workgroup information
    workgroup_info=$(psql -X -A -t "$DBURL" <<EOF
        SELECT wg.id::text, wg.name, wg.creator, 
               to_char(wg.created_at, 'YYYY-MM-DD HH24:MI:SS'), 
               to_char(wg.deleted_at, 'YYYY-MM-DD HH24:MI:SS')
        FROM workgroups wg
        WHERE wg.id IN ($namespace_ids_str)
        AND wg.deleted_at IS NOT NULL
        ORDER BY wg.created_at;
EOF
    )
    
    # Print header
    printf "%-45s %-30s %-50s %-20s %-30s %-20s %-10s \n" \
        "NAMESPACE" "WORKGROUP_NAME" "CREATOR" "CREATED_AT" "DELETED_AT" "RUN_STATUS"
    printf "%s\n" "$(printf '=%.0s' {1..215})"
    
    # Process results and generate output
    while IFS='|' read -r namespace workgroup_name creator created_at deleted_at; do
        if [ -z "$namespace" ]; then
            continue
        fi
        
        # Get creation timestamp from map
        timestamp=${namespace_map["$namespace"]}
        formatted_time=$(format_timestamp "$timestamp")
        
        run_status="-"
        if $RUN; then
            # echo "kubectl delete ns $namespace"
            kubectl delete ns "$namespace"
            run_status=$?
        fi
        
        printf "%-45s %-30s %-50s %-20s %-30s %-20s %-10s \n" \
            "$namespace" "$workgroup_name" "$creator" "$formatted_time" "$deleted_at" "$run_status"
    done <<< "$workgroup_info"
    
    printf "%s\n" "$(printf '=%.0s' {1..215})"
fi
