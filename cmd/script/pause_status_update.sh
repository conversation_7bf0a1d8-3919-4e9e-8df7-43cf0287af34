#!/bin/bash
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift
        ;;
    esac
done

# Check database URL
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else
    echo "DBURL: $DBURL"
fi

# Get all TG resources with pause status
tg_output=$(kubectl get tg -A -o custom-columns=NAME:.metadata.name,TG_PAUSE:.spec.pause --no-headers)

# Print header
printf "%-50s %-15s %-25s %-10s %-10s %-10s %-40s\n" \
    "WORKSPACE_NAME" "WORKGROUP_NAME" "CREATED_AT" "TG_PAUSE" "DB_PAUSE" "ExitCode" "WORKSPACE_ID"
printf "%s\n" "$(printf '=%.0s' {1..160})"

# Array to hold the rows
rows=()

while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi

    read -r tg_name tg_pause <<< "$line"

    # Default tg_pause to false if null or <none>
    if [ -z "$tg_pause" ] || [ "$tg_pause" == "<none>" ]; then
        tg_pause="false"
    fi

    # Extract WorkspaceID (UUID) from tg_name
    workspace_id=$(echo "$tg_name" | grep -oP '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')

    if [ ! -z "$workspace_id" ]; then
        # Query database for workspace information
        workspace_info=$(psql -X -A -t "$DBURL" <<EOF
            SELECT ws.name AS ws_name, wg.name AS wg_name, COALESCE(ws.pause, 'f') AS db_pause,
                   to_char(ws.created_at, 'YYYY-MM-DD HH24:MI:SS')
            FROM workspaces ws
            JOIN workgroups wg ON ws.workgroup_id = wg.id
            WHERE ws.id = '$workspace_id'::uuid
            AND ws.deleted_at IS NULL;
EOF
        )

        if [ ! -z "$workspace_info" ]; then
            IFS='|' read -r workspace_name workgroup_name db_pause created_at <<< "$workspace_info"

            # Convert db_pause from 't'/'f' to 'true'/'false'
            if [ "$db_pause" == "t" ]; then
                db_pause="true"
            elif [ "$db_pause" == "f" ]; then
                db_pause="false"
            fi

            update_exit_code="-"

            if [ "$tg_pause" != "$db_pause" ]; then
                if $RUN; then
                    psql -v ON_ERROR_STOP=0 "$DBURL" <<EOF
                        UPDATE workspaces SET pause = '$tg_pause' WHERE id = '$workspace_id'::uuid;
EOF
                    update_exit_code=$?
                fi
            fi

            # Append row to the array
            rows+=("$workspace_name|$workgroup_name|$created_at|$tg_pause|$db_pause|$update_exit_code|$workspace_id")
        fi
    fi
done <<< "$tg_output"

# Sort rows by created_at
IFS=$'\n' sorted_rows=($(sort -t '|' -k3,3 <<<"${rows[*]}"))
unset IFS

# Print sorted rows
for row in "${sorted_rows[@]}"; do
    IFS='|' read -r workspace_name workgroup_name created_at tg_pause db_pause update_exit_code workspace_id <<< "$row"
    
    if [ "$tg_pause" != "$db_pause" ]; then
        # Use red color for inconsistent rows
        printf "\e[31m%-50s %-15s %-25s %-10s %-10s %-10s %-40s\e[0m\n" \
            "$workspace_name" "$workgroup_name" "$created_at" "$tg_pause" "$db_pause" "$update_exit_code" "$workspace_id"
    else
        printf "%-50s %-15s %-25s %-10s %-10s %-10s %-40s\n" \
            "$workspace_name" "$workgroup_name" "$created_at" "$tg_pause" "$db_pause" "$update_exit_code" "$workspace_id"
    fi
done

printf "%s\n" "$(printf '=%.0s' {1..160})"
