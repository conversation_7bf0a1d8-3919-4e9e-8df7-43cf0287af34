#!/bin/bash

# Print current AWS account information
echo "Fetching AWS account information..."
aws_account_info=$(aws sts get-caller-identity)
if [ $? -ne 0 ]; then
    echo "Failed to fetch AWS account information. Please check your AWS CLI configuration."
    exit 1
fi
echo "AWS Account Information: $aws_account_info"
echo

# Process command line arguments
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift
        ;;
    esac
done

# Check required environment variables
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else
    echo "DBURL: $DBURL"
fi

# Check AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed"
    exit 1
fi

# Get all StorageClasses with sc-uuid pattern and their parameters, sorted by creation time
sc_output=$(kubectl get sc -o json | jq '.items | sort_by(.metadata.creationTimestamp)')

# Print header
printf "%-40s %-40s %-15s %-20s %-20s %-20s %-25s %-25s %-15s %-15s %-20s\n" \
    "WORKGROUP_NAME" "STORAGECLASS" "REGION" "EFS_ID" "EFS_ID(DB)" "EFS_TOTAL_SIZE" "CREATION_TIME" "DELETION_TIME" "AWS_EXISTS" "RUN_STATUS" "CREATOR"
printf "%s\n" "$(printf '=%.0s' {1..295})"

# Collect all workgroup IDs
workgroup_ids=()
declare -A sc_info_map
echo "$sc_output" | jq -c '.[] | select(.metadata.name | test("^sc-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"))' | while IFS= read -r sc; do
    sc_name=$(echo "$sc" | jq -r '.metadata.name')
    creation_time=$(echo "$sc" | jq -r '.metadata.creationTimestamp')
    workgroup_id=${sc_name#sc-}
    efs_id=$(echo "$sc" | jq -r '.parameters.fileSystemId // empty')
    
    if [ ! -z "$workgroup_id" ]; then
        workgroup_ids+=("'$workgroup_id'")
        sc_info_map["$workgroup_id"]="$sc_name|$creation_time|$efs_id"
    fi
done

# If we have workgroup IDs, query them all at once
if [ ${#workgroup_ids[@]} -gt 0 ]; then
    # Join workgroup IDs with commas for IN clause
    workgroup_ids_str=$(IFS=,; echo "${workgroup_ids[*]}")
    
    # Single query to get all workgroup information
    workgroup_info=$(psql -X -A -t "$DBURL" <<EOF
        SELECT id::text, name, region, efs_id, to_char(deleted_at, 'YYYY-MM-DD HH24:MI:SS'), creator
        FROM workgroups
        WHERE id IN ($workgroup_ids_str)
        AND deleted_at IS NOT NULL
        ORDER BY created_at;
EOF
    )
    
    # Process results and generate output
    while IFS='|' read -r workgroup_id workgroup_name region efs_id_db deletion_time creator; do
        if [ -z "$workgroup_id" ]; then
            continue
        fi

        # Get StorageClass info from map
        IFS='|' read -r sc_name creation_time efs_id <<< "${sc_info_map[$workgroup_id]}"
        
        # Check if EFS exists in AWS and get its total size
        aws_exists="No"
        efs_total_size="-"
        if [ ! -z "$efs_id" ]; then
            if aws efs describe-file-systems --file-system-id "$efs_id" --region "$region" &> /dev/null; then
                aws_exists="Yes"
                efs_total_size=$(aws efs describe-file-systems --file-system-id "$efs_id" --region "$region" --query 'FileSystems[0].SizeInBytes.Value' --output text)
            fi
        fi
        
        run_status="-"
        if $RUN; then
            # Delete StorageClass
            kubectl delete sc "$sc_name" &> /dev/null
            sc_status=$?
            
            # Delete EFS if it exists
            if [ "$aws_exists" = "Yes" ]; then
                # First delete mount targets
                mount_targets=$(aws efs describe-mount-targets --file-system-id "$efs_id" --region "$region" --query 'MountTargets[*].MountTargetId' --output text)
                for mt_id in $mount_targets; do
                    aws efs delete-mount-target --mount-target-id "$mt_id" --region "$region" &> /dev/null
                done
                
                # Wait for mount targets to be deleted
                sleep 30
                
                # Delete EFS
                aws efs delete-file-system --file-system-id "$efs_id" --region "$region" &> /dev/null
                efs_status=$?
            else
                efs_status=0
            fi
            
            # Combine exit codes
            run_status=$((sc_status + efs_status))
        fi
        
        # Print row in table format
        printf "%-40s %-40s %-15s %-20s %-20s %-20s %-25s %-25s %-15s %-15s %-20s\n" \
            "$workgroup_name" "$sc_name" "$region" "$efs_id" "$efs_id_db" "$efs_total_size" "$creation_time" "$deletion_time" "$aws_exists" "$run_status" "$creator"
    done <<< "$workgroup_info"
    
    printf "%s\n" "$(printf '=%.0s' {1..295})"
fi
