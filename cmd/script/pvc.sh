#!/bin/bash

# Parse arguments
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift # Remove --run from processing
        ;;
    esac
done

# Database connection URL
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else 
    echo "DBURL: $DBURL"
fi

# Get all PVCs with tigergraph.com/cluster-name label
pvc_output=$(kubectl get pvc -A -l tigergraph.com/cluster-name -o custom-columns=NAMESPACE:.metadata.namespace,NAME:.metadata.name,STATUS:.status.phase,VOLUME:.spec.volumeName,CAPACITY:.status.capacity.storage,STORAGECLASS:.spec.storageClassName,CLUSTER_NAME:.metadata.labels.tigergraph\\.com/cluster-name --no-headers)

# Collect all workspace IDs first
workspace_ids=()
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    read -r namespace pvc_name status volume capacity storage_class cluster_name <<< "$line"
    
    if [[ $cluster_name =~ ^tg-(.*) ]]; then
        workspace_id="${BASH_REMATCH[1]}"
        workspace_ids+=("$workspace_id")
    fi
done <<< "$pvc_output"

# Query all workspace information at once if we have any workspace IDs
declare -A workspace_info_map
if [ ${#workspace_ids[@]} -gt 0 ]; then
    # Convert array to comma-separated string of quoted UUIDs
    workspace_ids_str=$(printf "'%s'," "${workspace_ids[@]}" | sed 's/,$//')
    
    # Query all workspace information at once
    while IFS='|' read -r workspace_id workspace_name creator created_at deleted_at workgroup_name; do
        if [ ! -z "$workspace_id" ]; then
            workspace_info_map[$workspace_id]="$workspace_name|$creator|$created_at|$deleted_at|$workgroup_name"
        fi
    done < <(psql -X -A -t "$DBURL" <<EOF
        SELECT ws.id::text, ws.name AS ws_name, ws.creator, 
               to_char(ws.created_at, 'YYYY-MM-DD-HH24:MI:SS'),
               to_char(ws.deleted_at, 'YYYY-MM-DD-HH24:MI:SS'),
               wg.name AS wg_name
        FROM workspaces ws
        JOIN workgroups wg ON ws.workgroup_id = wg.id
        WHERE ws.id = ANY(ARRAY[$workspace_ids_str]::uuid[])
        AND ws.deleted_at IS NOT NULL;
EOF
    )
fi

# Process PVC information using the workspace info map
pvc_info_list=()
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    read -r namespace pvc_name status volume capacity storage_class cluster_name <<< "$line"
    
    if [[ $cluster_name =~ ^tg-(.*) ]]; then
        workspace_id="${BASH_REMATCH[1]}"
        workspace_info="${workspace_info_map[$workspace_id]}"
        
        if [ ! -z "$workspace_info" ]; then
            pvc_info_list+=("$namespace|$pvc_name|$workspace_info|$cluster_name")
        fi
    fi
done <<< "$pvc_output"

# Sort PVC information by created_at (index 5 in the combined string)
sorted_pvc_info_list=($(printf "%s\n" "${pvc_info_list[@]}" | sort -t '|' -k5))

# Print header
printf "%-45s %-15s %-50s %-20s %-30s %-25s %-25s %-20s %-10s \n" \
    "NAMESPACE" "WORKGROUP_NAME" "PVC_NAME" "WORKSPACE_NAME" "CREATOR" "CREATED_AT" "DELETED_AT" "TG_STATUS" "RUN_STATUS"
printf "%s\n" "$(printf '=%.0s' {1..240})"

# Process sorted PVC information
for pvc_info in "${sorted_pvc_info_list[@]}"; do
    IFS='|' read -r namespace pvc_name workspace_name creator created_at deleted_at workgroup_name cluster_name <<< "$pvc_info"
    
    tg_exists=$(kubectl get tg "$cluster_name" -n "$namespace" 2>/dev/null)
    if [ -z "$tg_exists" ]; then
        tg_status="Not Found"
    else
        tg_status="Exists"
    fi

    run_status="-"
    if $RUN; then
        # echo "kubectl delete pvc $pvc_name -n $namespace" 
        kubectl delete pvc "$pvc_name" -n "$namespace"
        run_status=$?
    fi

    printf "%-45s %-15s %-50s %-20s %-30s %-25s %-25s %-20s %-10s \n" \
        "$namespace" "$workgroup_name" "$pvc_name" "$workspace_name" "$creator" "$created_at" "$deleted_at" "$tg_status" "$run_status"
done

printf "%s\n" "$(printf '=%.0s' {1..240})"
