#!/bin/bash
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift
        ;;
    esac
done

# Check database URL
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else
    echo "DBURL: $DBURL"
fi

# Get all TG resources with conditions
tg_output=$(kubectl get tg -A -o custom-columns=NAMESPACE:.metadata.namespace,NAME:.metadata.name,CONDITION:.status.conditions[0].type --no-headers)

# Print header
printf "%-45s %-15s %-50s %-30s %-25s %-25s %-20s %-30s %-15s\n" \
    "NAMESPACE" "WORKGROUP_NAME" "TG_NAME" "WORKSPACE_ID" "CREATED_AT" "DELETED_AT" "CONDITION_TYPE" "CREATOR" "RUN_STATUS"
printf "%s\n" "$(printf '=%.0s' {1..250})"

# Collect all workspace IDs
workspace_ids=()
declare -A tg_info_map
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    read -r namespace tg_name condition_type <<< "$line"
    
    # Extract workspace_id from tg name (remove tg- prefix)
    workspace_id=${tg_name#tg-}
    
    if [ ! -z "$workspace_id" ]; then
        workspace_ids+=("'$workspace_id'")
        # Store TG info in associative array with workspace_id as key
        tg_info_map["$workspace_id"]="$namespace|$tg_name|$condition_type"
    fi
done <<< "$tg_output"

# If we have workspace IDs, query them all at once
if [ ${#workspace_ids[@]} -gt 0 ]; then
    # Join workspace IDs with commas for IN clause
    workspace_ids_str=$(IFS=,; echo "${workspace_ids[*]}")
    
    # Single query to get all workspace information including creator
    workspace_info=$(psql -X -A -t "$DBURL" <<EOF
        SELECT ws.id::text, wg.name, 
               to_char(ws.created_at, 'YYYY-MM-DD HH24:MI:SS'), 
               to_char(ws.deleted_at, 'YYYY-MM-DD HH24:MI:SS'),
               wg.creator
        FROM workspaces ws
        JOIN workgroups wg ON ws.workgroup_id = wg.id
        WHERE ws.id IN ($workspace_ids_str)
        AND ws.deleted_at IS NOT NULL
        ORDER BY ws.created_at;
EOF
    )
    
    # Process results and generate output
    rows=()
    while IFS='|' read -r ws_id workgroup_name created_at deleted_at creator; do
        if [ -z "$ws_id" ]; then
            continue
        fi
        
        # Get TG info from map
        IFS='|' read -r namespace tg_name condition_type <<< "${tg_info_map[$ws_id]}"
        
        run_status="-"
        if $RUN; then
            kubectl delete tg "$tg_name" -n "$namespace"
            run_status=$?
        fi
        
        printf "%-45s %-15s %-50s %-30s %-25s %-25s %-20s %-30s %-15s\n" \
            "$namespace" "$workgroup_name" "$tg_name" "$ws_id" \
            "$created_at" "$deleted_at" "$condition_type" "$creator" "$run_status"
    done <<< "$workspace_info"
fi

printf "%s\n" "$(printf '=%.0s' {1..250})"