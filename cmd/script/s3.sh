#!/bin/bash

# Print current AWS account information
echo "Fetching AWS account information..."
aws_account_info=$(aws sts get-caller-identity)
if [ $? -ne 0 ]; then
    echo "Failed to fetch AWS account information. Please check your AWS CLI configuration."
    exit 1
fi
echo "AWS Account Information: $aws_account_info"
echo

# Parse arguments
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift # Remove --run from processing
        ;;
    esac
done

# Database connection URL
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else
    echo "DBURL: $DBURL"
fi

# Function to convert org_id to bucket name
convert_orgid_to_bucket_name() {
    local org_id=$1
    local region=$2
    local account_id=$3
    if [ -z "$account_id" ]; then
        echo "${org_id,,}" | tr '_' '-' | tr -d '"' | xargs -I {} echo "{}-${region}"
    else
        echo "${org_id,,}" | tr '_' '-' | tr -d '"' | xargs -I {} echo "{}-${region}-${account_id}"
    fi
}

# Function to convert to bucket folder name
convert_bucket_folder_name() {
    local workgroup_id=$1
    local tg_database_id=$2
    echo "${workgroup_id}/${tg_database_id}/"
}

# Function to extract account ID from role ARN
extract_account_id() {
    local role_arn=$1
    # arn:aws:iam::************:role/duhao-cp-role
    echo "$role_arn" | grep -oP '(?<=arn:aws:iam::)[0-9]+(?=:)'
}

# Function to check S3 folder size
get_s3_folder_size() {
    local bucket=$1
    local folder=$2
    
    # Check if folder exists and get size
    if aws s3 ls "s3://${bucket}/${folder}" &>/dev/null; then
        size=$(aws s3 ls "s3://${bucket}/${folder}" --recursive --summarize | grep "Total Size" | awk '{print $3}')
        human_readable_size=$(echo "$size" | numfmt --to=iec-i --suffix=B)
        echo "$human_readable_size"
    else
        echo "Not Found"
    fi
}

# Query database for deleted TG databases and related information
query_output=$(psql -X -A -t "$DBURL" <<EOF
    SELECT 
        td.id AS tg_database_id,
        td.name AS tg_database_name,
        td.creator,
        to_char(td.created_at, 'YYYY-MM-DD HH24:MI:SS'),
        to_char(td.deleted_at, 'YYYY-MM-DD HH24:MI:SS'),
        wg.id AS workgroup_id,
        wg.name AS workgroup_name,
        wg.region,
        wg.org_id,
        cpr.role_arn
    FROM tg_databases td
    JOIN workgroups wg ON td.workgroup_id = wg.id
    JOIN cloud_provider_resources cpr ON wg.cloud_provider_id = cpr.id
    WHERE td.deleted_at IS NOT NULL
    ORDER BY td.created_at;
EOF
)

# Print header
printf "%-40s %-25s %-25s %-50s %-20s %-10s %-10s\n" "TG_DATABASE_ID" "WORKGROUP_NAME" "CREATOR" "BUCKET" "DELETED_AT" "SIZE" "EXIT_CODE"

# Process query results
while IFS='|' read -r tg_database_id tg_database_name creator created_at deleted_at workgroup_id workgroup_name region org_id role_arn; do
    if [ -z "$tg_database_id" ]; then
        continue
    fi
    
    # Extract account ID and generate bucket name
    account_id=$(extract_account_id "$role_arn")
    bucket_name=$(convert_orgid_to_bucket_name "$org_id" "$region" "$account_id")
    folder_name=$(convert_bucket_folder_name "$workgroup_id" "$tg_database_id")
    
    # Check S3 folder size
    folder_size=$(get_s3_folder_size "$bucket_name" "$folder_name" "$role_arn")
    
    # Initialize exit code
    exit_code="-"

    if $RUN && [ "$folder_size" != "Not Found" ]; then
        aws s3 rm "s3://${bucket_name}/${folder_name}" --recursive --quiet
        exit_code=$?
    fi
    
    # Print the results
    if [ "$folder_size" != "Not Found" ]; then
        printf "%-40s %-25s %-25s %-50s %-20s \033[31m%-10s\033[0m %-10s\n" "$tg_database_id" "$workgroup_name" "$creator" "$bucket_name" "$deleted_at" "$folder_size" "$exit_code"
    else
        printf "%-40s %-25s %-25s %-50s %-20s %-10s %-10s\n" "$tg_database_id" "$workgroup_name" "$creator" "$bucket_name" "$deleted_at" "$folder_size" "$exit_code"
    fi
    
done <<< "$query_output"

