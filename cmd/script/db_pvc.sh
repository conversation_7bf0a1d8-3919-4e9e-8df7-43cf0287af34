#!/bin/bash

# Parse arguments
RUN=false
for arg in "$@"; do
    case $arg in
        --run)
        RUN=true
        shift # Remove --run from processing
        ;;
    esac
done

# Database connection URL
DBURL=$DBURL
if [ -z "$DBURL" ]; then
    echo "DBURL is not set"
    exit 1
else 
    echo "DBURL: $DBURL"
fi

# Get all PVCs with storage class sc-{namespace}
pvc_output=$(kubectl get pvc -A -o custom-columns=NAMESPACE:.metadata.namespace,NAME:.metadata.name,STATUS:.status.phase,VOLUME:.spec.volumeName,CAPACITY:.status.capacity.storage,STORAGECLASS:.spec.storageClassName --no-headers)

# Collect all tg_database_ids first
tg_database_ids=()
declare -A namespace_map
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    read -r namespace pvc_name status volume capacity storage_class <<< "$line"
    
    if [[ $storage_class == "sc-$namespace" ]]; then
        tg_database_id="${pvc_name#pvc-}"
        tg_database_ids+=("$tg_database_id")
        namespace_map["$tg_database_id"]="$namespace"
    fi
done <<< "$pvc_output"

# Print header
printf "%-45s %-50s %-20s %-50s %-30s %-10s\n" \
    "NAMESPACE" "PVC_NAME" "DELETED_AT" "NAME" "CREATOR" "RUN_STATUS"

# Query all tg_database information at once if we have any tg_database_ids
declare -A tg_database_info_map
if [ ${#tg_database_ids[@]} -gt 0 ]; then
    # Convert array to comma-separated string of quoted UUIDs
    tg_database_ids_str=$(printf "'%s'," "${tg_database_ids[@]}" | sed 's/,$//')
    
    # Query all tg_database information at once
    while IFS='|' read -r tg_database_id deleted_at name creator; do
        if [ ! -z "$tg_database_id" ]; then
            tg_database_info_map[$tg_database_id]="$deleted_at|$name|$creator"
        fi
    done < <(psql -X -A -t "$DBURL" -c "SELECT id, deleted_at, name, creator FROM tg_databases WHERE id IN ($tg_database_ids_str) AND deleted_at IS NOT NULL;")

    for tg_database_id in "${tg_database_ids[@]}"; do
        info="${tg_database_info_map[$tg_database_id]}"
        if [ ! -z "$info" ]; then
            IFS='|' read -r deleted_at name creator <<< "$info"
            namespace="${namespace_map[$tg_database_id]}"
            pvc_name="pvc-$tg_database_id"
            run_status="-"
            if $RUN; then
                kubectl delete pvc "$pvc_name" -n "$namespace"
                run_status=$?
            fi
            printf "%-45s %-50s %-20s %-50s %-30s %-10s \n" \
                "$namespace" "$pvc_name" "$deleted_at" "$name" "$creator" "$run_status"
        fi
    done
fi

printf "%s\n" "$(printf '=%.0s' {1..205})"
