package pb

import (
	"context"

	"github.com/stretchr/testify/mock"
	quotaData "github.com/tigergraph/cloud-universe/quota-manager/data"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
)

type MockQuotaManager struct {
	mock.Mock
}

func (m *MockQuotaManager) GetDefaultQuotaTierList(ctx context.Context) ([]*quotaData.DefaultQuota, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*quotaData.DefaultQuota), args.Error(1)
}

func (m *MockQuotaManager) CheckOrgQuotaExistence(ctx context.Context, orgID string) (bool, error) {
	args := m.Called(ctx, orgID)
	return args.Bool(0), args.Error(1)
}

func (m *MockQuotaManager) GetOrgQuota(ctx context.Context, orgID string) (*OrgQuota, error) {
	args := m.Called(ctx, orgID)
	return args.Get(0).(*OrgQuota), args.Error(1)
}

func (m *MockQuotaManager) CreateOrgQuota(ctx context.Context, orgID string) error {
	args := m.Called(ctx, orgID)
	return args.Error(0)
}

func (m *MockQuotaManager) UpdateOrgQuotaTier(ctx context.Context, orgID, tierName string) error {
	args := m.Called(ctx, orgID, tierName)
	return args.Error(0)
}

func (m *MockQuotaManager) UpdateOrgQuota(ctx context.Context, orgID string, updateQuotaRequest *QuotaUpdateRequest) error {
	args := m.Called(ctx, orgID, updateQuotaRequest)
	return args.Error(0)
}

func (m *MockQuotaManager) ClaimWorkspaceUpdateQuota(ctx context.Context, tx tggorm.IGorm, orgID, workspaceID string, workspaceTypeCurrent, workspaceTypeTarget *data.WorkspaceType) error {
	args := m.Called(ctx, tx, orgID, workspaceID, workspaceTypeCurrent, workspaceTypeTarget)
	return args.Error(0)
}

func (m *MockQuotaManager) ClaimWorkspaceQuota(ctx context.Context, tx tggorm.IGorm, orgID string, isRW bool, workspaceTypeTarget *data.WorkspaceType) error {
	args := m.Called(ctx, tx, orgID, isRW, workspaceTypeTarget)
	return args.Error(0)
}

func (m *MockQuotaManager) ReleaseWorkspaceQuota(ctx context.Context, tx tggorm.IGorm, orgID string, isRW bool, workspaceTypeTarget *data.WorkspaceType) error {
	args := m.Called(ctx, tx, orgID, isRW, workspaceTypeTarget)
	return args.Error(0)
}

func (m *MockQuotaManager) GetAutoBackupQuota(ctx context.Context, orgID string) (backupCount *int, backupRetentionInDays *int, err error) {
	args := m.Called(ctx, orgID)
	return args.Get(0).(*int), args.Get(1).(*int), args.Error(2)
}

func (m *MockQuotaManager) ClaimManualBackupQuota(ctx context.Context, orgID, workspaceID string, backups []*data.Backup) error {
	args := m.Called(ctx, orgID, workspaceID, backups)
	return args.Error(0)
}
