package pb_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/common/pb"
	"github.com/tigergraph/cloud-universe/controller/services/add_ons/constants"
)

func setupServer() *gin.Engine {
	r := gin.New()
	gin.SetMode(gin.TestMode)

	r.POST("/test", func(c *gin.Context) {
		var p pb.CreateWorkspaceRequest
		if err := c.ShouldBindJSON(&p); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	r.PUT("/test", func(c *gin.Context) {
		var p pb.UpdateWorkspaceRequest
		if err := c.<PERSON><PERSON>ind<PERSON>SON(&p); err != nil {
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
			return
		}

		c.J<PERSON><PERSON>(http.StatusOK, gin.H{"status": "ok"})
	})

	return r
}

func TestWorkspaceUpdateRequest(t *testing.T) {
	r := setupServer()
	testCases := []struct {
		caseName   string
		req        pb.UpdateWorkspaceRequest
		wantStatus int
	}{

		{caseName: "update-enable-nil", req: pb.UpdateWorkspaceRequest{}, wantStatus: http.StatusOK},
		{caseName: "update-disable", req: pb.UpdateWorkspaceRequest{
			Addons: []pb.WorkspaceAddonsRequest{
				{
					UniqueName: constants.COPILOT_UNIQUE_NAME,
					Enable:     false,
				},
			},
		}, wantStatus: http.StatusOK},
		{caseName: "update-enable", req: pb.UpdateWorkspaceRequest{
			Addons: []pb.WorkspaceAddonsRequest{
				{
					UniqueName: constants.COPILOT_UNIQUE_NAME,
					Enable:     true,
					EnableConfiguration: map[string]interface{}{
						"copilot_llm_provider_config_id": uuid.New(),
					},
				},
			},
		}, wantStatus: http.StatusOK},
	}

	for _, tc := range testCases {
		t.Run(tc.caseName, func(t *testing.T) {
			bs, _ := json.Marshal(tc.req)
			httpReq, err := http.NewRequest(http.MethodPut, "/test", bytes.NewReader(bs))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, httpReq)

			assert.NoError(t, err, tc.caseName)
			assert.Equal(t, tc.wantStatus, w.Code, tc.caseName)
		})
	}
}
