package pb

import (
	"context"
	"time"

	"github.com/tigergraph/cloud-universe/billingmanager/utils/constants"
)

type CreateCustomerRequest struct {
	CustomerID    string            `json:"customer_id"`
	CustomerName  string            `json:"customer_name"`
	CustomerEmail string            `json:"customer_email"`
	HasValidCard  bool              `json:"has_valid_card"`
	Enabled       bool              `json:"enabled"`
	Traits        map[string]string `json:"traits"`
} //@name CreateCustomerRequest

type UpdateCustomerRequest struct {
	CustomerName  *string            `json:"customer_name"`
	CustomerEmail *string            `json:"customer_email"`
	Traits        *map[string]string `json:"traits"`
} //@name UpdateCustomerRequest

type CustomerResponse struct {
	CustomerID string `json:"customer_id"`
} //@name CustomerResponse

type AddCreditRequest struct {
	CustomerID   string `json:"customer_id"`
	ID           string `json:"id"`
	PrepaidPrice int    `json:"prepaid_price"`
} //@name AddCreditRequest

type GetCustomerCreditCardsResponse struct {
	DefaultCard string               `json:"default_card"`
	CreditCards []CustomerCreditCard `json:"credit_cards"`
} //@name GetCustomerCreditCardsResponse

type ListDelinquentUsersResponse struct {
	CustomerID           string     `json:"customer_id"`
	DelinquentSince      *time.Time `json:"delinquent_since"`
	SuspendGracePeriod   int        `json:"suspend_grace_period"`
	TerminateGracePeriod int        `json:"terminate_grace_period"`
} //@name ListDelinquentUsersResponse

type CustomerCreditCard struct {
	OrgID       string `json:"org_id"`
	CardID      string `json:"card_id"`
	Last4Digits string `json:"last_4_digits"`
	ExpireMonth uint64 `json:"expire_month"`
	ExpireYear  uint64 `json:"expire_year"`
	NameOnCard  string `json:"name_on_card"`
	Brand       string `json:"brand"`
} //@name CustomerCreditCard

type ResourcePricing struct {
	WorkspacePricing   []WorkspacePricing   `json:"workspace_pricing"`
	AddOnPricing       []AddOnPricing       `json:"addon_pricing"`
	DataStoragePricing []DataStoragePricing `json:"data_storage_pricing"`
} //@name ResourcePricing

type WorkspacePricing struct {
	WorkspaceType string  `json:"workspace_type"`
	Region        string  `json:"region"`
	Platform      string  `json:"platform"`
	PricePerHour  float64 `json:"price_per_hour"`
	HA            string  `json:"ha"`
	CloudProvider string  `json:"cloud_provider"`
} //@name WorkspacePricing

type AddOnPricing struct {
	Type          string  `json:"type"`
	WorkspaceType string  `json:"workspace_type"`
	Platform      string  `json:"platform"`
	Region        string  `json:"region"`
	PricePerHour  float64 `json:"price_per_hour"`
} //@name AddOnPricing

type DataStoragePricing struct {
	Platform      string  `json:"platform"`
	Region        string  `json:"region"`
	PricePerMonth float64 `json:"price_per_month"`
} //@name DataStoragePricing

type BillingStatusResponse struct {
	PaymentMethod constants.PaymentMethod     `json:"payment_method"`
	AWS           constants.MarketplaceStatus `json:"aws"`
	Azure         constants.MarketplaceStatus `json:"azure"`
	GCP           constants.MarketplaceStatus `json:"gcp"`
} //@name MarketplaceStatus

type SwitchPaymentMethodRequest struct {
	TargetPaymentMethod constants.PaymentMethod `json:"target_payment_method"`
	DefaultCardID       string                  `json:"default_card_id"`
}

// Controller should only refer to this interface, not the implementation.
// In the future, this interface should be generated from the proto file.
type BillingManager interface {
	CreateCustomer(ctx context.Context, in *CreateCustomerRequest) (*CustomerResponse, error)
	UpdateCustomer(ctx context.Context, customerID string, request *UpdateCustomerRequest) (*CustomerResponse, error)
	UpdateCustomerSuspendGracePeriod(ctx context.Context, customerID string, gracePeriod int) error
	UpdateCustomerTerminateGracePeriod(ctx context.Context, customerID string, gracePeriod int) error
	CreateSession(ctx context.Context, customerID string) (string, error)
	AddCredit(ctx context.Context, in *AddCreditRequest) error
	GetCredit(ctx context.Context, customerID string, fromCache bool) (float64, error)
	ApplyOnboardingTaskDiscount(ctx context.Context, customerID string, onboardingTaskID string) error
	ListDelinquentUsers(ctx context.Context) ([]ListDelinquentUsersResponse, error)
	SendResourceCleanupNotificationEmail(ctx context.Context, customerID string) error
	PaymentValidation(ctx context.Context, customerID string, fromCache bool) (canPay bool, err error)
	NotifyInvoiceReady(ctx context.Context, customerID string) error
	GetCustomerCreditCards(ctx context.Context, customerID string) (*GetCustomerCreditCardsResponse, error)
	UpdateCustomerDefaultCreditCard(ctx context.Context, cardID string, customerID string) error
	DeleteCustomerCreditCard(ctx context.Context, cardID string, customerID string) error
	AddCustomerCreditCard(ctx context.Context, cardID string, customerID string) error
	GetCustomerCreditCard(ctx context.Context, cardID string) (*CustomerCreditCard, error)
	GetResourcePricing(ctx context.Context) (*ResourcePricing, error)
	SwitchPaymentMethod(ctx context.Context, in *SwitchPaymentMethodRequest) error

	// Marketplace
	ResolveAWSCustomer(ctx context.Context, rawToken string) (customerID, productCode string, err error)
	GetCookieDomain(ctx context.Context) string
	ShouldUseSecureCookie(ctx context.Context) bool
	GetBillingStatus(ctx context.Context, customerID string) (*BillingStatusResponse, error)
	ConnectMarketplaceUser(ctx context.Context, marketPlaceToken string, customerID string) error
	PollSQSMessage(quotaManager QuotaManager)
}
