package pb

import (
	"context"
	"time"

	"github.com/tigergraph/cloud-universe/controller/services/org"
	"github.com/tigergraph/cloud-universe/tgIAM"
)

type FeatureFlagRequest struct {
	FeatureFlags []*FeatureFlag `json:"feature_flags"`
} //@name FeatureFlagRequest

type FeatureFlag struct {
	FeatureName string `json:"feature_name"`
	Enabled     bool   `json:"enabled"`
} //@name FeatureFlag

type TierFeatureFlag struct {
	TierName     string         `json:"tier_name"`
	FeatureFlags []*FeatureFlag `json:"feature_flags"`
} //@name TierFeatureFlag

type Organization struct {
	OrgID          string         `json:"org_id"`
	CreateTime     *time.Time     `json:"create_time,omitempty"`
	OrgName        string         `json:"org_name"`
	OrgDisplayName string         `json:"org_display_name"`
	Creator        string         `json:"creator"`
	LogoUrl        string         `json:"logo_url"`
	FeatureFlag    []*FeatureFlag `json:"feature_flag"`
} //@name Organization

type OrganizationDetail struct {
	OrgID           string                          `json:"org_id"`
	OrgName         string                          `json:"org_name"`
	OrgDisplayName  string                          `json:"org_display_name"`
	LogoUrl         string                          `json:"logo_url"`
	CreateTime      time.Time                       `json:"create_time"`
	Creator         string                          `json:"creator"`
	WorkgroupCount  int                             `json:"workgroup_count"`
	WorkspaceCount  int                             `json:"workspace_count"`
	TGDatabaseCount int                             `json:"tg_database_count"`
	UserCount       int                             `json:"user_count"`
	Workgroups      []Workgroup                     `json:"workgroups"`
	Users           []*tgIAM.IAMUser                `json:"users"`
	PaymentMethods  *GetCustomerCreditCardsResponse `json:"payment_methods"`
	Quota           *OrgQuota                       `json:"quota"`
	FeatureFlag     []*FeatureFlag                  `json:"feature_flag"`
} //@name OrganizationDetail

type OrganizationRetention struct {
	Retention              *float64 `json:"retention"`
	CurrentActiveOrgCount  *int     `json:"current_active_orgs_count"`
	NewActiveOrgCount      *int     `json:"new_active_orgs_count"`
	PreviousActiveOrgCount *int     `json:"previous_active_orgs_count"`
} //@name OrganizationRetention

type CreateAPIKeyRequest struct {
	Description       string `json:"description"`
	LifeTimeInSeconds *int   `json:"life_time_in_seconds"`
} //@name CreateAPIKeyRequest

type DeleteAPIKeyRequest struct {
	KeyName string `json:"key_name"`
} //@name DeleteAPIKeyRequest

type APIKey struct {
	KeyName      string     `json:"key_name"`
	OrgID        string     `json:"org_id"`
	CreatedAt    *time.Time `json:"created_at"`
	PlaintextKey string     `json:"plaintext_key,omitempty"`
	RedactedKey  string     `json:"redacted_key"`
	Creator      string     `json:"creator"`
	Description  string     `json:"description"`
	UserID       string     `json:"user_id"`
	ExpiresAt    *time.Time `json:"expires_at"`
} //@name APIKey

type OrgManager interface {
	GetAllOrganizations(ctx context.Context, page, pageSize int) (*Pagination, error)
	GetOrganizationDetail(ctx context.Context, orgID string) (*OrganizationDetail, error)
	GetActiveOrganizations(ctx context.Context, from, to *time.Time) ([]string, error)
	GetOrganizationsRetention(ctx context.Context, from, to *time.Time) (*OrganizationRetention, error)
	CheckOrganizationExist(ctx context.Context, orgID string) (bool, error)
	CreateOrganization(ctx context.Context, orgDetail *org.OrgMeta) error
	UpdateOrganization(ctx context.Context, orgID, orgName, OrgDisplayName, logoUrl string) error
	UpdateOrganizationTier(ctx context.Context, orgID, tierName string) error

	CreateExternalOrgAPIKey(ctx context.Context, description string, lifeTimeInSeconds *int) (*APIKey, error)
	ListExternalOrgAPIKeys(ctx context.Context) ([]*APIKey, error)
	DeleteExternalOrgAPIKey(ctx context.Context, keyName string) error

	GetDefaultFeatureFlagTierList(ctx context.Context) ([]*TierFeatureFlag, error)
	GetOrgFeatureFlags(ctx context.Context, orgID string) ([]*FeatureFlag, error)
	CheckOrgFeatureFlag(ctx context.Context, orgID string, featureFlag string) (bool, error)
	AddOrgFeatureFlag(ctx context.Context, orgID string, featureFlags []*FeatureFlag) error
	DeleteOrgFeatureFlag(ctx context.Context, orgID string, featureFlags []*FeatureFlag) error

	GetSelfOrgs(ctx context.Context, token string) ([]*Organization, error)
}
