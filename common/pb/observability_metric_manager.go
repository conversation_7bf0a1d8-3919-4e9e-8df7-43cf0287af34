package pb

import (
	"context"
	"encoding/json"
	"time"

	"github.com/tigergraph/cloud-universe/tgIAM"
)

type Tenant struct {
	TenantId     string
	IAMGroup     map[string]*tgIAM.IAMGroup
	IAMWorkspace map[string]*tgIAM.IAMResource
	IsOrgAdmin   bool
}
type Range struct {
	// The boundaries of the time range.
	Start, End time.Time
	// The maximum time between two slices within the boundaries.
	Step time.Duration
}

// ErrorType models the different API error types.
type ErrorType string

type ApiResponse struct {
	Status    string          `json:"status"`
	Data      json.RawMessage `json:"data"`
	ErrorType ErrorType       `json:"errorType"`
	Error     string          `json:"error"`
	Warnings  []string        `json:"warnings,omitempty"`
}

const (
	StatusSuccess = "success"
	StatusError   = "error"

	// Possible values for ErrorType.
	ErrBadData     ErrorType = "bad_data"
	ErrTimeout     ErrorType = "timeout"
	ErrCanceled    ErrorType = "canceled"
	ErrExec        ErrorType = "execution"
	ErrBadResponse ErrorType = "bad_response"
	ErrServer      ErrorType = "server_error"
	ErrClient      ErrorType = "client_error"
)

type MetricManager interface {
	LabelNames(ctx context.Context, tenant *Tenant, matches []string, startTime, endTime time.Time) (int, *ApiResponse, error)
	// LabelValues performs a query for the values of the given label, time range and matchers.
	LabelValues(ctx context.Context, tenant *Tenant, label string, matches []string, startTime, endTime time.Time) (int, *ApiResponse, error)
	// Query performs a query for the given time.
	Query(ctx context.Context, tenant *Tenant, query string, ts time.Time) (int, *ApiResponse, error)
	// QueryRange performs a query for the given range.
	QueryRange(ctx context.Context, tenant *Tenant, query string, r Range) (int, *ApiResponse, error)
	// QueryExemplars performs a query for exemplars by the given query and time range.
	QueryExemplars(ctx context.Context, tenant *Tenant, query string, startTime, endTime time.Time) (int, *ApiResponse, error)
	// Series finds series by label matchers.
	Series(ctx context.Context, tenant *Tenant, matches []string, startTime, endTime time.Time) (int, *ApiResponse, error)
	// Metadata returns metadata about metrics currently scraped by the metric name.
	Metadata(ctx context.Context, tenant *Tenant, metric, limit string) (int, *ApiResponse, error)
}
