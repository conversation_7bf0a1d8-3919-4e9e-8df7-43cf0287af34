package pb

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/common/datatype"
)

type CompareType string

const (
	GreaterThanType CompareType = "GreaterThan"
	LowerThanType   CompareType = "LowerThan"
)

type CreateAlertRuleRequest struct {
	Name                string            `json:"name"`
	Metric              string            `json:"metric"`
	CompareType         CompareType       `json:"compareType"`
	Threshold           float64           `json:"threshold"`
	TimeWindow          datatype.Duration `json:"timeWindow"`
	DetectInterval      datatype.Duration `json:"detectInterval"`
	SuppressionDuration datatype.Duration `json:"suppressionDuration"`
	RecipientList       []string          `json:"recipientList"`
	OrgId               string            `json:"-"`
	WorkgroupID         *uuid.UUID        `json:"workgroupID"`
	WorkspaceID         *uuid.UUID        `json:"workspaceID"`
	Creator             string            `json:"-"`
	Paused              bool              `json:"paused"`
}

type ListAlertRuleRequest struct {
	ID          *uuid.UUID `json:"id"`
	OrgId       string     `json:"orgId"`
	WorkgroupID *uuid.UUID `json:"workgroupID"`
	WorkspaceID *uuid.UUID `json:"workspaceID"`
	Metric      string     `json:"metric"`
}

type DeleteAlertRuleRequest struct {
	ID          *uuid.UUID `json:"id"`
	WorkspaceID *uuid.UUID `json:"workspaceID"`
	OrgId       string     `json:"orgId"`
}

type UpdateAlertRuleRequest struct {
	ID                  *uuid.UUID        `json:"id"`
	WorkspaceID         *uuid.UUID        `json:"workspaceID"`
	OrgId               string            `json:"orgId"`
	Name                string            `json:"name"`
	Metric              string            `json:"metric"`
	CompareType         CompareType       `json:"compareType"`
	Threshold           float64           `json:"threshold"`
	TimeWindow          datatype.Duration `json:"timeWindow"`
	DetectInterval      datatype.Duration `json:"detectInterval"`
	SuppressionDuration datatype.Duration `json:"suppressionDuration"`
	RecipientList       []string          `json:"recipientList"`
	Paused              *bool             `json:"paused"`
}

type AlertRule struct {
	ID                  *uuid.UUID        `json:"id"`
	Name                string            `json:"name"`
	Metric              string            `json:"metric"`
	CompareType         CompareType       `json:"compareType"`
	Threshold           float64           `json:"threshold"`
	TimeWindow          datatype.Duration `json:"timeWindow"`
	DetectInterval      datatype.Duration `json:"detectInterval"`
	SuppressionDuration datatype.Duration `json:"suppressionDuration"`
	RecipientList       []string          `json:"recipientList"`
	WorkgroupID         *uuid.UUID        `json:"workgroupID"`
	WorkspaceID         *uuid.UUID        `json:"workspaceID"`
	Creator             string            `json:"creator"`
	Paused              bool              `json:"paused"`
}

// https://grafana.com/docs/grafana/v10.3/alerting/alerting-rules/manage-contact-points/integrations/webhook-notifier/#alert
type GrafanaAlertWebhookRequest struct {
	Receiver          string            `json:"receiver"`
	Status            string            `json:"status"`
	OrgId             int               `json:"orgId"`
	Alerts            []GrafanaAlert    `json:"alerts"`
	GroupLabels       map[string]string `json:"groupLabels"`
	CommonLabels      map[string]string `json:"commonLabels"`
	CommonAnnotations map[string]string `json:"commonAnnotations"`
	ExternalURL       string            `json:"externalURL"`
	Version           string            `json:"version"`
	GroupKey          string            `json:"groupKey"`
	TruncatedAlerts   int               `json:"truncatedAlerts"`
}

type GrafanaAlert struct {
	Status       string                 `json:"status"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	StartsAt     time.Time              `json:"startsAt"`
	EndsAt       time.Time              `json:"endsAt"`
	Values       map[string]interface{} `json:"values"`
	GeneratorURL string                 `json:"generatorURL"`
	SilenceURL   string                 `json:"silenceURL"`
}

type AlertMetric struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type ListAlertMetricRequest struct {
	TgVersion string `json:"tgVersion"`
}

type AlertManager interface {
	CreateAlertRule(ctx context.Context, req *CreateAlertRuleRequest) (*AlertRule, error)
	ListAlertRule(ctx context.Context, req *ListAlertRuleRequest) ([]AlertRule, error)
	DeleteAlertRule(ctx context.Context, req *DeleteAlertRuleRequest) error
	UpdateAlertRule(ctx context.Context, req *UpdateAlertRuleRequest) error
	AlertMetrics(ctx context.Context, req *ListAlertMetricRequest) ([]AlertMetric, error)
	CompareType(ctx context.Context) ([]CompareType, error)
	GrafanaAlert(ctx context.Context, req *GrafanaAlertWebhookRequest) error
}
