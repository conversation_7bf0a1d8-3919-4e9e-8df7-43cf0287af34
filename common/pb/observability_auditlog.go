package pb

import (
	"context"
)

type SearchAuditLogRequest struct {
	From         int64    `json:"from"`
	To           int64    `json:"to"`
	Statuses     []string `json:"statuses"`
	ServiceNames []string `json:"serviceNames"`
	PageNumber   int      `json:"pageNumber"`
	PageSize     int      `json:"pageSize"`
	Query        string   `json:"query"`
	RequestID    string   `json:"requestId"`
}

type SearchAuditLogResponse struct {
	Logs       []AuditLog `json:"logs"`
	PageNumber int        `json:"pageNumber"`
	PageSize   int        `json:"pageSize"`
	TotalCount int        `json:"totalCount"`
}

type AuditLog struct {
	Date      int64  `json:"date"`
	Service   string `json:"service"`
	Action    string `json:"action"`
	Status    string `json:"status"`
	User      string `json:"user"`
	ClientIP  string `json:"clientIP"`
	UserAgent string `json:"userAgent"`
	Detail    string `json:"detail"`
	RequestId string `json:"requestId"`
}

type AuditEvent struct {
	Service   string `json:"service"`
	Action    string `json:"action"`
	Status    string `json:"status"`
	ClientIP  string `json:"clientIP"`
	UserAgent string `json:"userAgent"`
	Detail    string `json:"detail"`
}

type AuditLogger interface {
	Success(action, detail string) error
	Warn(action, detail string) error
	Fail(action, detail string) error
	Error(action, detail string) error
	Successf(action, template string, values ...interface{}) error
	Warnf(action, template string, values ...interface{}) error
	Failf(action, template string, values ...interface{}) error
	Errorf(action, template string, values ...interface{}) error
	Event(event AuditEvent) error
	WithService(service string) AuditLogger
	WithContext(ctx context.Context) AuditLogger
	WithOrgID(orgId string) AuditLogger
	ServiceNames() ([]string, error)
	Search(req SearchAuditLogRequest) (*SearchAuditLogResponse, error)
}
