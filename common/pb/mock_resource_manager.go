package pb

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/tgIAM"
)

type MockResourceManager struct {
	mock.Mock
	ResourceManager
}

func (m *MockResourceManager) CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest) (*Workspace, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*Workspace), args.Error(1)
}

func (m *MockResourceManager) RefreshWorkspace(ctx context.Context, workspaceID string) error {
	args := m.Called(ctx, workspaceID)
	return args.Error(0)
}

func (m *MockResourceManager) UpdateWorkspace(ctx context.Context, workspaceID string, in *UpdateWorkspaceRequest) (*Workspace, error) {
	args := m.Called(ctx, workspaceID, in)
	return args.Get(0).(*Workspace), args.Error(1)
}

func (m *MockResourceManager) PauseWorkspace(ctx context.Context, workspaceID string) error {
	args := m.Called(ctx, workspaceID)
	return args.Error(0)
}

func (m *MockResourceManager) ResumeWorkspace(ctx context.Context, workspaceID string, requestedByAutoStart bool) error {
	args := m.Called(ctx, workspaceID, requestedByAutoStart)
	return args.Error(0)
}

func (m *MockResourceManager) DeleteWorkspace(ctx context.Context, workspaceID string, ignoreErr bool) error {
	args := m.Called(ctx, workspaceID, ignoreErr)
	return args.Error(0)
}

func (m *MockResourceManager) AssignWorkspaceAdmin(ctx context.Context, workspaceID string, userEmails []string) error {
	args := m.Called(ctx, workspaceID, userEmails)
	return args.Error(0)
}

func (m *MockResourceManager) AssignWorkspaceUser(ctx context.Context, workspaceID string, userEmails []string) error {
	args := m.Called(ctx, workspaceID, userEmails)
	return args.Error(0)
}

func (m *MockResourceManager) RevokeWorkspaceRole(ctx context.Context, workspaceID string, userEmails []string) error {
	args := m.Called(ctx, workspaceID, userEmails)
	return args.Error(0)
}

func (m *MockResourceManager) GetWorkspace(ctx context.Context, workspaceID string) (*Workspace, error) {
	args := m.Called(ctx, workspaceID)
	return args.Get(0).(*Workspace), args.Error(1)
}

func (m *MockResourceManager) GetWorkspacesByCreationTime(ctx context.Context, from, to *time.Time, page, pageSize int) (*Pagination, error) {
	args := m.Called(ctx, from, to, page, pageSize)
	return args.Get(0).(*Pagination), args.Error(1)
}

func (m *MockResourceManager) GetActiveWorkspaces(ctx context.Context, from, to *time.Time) ([]string, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockResourceManager) GetAllWorkspaceMetrics(ctx context.Context) ([]WorkspaceMetric, error) {
	args := m.Called(ctx)
	return args.Get(0).([]WorkspaceMetric), args.Error(1)
}

func (m *MockResourceManager) GetActiveWorkspaceEvents(ctx context.Context, from, to *time.Time) ([]WorkspaceEvent, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).([]WorkspaceEvent), args.Error(1)
}

func (m *MockResourceManager) VerifyAuthToken(ctx context.Context, workspaceID string, token string) error {
	args := m.Called(ctx, workspaceID, token)
	return args.Error(0)
}

func (m *MockResourceManager) GetWorkspaceTypes(ctx context.Context) ([]WorkspaceType, error) {
	args := m.Called(ctx)
	return args.Get(0).([]WorkspaceType), args.Error(1)
}

func (m *MockResourceManager) GetTGVersions(ctx context.Context) ([]TGVersion, error) {
	args := m.Called(ctx)
	return args.Get(0).([]TGVersion), args.Error(1)
}

func (m *MockResourceManager) GetRegions(ctx context.Context) ([]Region, error) {
	args := m.Called(ctx)
	return args.Get(0).([]Region), args.Error(1)
}

func (m *MockResourceManager) CreateWorkgroup(ctx context.Context, in *CreateWorkgroupRequest) (*Workgroup, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*Workgroup), args.Error(1)
}

func (m *MockResourceManager) UpdateWorkgroup(ctx context.Context, workgroupID string, in *UpdateWorkgroupRequest) (*Workgroup, error) {
	args := m.Called(ctx, workgroupID, in)
	return args.Get(0).(*Workgroup), args.Error(1)
}

func (m *MockResourceManager) GetWorkgroupDetail(ctx context.Context, workgroupID string, skipPermCheck bool) (*Workgroup, error) {
	args := m.Called(ctx, workgroupID, skipPermCheck)
	return args.Get(0).(*Workgroup), args.Error(1)
}

func (m *MockResourceManager) DeleteWorkgroup(ctx context.Context, workgroupID string, ignoreErr bool) error {
	args := m.Called(ctx, workgroupID, ignoreErr)
	return args.Error(0)
}

func (m *MockResourceManager) AssignWorkgroupAdminsRole(ctx context.Context, workgroupID string, userEmails []string) error {
	args := m.Called(ctx, workgroupID, userEmails)
	return args.Error(0)
}

func (m *MockResourceManager) RevokeWorkgroupAdminsRole(ctx context.Context, workgroupID string, userEmails []string) error {
	args := m.Called(ctx, workgroupID, userEmails)
	return args.Error(0)
}

func (m *MockResourceManager) GetWorkgroupsByCreationTime(ctx context.Context, from, time *time.Time, page, pageSize int) (*Pagination, error) {
	args := m.Called(ctx, from, time, page, pageSize)
	return args.Get(0).(*Pagination), args.Error(1)
}

func (m *MockResourceManager) ListWorkgroups(ctx context.Context, orgID string, skipPermCheck bool) ([]Workgroup, error) {
	args := m.Called(ctx, orgID, skipPermCheck)
	return args.Get(0).([]Workgroup), args.Error(1)
}

func (m *MockResourceManager) AssignBatchIAMRoles(ctx context.Context, in *UserBatchRolesAssignRequest) error {
	args := m.Called(ctx, in)
	return args.Error(0)
}

func (m *MockResourceManager) RevokeBatchIAMRoles(ctx context.Context, in *UserBatchRolesRevokeRequest) error {
	args := m.Called(ctx, in)
	return args.Error(0)
}

func (m *MockResourceManager) CreateTGDatabase(ctx context.Context, in *CreateTGDatabaseRequest) (*TGDatabase, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*TGDatabase), args.Error(1)
}

func (m *MockResourceManager) UpdateTGDatabase(ctx context.Context, databaseID string, in *UpdateTGDatabaseRequest) (*TGDatabase, error) {
	args := m.Called(ctx, databaseID, in)
	return args.Get(0).(*TGDatabase), args.Error(1)
}

func (m *MockResourceManager) DeleteTGDatabase(ctx context.Context, databaseID string, ignoreErr bool) error {
	args := m.Called(ctx, databaseID, ignoreErr)
	return args.Error(0)
}

func (m *MockResourceManager) GetTGDatabase(ctx context.Context, databaseID string) (*TGDatabase, error) {
	args := m.Called(ctx, databaseID)
	return args.Get(0).(*TGDatabase), args.Error(1)
}

func (m *MockResourceManager) GetTGDatabasesByCreationTime(ctx context.Context, from, to *time.Time, page, pageSize int) (*Pagination, error) {
	args := m.Called(ctx, from, to, page, pageSize)
	return args.Get(0).(*Pagination), args.Error(1)
}

func (m *MockResourceManager) GetAllTGDatabaseMetrics(ctx context.Context) ([]TGDatabaseMetric, error) {
	args := m.Called(ctx)
	return args.Get(0).([]TGDatabaseMetric), args.Error(1)
}

func (m *MockResourceManager) GetResourceOrgID(ctx context.Context, resourceID string, resourceType tgIAM.ResourceType) (string, error) {
	args := m.Called(ctx, resourceID, resourceType)
	return args.Get(0).(string), args.Error(1)
}

func (m *MockResourceManager) ProcessHeartbeat() {
	m.Called()
}

func (m *MockResourceManager) StopDelinquentOrgResources(billingManager BillingManager, interval time.Duration) {
	m.Called(billingManager, interval)
}

func (m *MockResourceManager) AutoStop(interval time.Duration) {
	m.Called(interval)
}

func (m *MockResourceManager) ReconcileRoutine(interval time.Duration, dryRun bool) {
	m.Called(interval, dryRun)
}

func (m *MockResourceManager) CreateBackup(ctx context.Context, workspaceID string, in *CreateBackupRequest) (string, error) {
	args := m.Called(ctx, workspaceID, in)
	return args.Get(0).(string), args.Error(1)
}

func (m *MockResourceManager) ListBackups(ctx context.Context, workspaceID string) ([]*Backup, error) {
	args := m.Called(ctx, workspaceID)
	return args.Get(0).([]*Backup), args.Error(1)
}

func (m *MockResourceManager) GetBackupsByTGDatabase(ctx context.Context, databaseID string) (map[string][]*Backup, error) {
	args := m.Called(ctx, databaseID)
	return args.Get(0).(map[string][]*Backup), args.Error(1)
}

func (m *MockResourceManager) DeleteBackup(ctx context.Context, workspaceID string, in *DeleteBackupRequest) error {
	args := m.Called(ctx, workspaceID, in)
	return args.Error(0)
}

func (m *MockResourceManager) RestoreBackup(ctx context.Context, workspaceID string, in *RestoreBackupRequest) (string, error) {
	args := m.Called(ctx, workspaceID, in)
	return args.Get(0).(string), args.Error(1)
}

func (m *MockResourceManager) GetBackupRestoreStatus(ctx context.Context, workspaceID string, job string) (*BackupRestoreStatus, error) {
	args := m.Called(ctx, workspaceID, job)
	return args.Get(0).(*BackupRestoreStatus), args.Error(1)
}

func (m *MockResourceManager) SetBackupSchedule(ctx context.Context, workspaceID string, request *BackupSchedule) error {
	args := m.Called(ctx, workspaceID, request)
	return args.Error(0)
}

func (m *MockResourceManager) GetBackupSchedule(ctx context.Context, workspaceID string) (*BackupSchedule, error) {
	args := m.Called(ctx, workspaceID)
	return args.Get(0).(*BackupSchedule), args.Error(1)
}

func (m *MockResourceManager) CreateInDatabaseGSQLUser(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) (*tgIAM.IAMUser, error) {
	args := m.Called(ctx, workgroupID, workspaceID, request)
	return args.Get(0).(*tgIAM.IAMUser), args.Error(1)
}

func (m *MockResourceManager) ListInDatabaseGSQLUsers(ctx context.Context, workgroupID, workspaceID string) ([]*tgIAM.IAMUser, error) {
	args := m.Called(ctx, workgroupID, workspaceID)
	return args.Get(0).([]*tgIAM.IAMUser), args.Error(1)
}

func (m *MockResourceManager) UpdateInDatabaseGSQLPassword(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) error {
	args := m.Called(ctx, workgroupID, workspaceID, request)
	return args.Error(0)
}

func (m *MockResourceManager) DeleteInDatabaseGSQLUser(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) error {
	args := m.Called(ctx, workgroupID, workspaceID, request)
	return args.Error(0)
}

func (m *MockResourceManager) ListWorkspacesByAddonsID(ctx context.Context, addonsID uuid.UUID) ([]Workspace, error) {
	args := m.Called(ctx, addonsID)
	return args.Get(0).([]Workspace), args.Error(1)
}

// cloud provider functions
func (m *MockResourceManager) CreateCloudProviderResource(ctx context.Context, in *CreateCloudProviderResourceRequest) (cloudProvider *CloudProviderResource, insufficientQuota []InsufficientQuota, missingPermission []string, err error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*CloudProviderResource), args.Get(1).([]InsufficientQuota), args.Get(2).([]string), args.Error(3)
}

func (m *MockResourceManager) GetCloudProviderResource(ctx context.Context, resourceID string, isAdmin bool) (*CloudProviderResource, error) {
	args := m.Called(ctx, resourceID)
	return args.Get(0).(*CloudProviderResource), args.Error(1)
}

func (m *MockResourceManager) UpdateCloudProviderResource(ctx context.Context, resourceID string, in *UpdateCloudProviderResourceRequest) (*CloudProviderResource, error) {
	args := m.Called(ctx, resourceID, in)
	return args.Get(0).(*CloudProviderResource), args.Error(1)
}

func (m *MockResourceManager) DeleteCloudProviderResource(ctx context.Context, resourceID string) error {
	args := m.Called(ctx, resourceID)
	return args.Error(0)
}

func (m *MockResourceManager) ListCloudProviderResources(ctx context.Context, filters map[string]interface{}, isAdmin bool) ([]*CloudProviderResource, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*CloudProviderResource), args.Error(1)
}

func (m *MockResourceManager) ListPublicCloudProviderResources(ctx context.Context) ([]*PublicCloudProviderResource, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*PublicCloudProviderResource), args.Error(1)
}

func (m *MockResourceManager) UpgradeCloudProviderResource(ctx context.Context, resourceID string, in *UpgradeCloudProviderResourceRequest) error {
	args := m.Called(ctx, resourceID, in)
	return args.Error(0)
}

func (m *MockResourceManager) ValidateCloudProviderRequest(ctx context.Context, request *ValidateCloudProviderRequest) (insufficientQuotas []InsufficientQuota, missingPermissions []string, err error) {
	args := m.Called(ctx, request)
	return args.Get(0).([]InsufficientQuota), args.Get(1).([]string), args.Error(2)
}

func (m *MockResourceManager) AutoClean(ctx context.Context) {
	m.Called(ctx)
}

func (m *MockResourceManager) AddAllowedIP(ctx context.Context, workgroupID string, cidr string, note string) (*AllowedIP, error) {
	args := m.Called(ctx, workgroupID, cidr, note)
	return args.Get(0).(*AllowedIP), args.Error(1)
}

func (m *MockResourceManager) UpdateAllowedIP(ctx context.Context, workgroupID string, id string, cidr string, note string) (*AllowedIP, error) {
	args := m.Called(ctx, workgroupID, id, cidr, note)
	return args.Get(0).(*AllowedIP), args.Error(1)
}

func (m *MockResourceManager) DeleteAllowedIP(ctx context.Context, workgroupID string, id string) (*AllowedIP, error) {
	args := m.Called(ctx, workgroupID, id)
	return args.Get(0).(*AllowedIP), args.Error(1)
}

func (m *MockResourceManager) ListAllowedIP(ctx context.Context, workgroupID string) ([]*AllowedIP, error) {
	args := m.Called(ctx, workgroupID)
	return args.Get(0).([]*AllowedIP), args.Error(1)
}

func (m *MockResourceManager) EnableAllowList(ctx context.Context, workgroupID string) error {
	args := m.Called(ctx, workgroupID)
	return args.Error(0)
}

func (m *MockResourceManager) DisableAllowList(ctx context.Context, workgroupID string) error {
	args := m.Called(ctx, workgroupID)
	return args.Error(0)
}

func (m *MockResourceManager) QueryIsAllowedIP(ctx context.Context, workgroupID string, ip string) (bool, error) {
	args := m.Called(ctx, workgroupID, ip)
	return args.Bool(0), args.Error(1)
}

func (m *MockResourceManager) CreateWorkspaceSchedule(ctx context.Context, in *WorkspaceSchedule) (*WorkspaceSchedule, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*WorkspaceSchedule), args.Error(1)
}

func (m *MockResourceManager) DeleteWorkspaceSchedule(ctx context.Context, scheduleID string) error {
	args := m.Called(ctx, scheduleID)
	return args.Error(0)
}

func (m *MockResourceManager) UpdateWorkspaceSchedule(ctx context.Context, in *WorkspaceSchedule) error {
	args := m.Called(ctx, in)
	return args.Error(0)
}

func (m *MockResourceManager) ListWorkspaceScheduleByWorkspaceID(ctx context.Context, workspaceID string) ([]*WorkspaceSchedule, error) {
	args := m.Called(ctx, workspaceID)
	return args.Get(0).([]*WorkspaceSchedule), args.Error(1)
}

func (m *MockResourceManager) RunCronScheduler(ctx context.Context) {
	m.Called(ctx)
}

func (m *MockResourceManager) RotateLicenseForWorkspace(ctx context.Context, workspaceID string) error {
	args := m.Called(ctx, workspaceID)
	return args.Error(0)
}
