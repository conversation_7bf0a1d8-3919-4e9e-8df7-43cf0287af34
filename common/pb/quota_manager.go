package pb

import (
	"context"

	quotaData "github.com/tigergraph/cloud-universe/quota-manager/data"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
)

type OrgQuota struct {
	OrgID string `json:"org_id"`
	Tier  string `json:"tier"`

	MaxWorkspaceType                   string `json:"max_workspace_type"`
	MaxWorkspaceMemory                 string `json:"max_workspace_memory"`
	RWWorkspaceCountLimit              int    `json:"rw_workspace_count_limit"`
	ROWorkspaceCountLimit              int    `json:"ro_workspace_count_limit"`
	MemoryLimit                        string `json:"memory_limit"`
	WorkspaceAutoBackupCountLimit      int    `json:"workspace_auto_backup_count_limit"`
	WorkspaceAutoBackupRetentionInDays int    `json:"workspace_auto_backup_retention_in_days"`
	WorkspaceManualBackupCountLimit    int    `json:"workspace_manual_backup_count_limit"`

	RWWorkspaceCountUsage int    `json:"rw_workspace_count_usage"`
	ROWorkspaceCountUsage int    `json:"ro_workspace_count_usage"`
	MemoryUsageInBytes    uint64 `json:"memory_usage_in_bytes"`
} //@name OrgQuota

type QuotaUpdateRequest struct {
	MaxWorkspaceType                   string `json:"max_workspace_type"`
	RWWorkspaceCountLimit              int    `json:"rw_workspace_count_limit"`
	ROWorkspaceCountLimit              int    `json:"ro_workspace_count_limit"`
	MemoryLimit                        string `json:"memory_limit"`
	WorkspaceAutoBackupCountLimit      int    `json:"workspace_auto_backup_count_limit"`
	WorkspaceAutoBackupRetentionInDays int    `json:"workspace_auto_backup_retention_in_days"`
	WorkspaceManualBackupCountLimit    int    `json:"workspace_manual_backup_count_limit"`
} //@name QuotaUpdateRequest

type TierUpdateRequest struct {
	TierName string `json:"tier_name"`
} //@name TierUpdateRequest

type QuotaManager interface {
	GetOrgQuota(ctx context.Context, orgID string) (*OrgQuota, error)
	CheckOrgQuotaExistence(ctx context.Context, orgID string) (bool, error)
	CreateOrgQuota(ctx context.Context, orgID string) error
	GetDefaultQuotaTierList(ctx context.Context) ([]*quotaData.DefaultQuota, error)
	UpdateOrgQuotaTier(ctx context.Context, orgID, tierName string) error
	UpdateOrgQuota(ctx context.Context, orgID string, updateQuotaRequest *QuotaUpdateRequest) error
	ClaimWorkspaceUpdateQuota(ctx context.Context, tx tggorm.IGorm, orgID, workspaceID string, workspaceTypeCurrent, workspaceTypeTarget *data.WorkspaceType) error
	ClaimWorkspaceQuota(ctx context.Context, tx tggorm.IGorm, orgID string, isRW bool, workspaceTypeTarget *data.WorkspaceType) error
	ReleaseWorkspaceQuota(ctx context.Context, tx tggorm.IGorm, orgID string, isRW bool, workspaceTypeTarget *data.WorkspaceType) error
	GetAutoBackupQuota(ctx context.Context, orgID string) (backupCount *int, backupRetentionInDays *int, err error)
	ClaimManualBackupQuota(ctx context.Context, orgID, workspaceID string, backups []*data.Backup) error
}
