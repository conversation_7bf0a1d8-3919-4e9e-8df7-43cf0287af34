package pb

import (
	"context"
	"time"
)

const (
	SLOMetricGranularityHour = "Hour"
	SLOMetricGranularityDay  = "Day"
)

type SLOMetricManager interface {
	// handle k8s event,eg. product detailed metric
	K8SEvents(ctx context.Context, events []K8SEvent) error
	// generate slo metrics base on detailed metric
	ProduceSLOMetrics(ctx context.Context, startTime time.Time, endTime time.Time) error
	// search original event
	SearchRawEvent(ctx context.Context, req *SearchRawEventRequest) (*SearchRawEventResponse, error)
	// search requestid level metric
	SearchDetailedMetric(ctx context.Context, req *SearchDetailedMetricRequest) (*SearchDetailedMetricResponse, error)
	// list action level slo metric
	ListSLOMetric(ctx context.Context, req *ListSLOMetricRequest) ([]SLOMetrics, error)
}

type K8SEvent struct {
	MetaData       K8SMetaData       `json:"metadata"`
	InvolvedObject K8SInvolvedObject `json:"involvedObject"`
	Reason         string            `json:"reason"`
	Message        string            `json:"message"`
	Type           string            `json:"type"`
	Date           float64           `json:"date"`
}

type K8SMetaData struct {
	Annotations map[string]interface{} `json:"annotations"`
	Name        string                 `json:"name"`
	Namespace   string                 `json:"namespace"`
}

type K8SInvolvedObject struct {
	Kind      string `json:"kind"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type RawEvent struct {
	Time        int64  `json:"time"`
	Reason      string `json:"reason"`
	Message     string `json:"message"`
	OrgId       string `json:"orgId"`
	WorkgroupId string `json:"workgroupId"`
	WorkspaceId string `json:"workspaceId"`
	RequestId   string `json:"requestId"`
}

type SearchRawEventRequest struct {
	OrgId       string `json:"orgId"`
	WorkgroupId string `json:"workgroupId"`
	WorkspaceId string `json:"workspaceId"`
	RequestId   string `json:"requestId"`
	From        int64  `json:"from"`
	To          int64  `json:"to"`
	PageNumber  int    `json:"pageNumber"`
	PageSize    int    `json:"pageSize"`
}

type SearchRawEventResponse struct {
	Events     []RawEvent `json:"events"`
	PageNumber int        `json:"pageNumber"`
	PageSize   int        `json:"pageSize"`
	TotalCount int        `json:"totalCount"`
}

type DetailedMetric struct {
	RequestId         string `json:"requestId"`
	WorkgroupId       string `json:"workgroupId"`
	WorkspaceId       string `json:"workspaceId"`
	Action            string `json:"action"`
	ProvisionDuration int64  `json:"provisionDuration"`
	ProvisionEndtime  int64  `json:"provisionEndtime"`
	OrgId             string `json:"orgId"`
}

type SearchDetailedMetricRequest struct {
	OrgId       string `json:"orgId"`
	WorkgroupId string `json:"workgroupId"`
	WorkspaceId string `json:"workspaceId"`
	RequestId   string `json:"requestId"`
	Action      string `json:"action"`
	ValueFrom   int64  `json:"valueFrom"`
	ValueTo     int64  `json:"valueTo"`
	From        int64  `json:"from"`
	To          int64  `json:"to"`
	PageNumber  int    `json:"pageNumber"`
	PageSize    int    `json:"pageSize"`
}

type SearchDetailedMetricResponse struct {
	Metrics    []DetailedMetric `json:"metrics"`
	PageNumber int              `json:"pageNumber"`
	PageSize   int              `json:"pageSize"`
	TotalCount int              `json:"totalCount"`
}

type SLOMetrics struct {
	Action      string                `json:"action"`
	Granularity string                `json:"granularity"`
	Metrics     map[string]*SLOMetric `json:"metrics"`
}

type SLOMetric struct {
	Unit   string          `json:"unit"`
	Values [][]interface{} `json:"values"`
}

type ListSLOMetricRequest struct {
	Granularity string `json:"granularity"`
	From        int64  `json:"from"`
	To          int64  `json:"to"`
	Action      string `json:"action"`
}
