package pb

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/common/cloudprovider"
	"github.com/tigergraph/cloud-universe/resource-manager/cloud_provider/aws"
	"github.com/tigergraph/cloud-universe/resource-manager/model/data"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/constants"
)

const (
	WorkspaceSchedule_DAILY    = "DAILY"
	WorkspaceSchedule_WEEKLY   = "WEEKLY"
	WorkspaceSchedule_WEEKDAY  = "WEEKDAY"
	WorkspaceSchedule_NOREPEAT = "NOREPEAT"
)

type CreateCloudProviderResourceRequest struct {
	Platform        string                             `json:"platform"`
	Region          string                             `json:"region"`
	Name            string                             `json:"name"`
	CreatedBy       string                             `json:"created_by"`
	RoleARN         string                             `json:"role_arn"`
	RoleExternalID  string                             `json:"role_external_id"`
	VPCOwnedByTG    bool                               `json:"vpc_owned_by_tg"`
	VPCID           string                             `json:"vpc_id"`
	SubnetID        []string                           `json:"subnet_id"`
	SecurityGroupID string                             `json:"security_group_id"`
	Options         cloudprovider.CloudProviderOptions `json:"options"`
	// Type is used by two sets of API and will be set to 'private' when called by the Admin API
	// and 'public' when called by the public cloud provider creation API.
	Type string `json:"type"`
} //@name CreateCloudProviderResourceRequest

type CloudProviderResource struct {
	ID                  string                             `json:"id"`
	OrgID               string                             `json:"org_id"`
	Platform            string                             `json:"platform"`
	Region              string                             `json:"region"`
	Name                string                             `json:"name"`
	Type                string                             `json:"type"`
	Status              string                             `json:"status"`
	CreatedBy           string                             `json:"created_by"`
	RoleARN             string                             `json:"role_arn"`
	RoleExternalID      string                             `json:"role_external_id"`
	VPCOwnedByTG        bool                               `json:"vpc_owned_by_tg"`
	VPCID               string                             `json:"vpc_id"`
	SubnetID            []string                           `json:"subnet_id"`
	AvailabilityZones   []string                           `json:"availability_zones"`
	SecurityGroupID     string                             `json:"security_group_id"`
	APITokens           []string                           `json:"api_tokens"`
	VersionNumber       int                                `json:"version_number"`
	Version             string                             `json:"version"`
	ComponentVersions   string                             `json:"component_versions"`
	LastError           string                             `json:"last_error"`
	CreatedAt           time.Time                          `json:"created_at"`
	UpdatedAt           time.Time                          `json:"updated_at"`
	EndpointServiceName string                             `json:"endpoint_service_name"`
	Options             cloudprovider.CloudProviderOptions `json:"options"`
	EKSID               string                             `json:"eks_id"`
	ShortID             string                             `json:"short_id"`
	NodeGroupConfig     *aws.NodeGroupConfig               `json:"node_group_config"`
	WorkDir             string                             `json:"work_dir"`
} //@name CloudProviderResource

type PublicCloudProviderResource struct {
	ID                string `json:"id"`
	OrgID             string `json:"org_id"`
	Platform          string `json:"platform"`
	Region            string `json:"region"`
	Name              string `json:"name"`
	Status            string `json:"status"`
	VersionNumber     int    `json:"version_number"`
	Version           string `json:"version"`
	ComponentVersions string `json:"component_versions"`
} //@name PublicCloudProviderResource

type ListAllCloudProviderResource struct {
	ID            string    `json:"id"`
	OrgID         string    `json:"org_id"`
	Platform      string    `json:"platform"`
	Region        string    `json:"region"`
	Name          string    `json:"name"`
	Type          string    `json:"type"`
	Status        string    `json:"status"`
	CreatedBy     string    `json:"created_by"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	VersionNumber int       `json:"version_number"`
} //@name ListAllCloudProviderResource

type UpdateCloudProviderResourceRequest struct {
	Name    string `json:"name"`
	Version string `json:"version"`
} //@name UpdateCloudProviderResourceRequest

type UpgradeCloudProviderComponent struct {
	Name    string `json:"name"`
	Version string `json:"version"`
} //@name UpgradeCloudProviderComponent

type UpgradeCloudProviderResourceRequest struct {
	ComponentVersions []UpgradeCloudProviderComponent `json:"components"`
	NodeGroupConfig   *aws.NodeGroupConfig            `json:"node_group_config"`
} //@name UpgradeCloudProviderResourceRequest

// @Description Create Workspace Request
type CreateWorkspaceRequest struct {
	// Workspace Type Name TG-0/TG-2
	WorkspaceTypeName string `json:"workspace_type_name"`
	TGDatabaseID      string `json:"tg_database_id"`
	TGVersion         string `json:"tg_version"`
	EnableAutoStart   bool   `json:"enable_auto_start"`

	Name string `json:"name"`
	IsRW bool   `json:"is_rw"`

	AutoStopMinutes int `json:"auto_stop_minutes"`

	Addons                 []WorkspaceAddonsRequest `json:"addons"`
	EnableHA               bool                     `json:"enable_ha"`
	SolutionInstallRequest *SolutionInstallRequest  `json:"solution_install_request"`
	XTestEnv               string                   `json:"x_test_env"`

	MaintenanceWindowTimeZone  string `json:"maintenance_window_time_zone"`
	MaintenanceWindowDayOfWeek int    `json:"maintenance_window_day_of_week"`
	MaintenanceWindowHourOfDay int    `json:"maintenance_window_hour_of_day"`
	UpdateStrategy             string `json:"update_strategy"`
} //@name CreateWorkspaceRequest

type SolutionInstallRequest struct {
	Name              string                   `json:"name"`
	SolutionCatalogID string                   `json:"solution_catalog_id"`
	DataSourceOrigin  string                   `json:"data_source_origin"`
	Addons            []WorkspaceAddonsRequest `json:"addons"`
} //@name SolutionInstallRequest

type Workspace struct {
	WorkspaceID            string                   `json:"workspace_id"`
	WorkgroupID            string                   `json:"workgroup_id"`
	WorkgroupName          string                   `json:"workgroup_name"`
	DatabaseID             string                   `json:"database_id"`
	FolderStatistics       *FolderStatistics        `json:"folder_statistics,omitempty"`
	OrgID                  string                   `json:"org_id"`
	OrgName                string                   `json:"org_name"`
	Region                 string                   `json:"region"`
	Name                   string                   `json:"name"`
	Status                 string                   `json:"status,omitempty"`
	ErrMessage             string                   `json:"err_message"`
	ConditionType          string                   `json:"condition_type,omitempty"`
	TGVersion              string                   `json:"tg_version"`
	CreatedAt              string                   `json:"created_at"`
	WorkspaceType          WorkspaceType            `json:"workspace_type"`
	Replicas               int                      `json:"replicas"`
	IsRW                   bool                     `json:"is_rw"`
	NginxHost              string                   `json:"nginx_host,omitempty"`
	VersionNumber          int                      `json:"version_number"`
	Creator                string                   `json:"creator"`
	LastModifiedTime       string                   `json:"last_modified_time"`
	CloudProviderId        string                   `json:"cloud_provider_id"`
	AutoStopMinutes        int                      `json:"auto_stop_minutes"`
	EnableAutoStart        bool                     `json:"enable_auto_start"`
	SnapshotTime           string                   `json:"snapshot_time,omitempty"`
	RefreshStatus          string                   `json:"refresh_status"`
	RefreshMessage         string                   `json:"refresh_message"`
	VertexCount            *int64                   `json:"vertex_count,omitempty"`
	EdgeCount              *int64                   `json:"edge_count,omitempty"`
	GraphTopologySizeBytes *int64                   `json:"graph_topology_size_bytes,omitempty"`
	Addons                 []WorkspaceAddonsRequest `json:"addons"`
	EnableCopilot          bool                     `json:"enable_copilot"`
	EnableHA               bool                     `json:"enable_ha"`
	SolutionCatalogID      string                   `json:"solution_catalog_id"`
	AgentConfig            *AgentConfig             `json:"agent_config"`
} //@name Workspace

type WorkspaceMetric struct {
	Name              string                   `json:"name"`
	ID                string                   `json:"id"`
	OrgID             string                   `json:"org_id"`
	WorkgroupName     string                   `json:"workgroup_name"`
	Region            string                   `json:"region"`
	Platform          constants.Platform       `json:"platform"`
	CurrentTimestamp  time.Time                `json:"current_timestamp"`
	PreviousTimestamp time.Time                `json:"previous_timestamp"`
	WorkspaceType     string                   `json:"workspace_type"`
	CloudProvider     string                   `json:"cloud_provider"`
	EnableHA          bool                     `json:"enable_ha"`
	Addons            []WorkspaceAddonsRequest `json:"addons"`
} //@name WorkspaceMetric

type TGDatabaseMetric struct {
	Name             string             `json:"name"`
	ID               string             `json:"id"`
	OrgID            string             `json:"org_id"`
	WorkgroupName    string             `json:"workgroup_name"`
	Region           string             `json:"region"`
	DeletedAt        time.Time          `json:"deleted_at"`
	Platform         constants.Platform `json:"platform"`
	FolderStatistics *FolderStatistics  `json:"folder_statistics"`
} //@name TGDatabaseMetric

type UpdateWorkspaceRequest struct {
	WorkspaceName     *string `json:"workspace_name,omitempty"`
	WorkspaceTypeName *string `json:"workspace_type_name,omitempty"`
	AutoStopMinutes   *int    `json:"auto_stop_minutes,omitempty" validate:"omitempty,gte=0"`
	EnableAutoStart   *bool   `json:"enable_auto_start,omitempty"`
	EnableHA          *bool   `json:"enable_ha,omitempty"`
	Pause             *bool   `json:"pause,omitempty"`

	MaintenanceWindowTimeZone  *string `json:"maintenance_window_time_zone,omitempty"`
	MaintenanceWindowDayOfWeek *int    `json:"maintenance_window_day_of_week,omitempty"`
	MaintenanceWindowHourOfDay *int    `json:"maintenance_window_hour_of_day,omitempty"`
	UpdateStrategy             *string `json:"update_strategy,omitempty"`

	Addons      []WorkspaceAddonsRequest `json:"addons,omitempty"`
	AgentConfig *AgentConfig             `json:"agent_config,omitempty"`
} //@name UpdateWorkspaceRequest

type RotateLicenseRequest struct {
	License *string `json:"license,omitempty"`
}

type CreateWorkgroupRequest struct {
	Name                 string             `json:"name"`
	Platform             constants.Platform `json:"platform"`
	Region               string             `json:"region"`
	CloudProviderID      string             `json:"cloud_provider_id" validate:"required"`
	EnableEncryption     bool               `json:"enable_encryption"`
	EnableApplicationLog bool               `json:"enable_application_log"`
} //@name CreateWorkgroupRequest

type UpdateWorkgroupRequest struct {
	WorkgroupName        *string                `json:"workgroup_name"`
	EncryptionMethod     *data.EncryptionMethod `json:"encryption_method"`
	EnableApplicationLog *bool                  `json:"enable_application_log"`
} //@name UpdateWorkgroupRequest

type UserRoleRequest struct {
	UserEmails []string `json:"userEmails"`
} //@name UserRoleRequest

type UserBatchRolesAssignRequest struct {
	UserEmail               string   `json:"userEmail"`
	WorkgroupsToAssignAdmin []string `json:"workgroupsToAssignAdmin"`
	WorkspacesToAssignAdmin []string `json:"workspacesToAssignAdmin"`
	WorkspacesToAssignUser  []string `json:"workspacesToAssignUser"`
	BillingRoleToAssign     string   `json:"billingRoleToAssign"`
} //@name UserBatchRolesAssignRequest

type UserBatchRolesRevokeRequest struct {
	UserEmail               string   `json:"userEmail"`
	WorkgroupsToRevokeAdmin []string `json:"workgroupsToRevokeAdmin"`
	WorkspacesToRevokeRoles []string `json:"workspacesToRevokeRoles"`
	BillingRoleToRevoke     bool     `json:"billingRoleToRevoke"`
} //@name UserBatchRolesRevokeRequest

type Workgroup struct {
	WorkgroupID          string                 `json:"workgroup_id"`
	Name                 string                 `json:"name"`
	OrgID                string                 `json:"org_id"`
	OrgName              string                 `json:"org_name"`
	Platform             constants.Platform     `json:"platform"`
	Region               string                 `json:"region"`
	CloudProviderID      string                 `json:"cloud_provider_id"`
	CloudProviderName    string                 `json:"cloud_provider_name"`
	CloudProviderType    string                 `json:"cloud_provider_type"`
	CloudProviderStatus  string                 `json:"cloud_provider_status"`
	Workspaces           []Workspace            `json:"workspaces"`
	TGDatabases          []TGDatabase           `json:"tg_databases"`
	Creator              string                 `json:"creator"`
	EnableAllowList      bool                   `json:"enable_allow_list"`
	IsCurrentIPAllowed   bool                   `json:"is_current_ip_allowed"`
	EncryptionMethod     *data.EncryptionMethod `json:"encryption_method"`
	EnableApplicationLog *bool                  `json:"enable_application_log"`
} //@name Workgroup

type CreateTGDatabaseRequest struct {
	WorkgroupID string `json:"workgroup_id"`
	Name        string `json:"name"`
	TGVersion   string `json:"tg_version"`
} //@name CreateTGDatabaseRequest

type UpdateTGDatabaseRequest struct {
	TGDatabaseName string `json:"tg_database_name" validate:"required,gt=0"`
} //@name UpdateTGDatabaseRequest

type TGDatabase struct {
	OrgID            string            `json:"org_id"`
	OrgName          string            `json:"org_name"`
	DatabaseID       string            `json:"database_id"`
	Name             string            `json:"name"`
	WorkgroupID      string            `json:"workgroup_id"`
	TGVersion        string            `json:"tg_version"`
	Workspaces       []Workspace       `json:"workspaces"`
	FolderStatistics *FolderStatistics `json:"folder_statistics"`
	Region           string            `json:"region"`
	CreateTime       string            `json:"create_time"`
} //@name TGDatabase

type WorkspaceType struct {
	TypeName  string `yaml:"typeName" json:"typeName"`
	CPU       string `yaml:"cpu" json:"cpu"`
	Memory    string `yaml:"memory" json:"memory"`
	Partition int    `yaml:"partition" json:"partition"`
	HA        int    `yaml:"ha" json:"ha"`
} //@name WorkspaceType

type TGVersion struct {
	Name  string `yaml:"name" json:"name"`
	Image string `yaml:"image" json:"image"`
} //@name TGVersion

type AgentConfig struct {
	EnableTopologyForceUpdate            *bool  `json:"enable_topology_force_update,omitempty"`
	HeartbeatIntervalSeconds             *int64 `json:"heartbeat_interval_seconds,omitempty"`
	TopologyUpdateIntervalSeconds        *int64 `json:"topology_update_interval_seconds,omitempty"`
	CatalogActivityUpdateIntervalSeconds *int64 `json:"catalog_activity_update_interval_seconds,omitempty"`
	QueryActivityUpdateIntervalSeconds   *int64 `json:"query_activity_update_interval_seconds,omitempty"`
} //@name AgentConfig

type Region struct {
	Name        string `yaml:"name" json:"name"`
	Description string `yaml:"description" json:"description"`
} //@name Region

type Backup struct {
	ID          string `json:"id"`
	Tag         string `json:"tag"`
	SizeBytes   string `json:"size_bytes"`
	Time        string `json:"time"`
	Type        string `json:"type"`
	Version     string `json:"version"`
	IsAutomatic bool   `json:"is_automatic"`
	Status      string `json:"status"`
} //@name Backup

type CreateBackupRequest struct {
	Name string `json:"name" validate:"required"`
} //@name CreateBackupRequest

type DeleteBackupRequest struct {
	BackupID string `json:"backup_id" validate:"required"`
} //@name DeleteBackupRequest

type RestoreBackupRequest struct {
	BackupID string `json:"backup_id" validate:"required"`
} //@name RestoreBackupRequest

type BackupRestoreStatus struct {
	Name      string `json:"name"`
	Status    string `json:"status"`
	Output    string `json:"output"`
	CreatedAt string `json:"created_at"`
} //@name BackupRestoreStatus

type BackupSchedule struct {
	Pause           bool   `json:"pause" `
	Schedule        string `json:"schedule" validate:"required"`
	MaxBackupFiles  int    `json:"max_backup_files"`
	MaxReservedDays int    `json:"max_reserved_days"`
	MaxRetry        int    `json:"max_retry"`
} //@name BackupSchedule

type InDatabaseUserRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
} //@name InDatabaseUserRequest

type WorkspaceAddonsRequest struct {
	AddonsID            string                 `json:"addons_id" bindings:"required" validate:"required"`
	Enable              bool                   `json:"enable" binding:"required" validate:"required"`
	EnableConfiguration map[string]interface{} `json:"enable_configuration"`
	IsFree              bool                   `json:"is_free"`

	WorkspaceID string `json:"workspace_id"`
	UniqueName  string `json:"unique_name"`
} //@name WorkspaceAddonsRequest

type WorkspaceSize struct {
	WorkspaceID string `json:"workspace_id"`
	Size        int64  `json:"size"`
} //@name WorkspaceSize

type WorkspaceEvent struct {
	WorkspaceID *uuid.UUID
	WorkgroupID *uuid.UUID
	OrgID       string
	Event       data.WorkspaceEventType
	EventStart  *time.Time
	EventEnd    *time.Time
} //@name WorkspaceEvent

func (r InDatabaseUserRequest) String() string {
	return fmt.Sprintf("{Username:%s, Password:%s}", r.Username, r.Password)
}

type ValidateCloudProviderRequest struct {
	ValidateVPC      bool     `json:"validate_vpc"`
	SecureConnection bool     `json:"secure_connection"`
	AccountID        string   `json:"account_id"`
	Platform         string   `json:"platform" binding:"required"`
	Region           string   `json:"region" binding:"required"`
	RoleARN          string   `json:"role_arn" binding:"required"`
	VPCID            string   `json:"vpc_id"`
	SubnetID         []string `json:"subnet_id"`
	SecurityGroupID  string   `json:"security_group_id"`
	RoleExternalID   string   `json:"role_external_id"`
} //@name ValidateCloudProviderRequest

type InsufficientQuota struct {
	Service   string `json:"service"`
	QuotaCode string `json:"quota_code"`
	Required  int    `json:"required"`
	Available int    `json:"available"`
	Limit     int    `json:"limit"`
} //@name InsufficientQuota

type FolderStatistics struct {
	TotalSize  int64           `json:"total_size"`
	MilvusSize int64           `json:"milvus_size"`
	BackupSize int64           `json:"backup_size"`
	LogSize    int64           `json:"log_size"`
	Workspaces []WorkspaceSize `json:"workspaces"`
	BilledSize int64           `json:"billed_size"`
} //@name FolderStatistics

type AllowedIP struct {
	ID   string `json:"id"`
	CIDR string `json:"cidr" validate:"required,cidr"`
	Note string `json:"note"`
} //@name AllowedIP

type Pagination struct {
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
	Data       interface{} `json:"data"`
} //@name Pagination

func (f *FolderStatistics) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to scan FolderStatistics")
	}

	result := &FolderStatistics{}
	err := json.Unmarshal(bytes, result)
	if err != nil {
		return err
	}

	*f = *result
	return nil
}

func (f FolderStatistics) Value() (interface{}, error) {
	return json.Marshal(f)
}

type WorkspaceSchedule struct {
	ID                string `json:"id"`
	WorkspaceID       string `json:"workspace_id" validate:"required"`
	Timezone          string `json:"timezone" validate:"required"`
	Repeat            string `json:"repeat" validate:"required"`
	RepeatOn          string `json:"repeat_on"`
	Date              string `json:"date"`
	Time              string `json:"time" validate:"required"`
	WorkspaceTypeName string `json:"workspace_type_name"`
} //@name WorkspaceSchedule

// Controller should only refers to this interface, not the implementation
// in the future, this interface should be generated from the proto file
type ResourceManager interface {
	CreateWorkspace(ctx context.Context, in *CreateWorkspaceRequest) (*Workspace, error)
	RefreshWorkspace(ctx context.Context, workspaceID string) error
	UpdateWorkspace(ctx context.Context, workspaceID string, in *UpdateWorkspaceRequest) (*Workspace, error)
	PauseWorkspace(ctx context.Context, workspaceID string) error
	ResumeWorkspace(ctx context.Context, workspaceID string, requestedByAutoStart bool) error
	DeleteWorkspace(ctx context.Context, workspaceID string, ignoreErr bool) error
	AssignWorkspaceAdmin(ctx context.Context, workspaceID string, userEmails []string) error
	AssignWorkspaceUser(ctx context.Context, workspaceID string, userEmails []string) error
	RevokeWorkspaceRole(ctx context.Context, workspaceID string, userEmails []string) error
	GetWorkspace(ctx context.Context, workspaceID string) (*Workspace, error)
	GetWorkspacesByCreationTime(ctx context.Context, from, to *time.Time, page, pageSize int) (*Pagination, error)
	GetActiveWorkspaces(ctx context.Context, from, to *time.Time) ([]string, error)
	GetAllWorkspaceMetrics(ctx context.Context) ([]WorkspaceMetric, error)
	GetActiveWorkspaceEvents(ctx context.Context, from, to *time.Time) ([]WorkspaceEvent, error)
	VerifyAuthToken(ctx context.Context, workspaceID string, token string) error
	RotateLicenseForWorkspace(ctx context.Context, workspaceID string) error

	AddAllowedIP(ctx context.Context, workgroupID string, cidr string, note string) (*AllowedIP, error)
	DeleteAllowedIP(ctx context.Context, workgroupID string, id string) (*AllowedIP, error)
	UpdateAllowedIP(ctx context.Context, workgroupID string, id string, cidr string, note string) (*AllowedIP, error)
	ListAllowedIP(ctx context.Context, workgroupID string) ([]*AllowedIP, error)
	EnableAllowList(ctx context.Context, workgroupID string) error
	DisableAllowList(ctx context.Context, workgroupID string) error
	QueryIsAllowedIP(ctx context.Context, workgroupID string, ip string) (bool, error)

	GetWorkspaceTypes(ctx context.Context) ([]WorkspaceType, error)
	GetTGVersions(ctx context.Context) ([]TGVersion, error)
	GetRegions(ctx context.Context) ([]Region, error)

	CreateWorkgroup(ctx context.Context, in *CreateWorkgroupRequest) (*Workgroup, error)
	UpdateWorkgroup(ctx context.Context, workgroupID string, in *UpdateWorkgroupRequest) (*Workgroup, error)
	GetWorkgroupDetail(ctx context.Context, workgroupID string, skipPermCheck bool) (*Workgroup, error)
	DeleteWorkgroup(ctx context.Context, workgroupID string, ignoreErr bool) error
	AssignWorkgroupAdminsRole(ctx context.Context, workgroupID string, userEmails []string) error
	RevokeWorkgroupAdminsRole(ctx context.Context, workgroupID string, userEmails []string) error
	GetWorkgroupsByCreationTime(ctx context.Context, from, to *time.Time, page, pageSize int) (*Pagination, error)
	ListWorkgroups(ctx context.Context, orgID string, skipPermCheck bool) ([]Workgroup, error)

	AssignBatchIAMRoles(ctx context.Context, in *UserBatchRolesAssignRequest) error
	RevokeBatchIAMRoles(ctx context.Context, in *UserBatchRolesRevokeRequest) error

	CreateTGDatabase(ctx context.Context, in *CreateTGDatabaseRequest) (*TGDatabase, error)
	UpdateTGDatabase(ctx context.Context, databaseID string, in *UpdateTGDatabaseRequest) (*TGDatabase, error)
	DeleteTGDatabase(ctx context.Context, databaseID string, ignoreErr bool) error
	GetTGDatabase(ctx context.Context, databaseID string) (*TGDatabase, error)
	GetTGDatabasesByCreationTime(ctx context.Context, from, to *time.Time, page, pageSize int) (*Pagination, error)
	GetAllTGDatabaseMetrics(ctx context.Context) ([]TGDatabaseMetric, error)

	GetResourceOrgID(ctx context.Context, resourceID string, resourceType tgIAM.ResourceType) (string, error)

	ProcessHeartbeat()
	StopDelinquentOrgResources(billingManager BillingManager, interval time.Duration)
	AutoStop(interval time.Duration)
	AutoClean(ctx context.Context)

	ReconcileRoutine(interval time.Duration, dryRun bool)

	CreateCloudProviderResource(ctx context.Context, in *CreateCloudProviderResourceRequest) (cloudProvider *CloudProviderResource, insufficientQuota []InsufficientQuota, missingPermissions []string, err error)
	GetCloudProviderResource(ctx context.Context, resourceID string, isAdmin bool) (*CloudProviderResource, error)
	UpdateCloudProviderResource(ctx context.Context, resourceID string, in *UpdateCloudProviderResourceRequest) (*CloudProviderResource, error)
	DeleteCloudProviderResource(ctx context.Context, resourceID string) error
	ListCloudProviderResources(ctx context.Context, filters map[string]interface{}, isAdmin bool) ([]*CloudProviderResource, error)
	ListPublicCloudProviderResources(ctx context.Context) ([]*PublicCloudProviderResource, error)
	UpgradeCloudProviderResource(ctx context.Context, resourceID string, in *UpgradeCloudProviderResourceRequest) error
	ValidateCloudProviderRequest(ctx context.Context, request *ValidateCloudProviderRequest) (insufficientQuotas []InsufficientQuota, missingPermissions []string, err error)

	CreateBackup(ctx context.Context, workspaceID string, in *CreateBackupRequest) (backupID string, err error)
	ListBackups(ctx context.Context, workspaceID string) ([]*Backup, error)
	GetBackupsByTGDatabase(ctx context.Context, databaseID string) (map[string][]*Backup, error)
	DeleteBackup(ctx context.Context, workspaceID string, in *DeleteBackupRequest) error
	RestoreBackup(ctx context.Context, workspaceID string, in *RestoreBackupRequest) (restoreID string, err error)
	GetBackupRestoreStatus(ctx context.Context, workspaceID string, job string) (status *BackupRestoreStatus, err error)

	SetBackupSchedule(ctx context.Context, workspaceID string, request *BackupSchedule) error
	GetBackupSchedule(ctx context.Context, workspaceID string) (*BackupSchedule, error)

	CreateInDatabaseGSQLUser(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) (*tgIAM.IAMUser, error)
	ListInDatabaseGSQLUsers(ctx context.Context, workgroupID, workspaceID string) ([]*tgIAM.IAMUser, error)
	UpdateInDatabaseGSQLPassword(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) error
	DeleteInDatabaseGSQLUser(ctx context.Context, workgroupID, workspaceID string, request *InDatabaseUserRequest) error

	ListWorkspacesByAddonsID(ctx context.Context, addonsID uuid.UUID) ([]Workspace, error)

	CreateWorkspaceSchedule(ctx context.Context, schedule *WorkspaceSchedule) (*WorkspaceSchedule, error)
	DeleteWorkspaceSchedule(ctx context.Context, scheduleID string) error
	UpdateWorkspaceSchedule(ctx context.Context, schedule *WorkspaceSchedule) error
	ListWorkspaceScheduleByWorkspaceID(ctx context.Context, workspaceID string) ([]*WorkspaceSchedule, error)
	RunCronScheduler(ctx context.Context)
}
