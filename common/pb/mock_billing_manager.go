package pb

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockBillingManager struct {
	mock.Mock
}

func (m *MockBillingManager) CreateCustomer(ctx context.Context, in *CreateCustomerRequest) (*CustomerResponse, error) {
	args := m.Called(ctx, in)
	return args.Get(0).(*CustomerResponse), args.Error(1)
}

func (m *MockBillingManager) UpdateCustomer(ctx context.Context, customerID string, in *UpdateCustomerRequest) (*CustomerResponse, error) {
	args := m.Called(ctx, customerID, in)
	return args.Get(0).(*CustomerResponse), args.Error(1)
}

func (m *MockBillingManager) UpdateCustomerSuspendGracePeriod(ctx context.Context, customerID string, gracePeriod int) error {
	args := m.Called(ctx, customerID, gracePeriod)
	return args.Error(0)
}

func (m *MockBillingManager) UpdateCustomerTerminateGracePeriod(ctx context.Context, customerID string, gracePeriod int) error {
	args := m.Called(ctx, customerID, gracePeriod)
	return args.Error(0)
}

func (m *MockBillingManager) CreateSession(ctx context.Context, customerID string) (string, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(string), args.Error(1)
}

func (m *MockBillingManager) AddCredit(ctx context.Context, in *AddCreditRequest) error {
	args := m.Called(ctx, in)
	return args.Error(0)
}

func (m *MockBillingManager) GetCredit(ctx context.Context, customerID string, fromCache bool) (float64, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockBillingManager) ApplyOnboardingTaskDiscount(ctx context.Context, customerID string, onboardingTaskID string) error {
	args := m.Called(ctx, customerID, onboardingTaskID)
	return args.Error(0)
}

func (m *MockBillingManager) ListDelinquentUsers(ctx context.Context) ([]ListDelinquentUsersResponse, error) {
	args := m.Called(ctx)
	return args.Get(0).([]ListDelinquentUsersResponse), args.Error(1)
}

func (m *MockBillingManager) SendResourceCleanupNotificationEmail(ctx context.Context, customerID string) error {
	args := m.Called(ctx, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) PaymentValidation(ctx context.Context, customerID string, fromCache bool) (bool, error) {
	args := m.Called(ctx, customerID, fromCache)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockBillingManager) NotifyInvoiceReady(ctx context.Context, customerID string) error {
	args := m.Called(ctx, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) GetCustomerCreditCards(ctx context.Context, customerID string) (*GetCustomerCreditCardsResponse, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(*GetCustomerCreditCardsResponse), args.Error(1)
}

func (m *MockBillingManager) UpdateCustomerDefaultCreditCard(ctx context.Context, cardID string, customerID string) error {
	args := m.Called(ctx, cardID, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) DeleteCustomerCreditCard(ctx context.Context, cardID string, customerID string) error {
	args := m.Called(ctx, cardID, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) AddCustomerCreditCard(ctx context.Context, cardID string, customerID string) error {
	args := m.Called(ctx, cardID, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) GetCustomerCreditCard(ctx context.Context, cardID string) (*CustomerCreditCard, error) {
	args := m.Called(ctx, cardID)
	return args.Get(0).(*CustomerCreditCard), args.Error(1)
}

func (m *MockBillingManager) GetResourcePricing(ctx context.Context) (*ResourcePricing, error) {
	args := m.Called(ctx)
	return args.Get(0).(*ResourcePricing), args.Error(1)
}

func (m *MockBillingManager) SwitchPaymentMethod(ctx context.Context, in *SwitchPaymentMethodRequest) error {
	args := m.Called(ctx, in)
	return args.Error(0)
}

func (m *MockBillingManager) ResolveAWSCustomer(ctx context.Context, rawToken string) (string, string, error) {
	args := m.Called(ctx, rawToken)
	return args.String(0), args.String(1), args.Error(2)
}

func (m *MockBillingManager) GetCookieDomain(ctx context.Context) string {
	args := m.Called(ctx)
	return args.String(0)
}

func (m *MockBillingManager) ShouldUseSecureCookie(ctx context.Context) bool {
	args := m.Called(ctx)
	return args.Bool(0)
}

func (m *MockBillingManager) GetBillingStatus(ctx context.Context, customerID string) (*BillingStatusResponse, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0).(*BillingStatusResponse), args.Error(1)
}

func (m *MockBillingManager) ConnectMarketplaceUser(ctx context.Context, marketPlaceToken string, customerID string) error {
	args := m.Called(ctx, marketPlaceToken, customerID)
	return args.Error(0)
}

func (m *MockBillingManager) PollSQSMessage(quotaManager QuotaManager) {
	m.Called()
}
