package pb

import (
	"context"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/controller/services/org"
)

type MockOrgManager struct {
	mock.Mock
}

func (m *MockOrgManager) GetAllOrganizations(ctx context.Context, page, pageSize int) (*Pagination, error) {
	args := m.Called(ctx, page, pageSize)
	return args.Get(0).(*Pagination), args.Error(1)
}

func (m *MockOrgManager) GetOrganizationDetail(ctx context.Context, orgID string) (*OrganizationDetail, error) {
	args := m.Called(ctx, orgID)
	return args.Get(0).(*OrganizationDetail), args.Error(1)
}

func (m *MockOrgManager) GetSelfOrgs(ctx context.Context, token string) ([]*Organization, error) {
	args := m.Called(ctx, token)
	return args.Get(0).([]*Organization), args.Error(1)
}

func (m *MockOrgManager) GetActiveOrganizations(ctx context.Context, from, to *time.Time) ([]string, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockOrgManager) GetOrganizationsRetention(ctx context.Context, from, to *time.Time) (*OrganizationRetention, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).(*OrganizationRetention), args.Error(1)
}

func (m *MockOrgManager) CheckOrganizationExist(ctx context.Context, orgID string) (bool, error) {
	args := m.Called(ctx, orgID)
	return args.Bool(0), args.Error(1)
}

func (m *MockOrgManager) CreateOrganization(ctx context.Context, orgDetail *org.OrgMeta) error {
	args := m.Called(ctx, orgDetail)
	return args.Error(0)
}

func (m *MockOrgManager) UpdateOrganization(ctx context.Context, orgID, orgName, OrgDisplayName, logoUrl string) error {
	args := m.Called(ctx, orgID, orgName, OrgDisplayName, logoUrl)
	return args.Error(0)
}

func (m *MockOrgManager) UpdateOrganizationTier(ctx context.Context, orgID, tierName string) error {
	args := m.Called(ctx, orgID, tierName)
	return args.Error(0)
}

func (m *MockOrgManager) CreateExternalOrgAPIKey(ctx context.Context, description string, lifeTimeInSeconds *int) (*APIKey, error) {
	args := m.Called(ctx, description, lifeTimeInSeconds)
	return args.Get(0).(*APIKey), args.Error(1)
}

func (m *MockOrgManager) ListExternalOrgAPIKeys(ctx context.Context) ([]*APIKey, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*APIKey), args.Error(1)
}

func (m *MockOrgManager) DeleteExternalOrgAPIKey(ctx context.Context, keyName string) error {
	args := m.Called(ctx, keyName)
	return args.Error(0)
}

func (m *MockOrgManager) GetDefaultFeatureFlagTierList(ctx context.Context) ([]*TierFeatureFlag, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*TierFeatureFlag), args.Error(1)
}

func (m *MockOrgManager) GetOrgFeatureFlags(ctx context.Context, orgID string) ([]*FeatureFlag, error) {
	args := m.Called(ctx, orgID)
	return args.Get(0).([]*FeatureFlag), args.Error(1)
}

func (m *MockOrgManager) CheckOrgFeatureFlag(ctx context.Context, orgID string, featureFlag string) (bool, error) {
	args := m.Called(ctx, orgID, featureFlag)
	return args.Bool(0), args.Error(1)
}

func (m *MockOrgManager) AddOrgFeatureFlag(ctx context.Context, orgID string, featureFlags []*FeatureFlag) error {
	args := m.Called(ctx, orgID, featureFlags)
	return args.Error(0)
}

func (m *MockOrgManager) DeleteOrgFeatureFlag(ctx context.Context, orgID string, featureFlags []*FeatureFlag) error {
	args := m.Called(ctx, orgID, featureFlags)
	return args.Error(0)
}
