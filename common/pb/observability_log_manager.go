package pb

import (
	"context"
	"time"
)

type LogManager interface {
	ProcessWorkspaceLogs(ctx context.Context, logs []WorkspaceLog) error
	CreateWorkspaceStorage(ctx context.Context, wsId string) error
	DeleteWorkspaceStorage(ctx context.Context, wsId string) error
	SearchWorkspaceLog(ctx context.Context, req SearchWorkspaceLogRequest) (*SearchWorkspaceLogResponse, error)
	ListWorkspaceLogFieldValues(ctx context.Context, req ListWorkspaceLogFieldValuesRequest) ([]string, error)
}

type LogField string

const (
	LogFieldHost      = "host"
	LogFieldComponent = "component"
)

type WorkspaceLog struct {
	OrgId        string    `json:"orgId"`
	WorkgroupId  string    `json:"workgroupId"`
	WorkspaceId  string    `json:"workspaceId"`
	Host         string    `json:"host"`
	Component    string    `json:"component"`
	Time         time.Time `json:"timestamp"`
	Log          string    `json:"log"`
	EncryptedLog string    `json:"encryptedLog"`
}

type ListWorkspaceLogFieldValuesRequest struct {
	OrgId       string   `json:"orgId"`
	WorkgroupId string   `json:"workgroupId"`
	WorkspaceId string   `json:"workspaceId"`
	Field       LogField `json:"field"`
}

type SearchWorkspaceLogRequest struct {
	OrgId       string   `json:"orgId"`
	WorkgroupId string   `json:"workgroupId"`
	WorkspaceId string   `json:"workspaceId"`
	Hosts       []string `json:"hosts"`
	Components  []string `json:"components"`
	Query       string   `json:"query"`
	From        int64    `json:"from"`
	To          int64    `json:"to"`
	PageNumber  int      `json:"pageNumber"`
	PageSize    int      `json:"pageSize"`
}

type SearchWorkspaceLogResponse struct {
	Logs       []WorkspaceLog `json:"logs"`
	PageNumber int            `json:"pageNumber"`
	PageSize   int            `json:"pageSize"`
	TotalCount int            `json:"totalCount"`
}
