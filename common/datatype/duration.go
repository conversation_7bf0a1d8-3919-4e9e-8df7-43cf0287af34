package datatype

import (
	"encoding/json"
	"errors"
	"strings"
	"time"
)

type Duration time.Duration

func (d Duration) MarshalJSON() ([]byte, error) {
	str := time.Duration(d).String()
	if str != "0s" && int64(d)%int64(time.Minute) == 0 {
		str = strings.TrimSuffix(str, "0s")
	}

	if int64(d)%int64(time.Minute) == 0 {
		str = strings.TrimSuffix(str, "0m")
	}

	return json.Marshal(str)
}

func (d *Duration) UnmarshalJSON(b []byte) error {
	var v interface{}
	if err := json.Unmarshal(b, &v); err != nil {
		return err
	}
	switch value := v.(type) {
	case float64:
		*d = Duration(time.Duration(value))
		return nil
	case string:
		parsedVal, err := time.ParseDuration(value)
		if err != nil {
			return err
		}
		*d = Duration(parsedVal)
		return nil
	default:
		return errors.New("invalid duration")
	}
}
