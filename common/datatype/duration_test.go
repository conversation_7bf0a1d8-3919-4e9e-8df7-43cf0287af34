package datatype

import (
	"encoding/json"
	"reflect"
	"testing"
	"time"
)

func TestDuration_MarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		d       Duration
		want    []byte
		wantErr bool
	}{
		{
			"zero",
			Duration(0),
			[]byte("\"0s\""),
			false,
		},
		{
			"1s",
			Duration(time.Second),
			[]byte("\"1s\""),
			false,
		},
		{
			"1m",
			Duration(time.Minute),
			[]byte("\"1m\""),
			false,
		},
		{
			"1m1s",
			Duration(time.Minute + time.Second),
			[]byte("\"1m1s\""),
			false,
		},
		{
			"1h",
			Duration(time.Hour),
			[]byte("\"1h\""),
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.d.MarshalJSON()
			if (err != nil) != tt.wantErr {
				t.<PERSON>("Duration.MarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("Duration.MarshalJSON() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDuration_UnmarshalJSON(t *testing.T) {
	type args struct {
		b []byte
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    Duration
	}{
		{
			"0s",
			args{
				[]byte("\"0s\""),
			},
			false,
			Duration(0 * time.Second),
		},
		{
			"1s",
			args{
				[]byte("\"1s\""),
			},
			false,
			Duration(time.Second),
		},
		{
			"1m",
			args{
				[]byte("\"1m\""),
			},
			false,
			Duration(time.Minute),
		},
		{
			"1m1s",
			args{
				[]byte("\"1m1s\""),
			},
			false,
			Duration(time.Minute + time.Second),
		},
		{
			"1h",
			args{
				[]byte("\"1h\""),
			},
			false,
			Duration(time.Hour),
		},
		{
			"1s float",
			args{
				[]byte("1000000000.0"),
			},
			false,
			Duration(time.Second),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			json.Marshal(1000000000.0)
			var d Duration
			if err := d.UnmarshalJSON(tt.args.b); (err != nil) != tt.wantErr {
				t.Errorf("Duration.UnmarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
			}

			if !reflect.DeepEqual(d, tt.want) {
				t.Errorf("Duration.MarshalJSON() = %v, want %v", d, tt.want)
			}
		})
	}
}
