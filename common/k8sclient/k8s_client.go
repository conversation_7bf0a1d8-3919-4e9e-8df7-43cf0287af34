package k8sclient

import (
	"context"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

type K8SConfig struct {
	Region    string `yaml:"region"`
	Endpoint  string `yaml:"endpoint"`
	Token     string `yaml:"token"`     // K8s token for the cluster
	Subdomain string `yaml:"subdomain"` // Subdomain of the endpoint e.g. "us-west-2.tgcloud.io"
	CertARN   string `yaml:"certARN"`   // ARN of the certificate for the subdomain
}

type K8sClient struct {
	Clientset     kubernetes.Interface
	DynamicClient dynamic.Interface
}

func NewK8sClient(k8sConfig *K8SConfig) (*K8sClient, error) {
	kConfig := &rest.Config{
		Host:        k8sConfig.Endpoint,
		BearerToken: k8sConfig.Token,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: true,
		},
		QPS:     100,
		Burst:   200,
		Timeout: 10 * time.Second,
	}

	clientset, err := kubernetes.NewForConfig(kConfig)
	if err != nil {
		return nil, fmt.Errorf("unable to create kubernetes clientset: %v", err)
	}

	dynamicClient, err := dynamic.NewForConfig(kConfig)
	if err != nil {
		return nil, fmt.Errorf("unable to create dynamic client: %v", err)
	}

	return &K8sClient{
		Clientset:     clientset,
		DynamicClient: dynamicClient,
	}, nil
}

func (k *K8sClient) GetImageTagFromDeployment(namespace, deploymentName string) (string, error) {
	deploymentGVR := schema.GroupVersionResource{
		Group:    "apps",
		Version:  "v1",
		Resource: "deployments",
	}

	deployment, err := k.DynamicClient.Resource(deploymentGVR).Namespace(namespace).Get(context.TODO(), deploymentName, metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("unable to get deployment: %v", err)
	}

	// Extract deployment selector labels
	selector := deployment.Object["spec"].(map[string]interface{})["selector"].(map[string]interface{})
	labels := selector["matchLabels"].(map[string]interface{})

	// Check if selectors are empty
	if len(labels) == 0 {
		return "", fmt.Errorf("deployment selector is empty")
	}

	// Get the first key-value pair from selectors
	var selectorStrings []string
	for k, v := range labels {
		selectorStrings = append(selectorStrings, fmt.Sprintf("%s=%s", k, v))
	}
	selectorString := strings.Join(selectorStrings, ",")

	podList, err := k.Clientset.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: selectorString,
		FieldSelector: "status.phase=Running",
	})
	if err != nil {
		return "", fmt.Errorf("unable to list pods: %v", err)
	}

	newestPod, err := k.GetNewestPod(podList.Items)
	if err != nil {
		return "", fmt.Errorf("unable to get newest pod: %v", err)
	}

	for _, container := range newestPod.Spec.Containers {
		imageParts := strings.Split(container.Image, ":")
		if len(imageParts) == 2 {
			return imageParts[1], nil
		}
	}

	return "", fmt.Errorf("no image tag found for deployment: %s", deploymentName)
}

func (k *K8sClient) GetNewestPod(pods []v1.Pod) (*v1.Pod, error) {
	if len(pods) == 0 {
		return nil, fmt.Errorf("no pods found")
	}

	var newestPod *v1.Pod = &pods[0]
	var latestTimestamp time.Time = pods[0].ObjectMeta.CreationTimestamp.Time

	// Iterate through all pods to find the one with the latest CreationTimestamp
	for i := 1; i < len(pods); i++ {
		pod := pods[i]
		if pod.ObjectMeta.CreationTimestamp.Time.After(latestTimestamp) {
			newestPod = &pod
			latestTimestamp = pod.ObjectMeta.CreationTimestamp.Time
		}
	}

	// Return the newest pod
	return newestPod, nil
}
