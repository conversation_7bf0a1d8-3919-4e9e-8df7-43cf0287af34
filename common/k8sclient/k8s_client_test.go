package k8sclient

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic/fake"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	k8stesting "k8s.io/client-go/testing"
)

func TestGetImageTagFromDeployment(t *testing.T) {
	// Create a fake Kubernetes client
	clientset := k8sfake.NewSimpleClientset()
	dynamicClient := fake.NewSimpleDynamicClient(runtime.NewScheme())

	// Create a fake deployment
	deployment := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "apps/v1",
			"kind":       "Deployment",
			"metadata": map[string]interface{}{
				"name":      "test-deployment",
				"namespace": "default",
			},
			"spec": map[string]interface{}{
				"selector": map[string]interface{}{
					"matchLabels": map[string]interface{}{
						"app.kubernetes.io/instance": "cluster-agent",
						"app.kubernetes.io/name":     "clusteragent",
					},
				},
			},
		},
	}

	dynamicClient.PrependReactor("get", "deployments", func(action k8stesting.Action) (bool, runtime.Object, error) {
		return true, deployment, nil
	})

	// Create a fake pod with an image tag
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			Labels: map[string]string{
				"app.kubernetes.io/instance": "cluster-agent",
				"app.kubernetes.io/name":     "clusteragent",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "test-container",
					Image: "test-image:latest",
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
	clientset.CoreV1().Pods("default").Create(context.TODO(), pod, metav1.CreateOptions{})

	// Create the K8sClient
	k8sConfig := &K8SConfig{
		Endpoint: "https://fake-endpoint",
		Token:    "fake-token",
		Region:   "fake-region",
	}
	k8sClient, err := NewK8sClient(k8sConfig)
	k8sClient.Clientset = clientset
	k8sClient.DynamicClient = dynamicClient

	require.NoError(t, err)

	// Test the GetImageTagFromDeployment method
	imageTag, err := k8sClient.GetImageTagFromDeployment("default", "test-deployment")
	require.NoError(t, err)
	require.Equal(t, "latest", imageTag)
}

func TestGetNewestPod(t *testing.T) {
	// Create a fake pod list
	podList := []corev1.Pod{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "pod-1",
				CreationTimestamp: metav1.NewTime(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "pod-2",
				CreationTimestamp: metav1.NewTime(time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC)),
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "pod-3",
				CreationTimestamp: metav1.NewTime(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
			},
		},
	}

	// Create the K8sClient
	k8sConfig := &K8SConfig{
		Endpoint: "https://fake-endpoint",
		Token:    "fake-token",
		Region:   "fake-region",
	}
	k8sClient, err := NewK8sClient(k8sConfig)
	require.NoError(t, err)

	// Test the GetNewestPod method
	newestPod, err := k8sClient.GetNewestPod(podList)
	require.NoError(t, err)
	require.Equal(t, "pod-2", newestPod.ObjectMeta.Name)
}
