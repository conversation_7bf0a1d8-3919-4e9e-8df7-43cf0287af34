package cloudprovider

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// component name which must be lower case
const (
	Component_TGOperator        = "tgoperator"
	Component_ClusterAgent      = "clusteragent"
	Component_AutoStartHandler  = "autostarthandler"
	Component_TFTemplateVersion = "tftemplateversion"
)

type CloudProviderInfo struct {
	CloudProviderId   string
	Version           string
	ComponentVersion  string
	Status            string
	APIToken          string
	TokenTTLInSeconds int
	CreatedAt         time.Time
	LastHeartbeat     time.Time
}

type ComponentVersion struct {
	VersionMap map[string]string
}

func NewComponentVersion() *ComponentVersion {
	return &ComponentVersion{
		VersionMap: make(map[string]string),
	}
}

func NewComponentVersionWithMap(versionMap map[string]string) *ComponentVersion {
	return &ComponentVersion{
		VersionMap: versionMap,
	}
}

func IsValidComponent(component string) bool {
	switch strings.ToLower(component) {
	case Component_TGOperator, Component_ClusterAgent:
		return true
	case Component_AutoStartHandler:
		return true
	case Component_TFTemplateVersion:
		return true
	}

	return false
}

func (cv *ComponentVersion) SetVersion(component string, version string) {
	cv.VersionMap[component] = version
}

func (cv *ComponentVersion) GetVersion(component string) string {
	return cv.VersionMap[component]
}

func (cv *ComponentVersion) GetTGOperatorVersion() string {
	return cv.VersionMap[Component_TGOperator]
}

func (cv *ComponentVersion) GetClusterAgentVersion() string {
	return cv.VersionMap[Component_ClusterAgent]
}

func (cv *ComponentVersion) GetAutoStartHandlerVersion() string {
	return cv.VersionMap[Component_AutoStartHandler]
}

func (cv *ComponentVersion) GetTFTemplateVersion() string {
	return cv.VersionMap[Component_TFTemplateVersion]
}

func (cv *ComponentVersion) SetTFTemplateVersion(version string) {
	cv.VersionMap[Component_TFTemplateVersion] = version
}

func (cv *ComponentVersion) SetTGOperatorVersion(version string) {
	cv.VersionMap[Component_TGOperator] = version
}

func (cv *ComponentVersion) SetClusterAgentVersion(version string) {
	cv.VersionMap[Component_ClusterAgent] = version
}

func (cv *ComponentVersion) GetVersionMap() map[string]string {
	return cv.VersionMap
}

func (cv *ComponentVersion) SetVersionMap(versionMap map[string]string) {
	cv.VersionMap = versionMap
}

func (cv *ComponentVersion) String() string {
	return fmt.Sprintf("ComponentVersion: %v", cv.VersionMap)
}

func (cv *ComponentVersion) MarshalJSON() ([]byte, error) {
	return json.Marshal(cv.VersionMap)
}

func (cv *ComponentVersion) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &cv.VersionMap)
}

func (cv *ComponentVersion) Equal(cv2 *ComponentVersion) bool {

	for k, v := range cv.VersionMap {
		if cv2.VersionMap[strings.ToLower(k)] != v {
			return false
		}
	}
	return true
}
