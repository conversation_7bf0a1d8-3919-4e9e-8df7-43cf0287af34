package cloudprovider

import (
	"testing"

	"encoding/json"

	"github.com/stretchr/testify/require"
)

func TestNewCloudProviderOptions(t *testing.T) {
	// Test default options
	opt := NewCloudProviderOptions(true, "123")
	require.NotNil(t, opt)
	require.Equal(t, true, opt.SecureConnection)
	require.Equal(t, "123", opt.AccountID)

	// Test setting options
	opt = NewCloudProviderOptions(false, "123")
	require.NotNil(t, opt)
	require.Equal(t, false, opt.SecureConnection)
	require.Equal(t, "123", opt.AccountID)
}

func TestCloudProviderOptions_String(t *testing.T) {
	// Test String method
	opt := NewCloudProviderOptions(true, "123")
	expectedString := "secure_connection: true, account_id: 123"
	require.Equal(t, expectedString, opt.String())

	opt = NewCloudProviderOptions(false, "123")
	expectedString = "secure_connection: false, account_id: 123"
	require.Equal(t, expectedString, opt.String())
}

func TestCloudProviderOptions_MarshalJSON(t *testing.T) {
	// Test MarshalJSON method
	opt := NewCloudProviderOptions(true, "123")
	jsonData, err := opt.MarshalJSON()
	require.NoError(t, err)
	require.JSONEq(t, `{"account_id":"123", "secure_connection":true}`, string(jsonData))

	opt = NewCloudProviderOptions(false, "123")
	jsonData, err = opt.MarshalJSON()
	require.NoError(t, err)
	require.JSONEq(t, `{"account_id":"123", "secure_connection":false}`, string(jsonData))
}

func TestCloudProviderOptions_UnmarshalJSON(t *testing.T) {
	// Test UnmarshalJSON method
	jsonData := `{"account_id":"123", "secure_connection":true}`
	opt := &CloudProviderOptions{}
	err := json.Unmarshal([]byte(jsonData), opt)
	require.NoError(t, err)
	require.Equal(t, true, opt.SecureConnection)
	require.Equal(t, "123", opt.AccountID)

	jsonData = `{"account_id":"123", "secure_connection":false}`
	err = json.Unmarshal([]byte(jsonData), opt)
	require.NoError(t, err)
	require.Equal(t, false, opt.SecureConnection)
	require.Equal(t, "123", opt.AccountID)

	// Test UnmarshalJSON with invalid data
	jsonData = `{"account_id":"123", "secure_connection":"invalid"}`
	err = json.Unmarshal([]byte(jsonData), opt)
	require.Error(t, err)
}
