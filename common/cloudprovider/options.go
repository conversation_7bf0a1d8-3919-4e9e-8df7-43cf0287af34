package cloudprovider

import (
	"encoding/json"
	"fmt"
)

// CloudProviderOptions holds configuration options for the cloud provider
type CloudProviderOptions struct {
	SecureConnection bool   `json:"secure_connection"`
	AccountID        string `json:"account_id"`
} //@name CloudProviderOptions

// NewCloudProviderOptions creates a new CloudProviderOptions with default or specified values
func NewCloudProviderOptions(secureConnection bool, accountID string) *CloudProviderOptions {
	return &CloudProviderOptions{
		SecureConnection: secureConnection,
		AccountID:        accountID,
	}
}

func (cv *CloudProviderOptions) String() string {
	return fmt.Sprintf("secure_connection: %v, account_id: %s", cv.SecureConnection, cv.AccountID)
}

func (cv *CloudProviderOptions) MarshalJSON() ([]byte, error) {
	return json.Marshal(*cv)
}
