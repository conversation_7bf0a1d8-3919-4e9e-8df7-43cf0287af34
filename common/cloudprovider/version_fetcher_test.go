package cloudprovider

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cloud-universe/common/k8sclient"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/dynamic/fake"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	k8stesting "k8s.io/client-go/testing"
)

func TestFetchComponentVersions(t *testing.T) {
	// Create a fake Kubernetes client
	clientset := k8sfake.NewSimpleClientset()

	// Create fake deployments
	deployments := []struct {
		namespace      string
		deploymentName string
	}{
		{"tigergraph", "tigergraph-operator-controller-manager"},
		{"default", "cluster-agent-clusteragent"},
		{"default", "autostart-handler-autostarthandler"},
	}

	deploymentObjects := map[string]*unstructured.Unstructured{}
	for _, dep := range deployments {
		deployment := &unstructured.Unstructured{
			Object: map[string]interface{}{
				"apiVersion": "apps/v1",
				"kind":       "Deployment",
				"metadata": map[string]interface{}{
					"name":      dep.deploymentName,
					"namespace": dep.namespace,
				},
				"spec": map[string]interface{}{
					"selector": map[string]interface{}{
						"matchLabels": map[string]interface{}{
							"app": dep.deploymentName,
						},
					},
				},
			},
		}
		deploymentObjects[dep.deploymentName] = deployment

	}
	dynamicClient := fake.NewSimpleDynamicClient(runtime.NewScheme())

	// deploymentGVR := schema.GroupVersionResource{
	//     Group:    "apps",
	//     Version:  "v1",
	//     Resource: "deployments",
	// }
	dynamicClient.PrependReactor("get", "deployments", func(action k8stesting.Action) (bool, runtime.Object, error) {
		deploymentName := action.(k8stesting.GetAction).GetName()
		fmt.Println("Getting deployment", deploymentName)
		deployment, ok := deploymentObjects[deploymentName]
		if !ok {
			return false, nil, fmt.Errorf("deployment %s not found", deploymentName)
		}
		return true, deployment, nil
	})

	// Create fake pods with image tags
	pods := []struct {
		namespace string
		labels    map[string]string
		image     string
	}{
		{"tigergraph", map[string]string{"app": "tigergraph-operator-controller-manager"}, "tg-operator:latest"},
		{"default", map[string]string{"app": "cluster-agent-clusteragent"}, "cluster-agent:080808"},
		{"default", map[string]string{"app": "autostart-handler-autostarthandler"}, "autostart-handler:090909"},
	}

	for ind, pod := range pods {
		p := &corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-pod" + fmt.Sprint(ind),
				Namespace: pod.namespace,
				Labels:    pod.labels,
			},
			Spec: corev1.PodSpec{
				Containers: []corev1.Container{
					{
						Name:  "test-container",
						Image: pod.image,
					},
				},
			},
			Status: corev1.PodStatus{
				Phase: corev1.PodRunning,
			},
		}
		clientset.CoreV1().Pods(pod.namespace).Create(context.TODO(), p, metav1.CreateOptions{})
	}

	// Create the K8sClient
	k8sConfig := k8sclient.K8SConfig{
		Endpoint: "https://fake-endpoint",
		Token:    "fake-token",
		Region:   "fake-region",
	}
	k8sClient, err := k8sclient.NewK8sClient(&k8sConfig)
	k8sClient.Clientset = clientset
	k8sClient.DynamicClient = dynamicClient

	require.NoError(t, err)

	// Initialize ComponentVersionFetcher
	componentVersionFetcher := NewComponentVersionFetcher(k8sClient)

	// Fetch component versions
	componentVersions, err := componentVersionFetcher.FetchComponentVersions()
	require.NoError(t, err)

	// Verify the component versions
	expectedVersions := map[string]string{
		Component_TGOperator:       "latest",
		Component_ClusterAgent:     "080808",
		Component_AutoStartHandler: "090909",
	}

	require.Equal(t, expectedVersions, componentVersions)
}
