package cloudprovider

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSetTGOperatorVersion(t *testing.T) {
	cv := NewComponentVersion()
	version := "1.0.0"
	cv.SetTGOperatorVersion(version)

	assert.Equal(t, version, cv.VersionMap[Component_TGOperator], "TGOperator version should be set correctly")
}

func TestSetClusterAgentVersion(t *testing.T) {
	cv := NewComponentVersion()
	version := "1.0.1"
	cv.SetClusterAgentVersion(version)

	assert.Equal(t, version, cv.VersionMap[Component_ClusterAgent], "ClusterAgent version should be set correctly")
}

func TestGetVersionMap(t *testing.T) {
	cv := NewComponentVersion()
	versionMap := map[string]string{
		"Component1": "1.0.0",
		"Component2": "2.0.0",
	}
	cv.VersionMap = versionMap

	assert.Equal(t, versionMap, cv.GetVersionMap(), "GetVersionMap should return the correct version map")
}

func TestSetVersionMap(t *testing.T) {
	cv := NewComponentVersion()
	versionMap := map[string]string{
		"Component1": "1.0.0",
		"Component2": "2.0.0",
	}
	cv.SetVersionMap(versionMap)

	assert.Equal(t, versionMap, cv.VersionMap, "SetVersionMap should correctly set the version map")
}

func TestString(t *testing.T) {
	cv := NewComponentVersion()
	versionMap := map[string]string{
		"Component1": "1.0.0",
	}
	cv.VersionMap = versionMap

	expectedString := "ComponentVersion: map[Component1:1.0.0]"
	assert.Equal(t, expectedString, cv.String(), "String method should return the correct string representation")
}

func TestMarshalJSON(t *testing.T) {
	cv := NewComponentVersion()
	versionMap := map[string]string{
		"Component1": "1.0.0",
	}
	cv.VersionMap = versionMap

	jsonBytes, err := cv.MarshalJSON()
	assert.NoError(t, err, "MarshalJSON should not return an error")
	assert.JSONEq(t, `{"Component1":"1.0.0"}`, string(jsonBytes), "MarshalJSON should return the correct JSON representation")
}

func TestUnmarshalJSON(t *testing.T) {
	cv := NewComponentVersion()
	jsonStr := `{"Component1":"1.0.0"}`
	err := cv.UnmarshalJSON([]byte(jsonStr))
	assert.NoError(t, err, "UnmarshalJSON should not return an error")
	assert.Equal(t, "1.0.0", cv.VersionMap["Component1"], "UnmarshalJSON should correctly set the version map")
}

func TestEqual(t *testing.T) {
	cv1 := NewComponentVersion()
	cv2 := NewComponentVersion()

	cv1.SetTGOperatorVersion("1.0.0")
	cv2.SetTGOperatorVersion("1.0.0")

	assert.True(t, cv1.Equal(cv2), "Equal should return true for identical ComponentVersions")

	cv2.SetTGOperatorVersion("1.0.1")
	assert.False(t, cv1.Equal(cv2), "Equal should return false for different ComponentVersions")
}
