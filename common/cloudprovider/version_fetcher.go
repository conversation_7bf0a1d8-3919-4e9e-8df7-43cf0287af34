package cloudprovider

import (
	"fmt"

	"github.com/tigergraph/cloud-universe/common/k8sclient"
)

type ComponentVersionFetcherInterface interface {
	FetchComponentVersions() (map[string]string, error)
}

type ComponentMetadata struct {
	ComponentName  string
	Namespace      string
	DeploymentName string
}

// assign a fixed list of ComponentMetadata
var TGComponentMetadata = []ComponentMetadata{
	{ComponentName: Component_TGOperator, Namespace: "tigergraph", DeploymentName: "tigergraph-operator-controller-manager"},
	{ComponentName: Component_ClusterAgent, Namespace: "default", DeploymentName: "cluster-agent-clusteragent"},
	{ComponentName: Component_AutoStartHandler, Namespace: "default", DeploymentName: "autostart-handler-autostarthandler"},
}

type ComponentVersionFetcher struct {
	K8sClient          *k8sclient.K8sClient
	ComponentMetadatas *[]ComponentMetadata
}

func NewComponentVersionFetcher(k8sClient *k8sclient.K8sClient) *ComponentVersionFetcher {
	return &ComponentVersionFetcher{
		K8sClient:          k8sClient,
		ComponentMetadatas: &TGComponentMetadata,
	}
}

func (cvf *ComponentVersionFetcher) FetchComponentVersions() (map[string]string, error) {
	componentVersions := make(map[string]string)

	for _, metadata := range *cvf.ComponentMetadatas {
		imageTag, err := cvf.K8sClient.GetImageTagFromDeployment(metadata.Namespace, metadata.DeploymentName)
		if err != nil {
			return nil, fmt.Errorf("failed to get image tag for component %s: %v", metadata.ComponentName, err)
		}
		componentVersions[metadata.ComponentName] = imageTag
	}

	return componentVersions, nil
}
