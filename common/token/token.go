package token

import (
	"context"
	"fmt"
	"time"

	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/logger"
	"gorm.io/gorm"
)

const (
	APIKeyType                  = "cloud_provider"
	DefaultKeyLifeTimeInSeconds = 3600 * 24 * 14 // 14 days
	RenewThreshold              = 72 * time.Hour // 3 days
)

type CloudProviderTokenService struct {
	cloudProviderID string
	orgID           string
	TGIAM           tgIAM.TGIAM
	TTLInSeconds    int
}

func NewCloudProviderTokenService(tgIAM tgIAM.TGIAM, cloudProviderID, orgID string, ttlInSeconds int) *CloudProviderTokenService {
	return &CloudProviderTokenService{
		cloudProviderID: cloudProviderID,
		orgID:           orgID,
		TGIAM:           tgIAM,
		TTLInSeconds:    ttlInSeconds,
	}
}

func (c *CloudProviderTokenService) GetCloudProviderID() string {
	return c.cloudProviderID
}

func (c *CloudProviderTokenService) GetOrgID() string {
	return c.orgID
}

// member functions that encapsulate tgIAM token operations

func (c *CloudProviderTokenService) CreateToken(versionNumber int) (string, int, error) {
	log := logger.L()
	// create token
	var keyName string
	var key *tgIAM.IAMAPIKey
	var err error
	const maxRetry = 1000

	for i := 0; i < maxRetry; i++ {
		keyName = fmt.Sprintf("%s-%d", c.cloudProviderID, versionNumber)
		log.Infof("Creating API key %s/%s, retry: %v", APIKeyType, keyName, i+1)
		key, err = c.TGIAM.CreateOrgAPIKey(context.Background(), keyName, c.orgID, "", "", APIKeyType, &c.TTLInSeconds)
		if err == nil {
			return key.PlaintextKey, versionNumber, nil
		}
		if err == gorm.ErrDuplicatedKey {
			versionNumber++
			log.Warnf("Key %s already exists, retrying", keyName)
			continue
		} else {
			return "", 0, err
		}
	}
	if err != nil {
		return "", 0, err
	}

	return key.PlaintextKey, versionNumber, nil
}

func (c *CloudProviderTokenService) DeleteToken(tokenName string) error {
	// delete token
	err := c.TGIAM.DeleteOrgAPIKey(context.Background(), tokenName, c.orgID, APIKeyType)
	if err != nil {
		return err
	}

	return nil
}

func (c *CloudProviderTokenService) ValidateToken(token string) (int, error) {
	log := logger.L()
	// validate token
	key, err := c.TGIAM.ValidateAPIKey(context.Background(), token)

	if key != nil {
		// check if renew is needed
		// {"Creator":"<EMAIL>","ExpiresAt":"2024-04-18T03:27:37.674075383Z","RedactedKey":"cZZ***********ur"}
		expiresAt, ok := key.MetaData["ExpiresAt"].(string)
		if !ok {
			return 0, fmt.Errorf("token %s does not have ExpiresAt metadata", token)
		}
		// convert string into time
		expireTime, err := time.Parse(time.RFC3339, expiresAt)
		if err != nil {
			log.Errorf("Failed to parse time %s: %v", expiresAt, err)
			return 0, fmt.Errorf("failed to parse time %s: %v", expiresAt, err)
		}

		// check if renew is needed by comparing expire time with RenewThreshold
		ttlInSeconds := int(time.Until(expireTime).Seconds())
		return ttlInSeconds, nil
	} else {
		return 0, err
	}
}
