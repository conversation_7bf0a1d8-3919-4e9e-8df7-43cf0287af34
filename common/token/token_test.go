package token

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"gorm.io/gorm"
)

func TestCloudProviderTokenService_CreateToken(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"

	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	versionNumber := 1
	expectedKeyName := fmt.Sprintf("%s-%d", cloudProviderID, versionNumber)
	expectedKey := &tgIAM.IAMAPIKey{
		PlaintextKey: "testkey",
	}

	tgIAMMock.On("CreateOrgAPIKey", expectedKeyName, orgID, mock.Anything, mock.AnythingOfType("*int")).Return(expectedKey, nil)

	token, actualVer, err := service.CreateToken(versionNumber)

	assert.NoError(t, err)
	assert.Equal(t, expectedKey.PlaintextKey, token)
	assert.Equal(t, versionNumber, actualVer)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_CreateToken_DuplicatedKey(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	versionNumber := 1
	//expectedKeyName := fmt.Sprintf("%s-%d", cloudProviderID, versionNumber)
	expectedKey := &tgIAM.IAMAPIKey{
		PlaintextKey: "testkey",
	}
	emptyKey := &tgIAM.IAMAPIKey{}

	tgIAMMock.On("CreateOrgAPIKey", mock.Anything, orgID, mock.Anything, mock.AnythingOfType("*int")).
		Return(emptyKey, gorm.ErrDuplicatedKey).Once()
	// tgIAMMock.On("DeleteOrgAPIKey", expectedKeyName, orgID, mock.Anything).Return(nil)
	tgIAMMock.On("CreateOrgAPIKey", mock.Anything, orgID, mock.Anything, mock.AnythingOfType("*int")).
		Return(expectedKey, nil).Once()

	token, actualVer, err := service.CreateToken(versionNumber)

	assert.NoError(t, err)
	assert.Equal(t, 2, actualVer)
	assert.Equal(t, expectedKey.PlaintextKey, token)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_CreateToken_Error(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	versionNumber := 1
	expectedKeyName := fmt.Sprintf("%s-%d", cloudProviderID, versionNumber)
	emptyKey := &tgIAM.IAMAPIKey{}

	tgIAMMock.On("CreateOrgAPIKey", expectedKeyName, orgID, mock.Anything, mock.Anything).
		Return(emptyKey, errors.New("failed to create token"))

	token, _, err := service.CreateToken(versionNumber)

	assert.Error(t, err)
	assert.Empty(t, token)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_DeleteToken(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	tokenName := "testtoken"

	tgIAMMock.On("DeleteOrgAPIKey", tokenName, orgID, mock.Anything).Return(nil)

	err := service.DeleteToken(tokenName)

	assert.NoError(t, err)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_DeleteToken_Error(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	tokenName := "testtoken"

	tgIAMMock.On("DeleteOrgAPIKey", tokenName, orgID, mock.Anything).
		Return(errors.New("failed to delete token"))

	err := service.DeleteToken(tokenName)

	assert.Error(t, err)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_ValidateToken_Valid(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	token := "testtoken"
	expectedKey := &tgIAM.IAMAPIKey{
		MetaData: map[string]interface{}{
			"ExpiresAt": "2024-04-18T03:27:37.674075383Z",
		},
	}

	tgIAMMock.On("ValidateAPIKey", token).Return(expectedKey, nil)

	ttl, err := service.ValidateToken(token)

	assert.NoError(t, err)
	assert.Equal(t, int(time.Until(time.Date(2024, 4, 18, 3, 27, 37, 674075383, time.UTC)).Seconds()), ttl)

	tgIAMMock.AssertExpectations(t)
}

func TestCloudProviderTokenService_ValidateToken_Invalid(t *testing.T) {
	const orgID = "org1"
	const cloudProviderID = "cloudprovider1"
	tgIAMMock := new(tgIAM.MockTGIAM)
	ttlInSeconds := 3600

	service := NewCloudProviderTokenService(tgIAMMock, cloudProviderID, orgID, ttlInSeconds)

	token := "testtoken"
	emptyKey := &tgIAM.IAMAPIKey{}

	tgIAMMock.On("ValidateAPIKey", token).Return(emptyKey, errors.New("invalid token"))

	ttl, err := service.ValidateToken(token)

	assert.Error(t, err)
	assert.Equal(t, 0, ttl)

	tgIAMMock.AssertExpectations(t)
}
