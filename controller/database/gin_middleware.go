package database

import (
	"github.com/gin-gonic/gin"
)

// CtxKey is the key of solution service stored in the context
const CtxKey = "Database"

// GinCtxSetter sets the given db in gin.Context
func GinCtxSetter(s DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(CtxKey, s)
	}
}

// GetService gets the db from gin.Context
func GetService(c *gin.Context) DB {
	db, _ := c.Get(CtxKey)
	result, _ := db.(DB)
	return result
}
