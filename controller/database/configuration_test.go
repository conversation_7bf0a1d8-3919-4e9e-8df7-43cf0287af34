package database_test

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"gopkg.in/DATA-DOG/go-sqlmock.v1"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	. "github.com/tigergraph/cloud-universe/controller/database"
)

type ConfigurationSuite struct {
	suite.Suite
	mockDB *sql.DB
	mock   sqlmock.Sqlmock
	db     *Database
}

func (s *ConfigurationSuite) SetupSuite() {
	var (
		gormDB *gorm.DB
		err    error
	)

	s.mockDB, s.mock, err = sqlmock.New()
	s.Require().NoError(err)

	gormDB, err = gorm.Open(postgres.New(postgres.Config{Conn: s.mockDB}))
	s.Require().NoError(err)

	db := &Database{}
	db = db.WithGormDB(gormDB)
	s.db = db
}

func (s *ConfigurationSuite) TearDownSuite() {
	s.mockDB.Close()
}

func (s *ConfigurationSuite) AfterTest(_ string, _ string) {
	s.Require().NoError(s.mock.ExpectationsWereMet())
}

func TestConfiguration(t *testing.T) {
	suite.Run(t, new(ConfigurationSuite))
}

func (s *ConfigurationSuite) TestGetConfiguration() {
	testKey := "test-key"
	testBranch := "test-branch"
	testData := map[string]interface{}{
		"key": "value",
	}
	testTime := time.Now()

	// Test getting configuration from specified branch
	rows := sqlmock.NewRows([]string{"id", "config_key", "branch", "config_value", "created_at", "updated_at"}).
		AddRow(1, testKey, testBranch, []byte(`{"key":"value"}`), testTime, testTime)

	s.mock.ExpectQuery(`SELECT \* FROM "configurations"`).
		WithArgs(testKey, testBranch).
		WillReturnRows(rows)

	config, err := s.db.GetConfiguration(context.Background(), testKey, testBranch)
	s.NoError(err)
	s.Equal(testKey, config.ConfigKey)
	s.Equal(testBranch, config.Branch)

	var result map[string]interface{}
	err = config.GetConfigResult(&result)
	s.NoError(err)
	s.Equal(testData, result)

	// Test fallback to DEFAULT_BRANCH when not found in specified branch
	s.mock.ExpectQuery(`SELECT \* FROM "configurations"`).
		WithArgs(testKey, "another-branch").
		WillReturnError(gorm.ErrRecordNotFound)

	rows = sqlmock.NewRows([]string{"id", "config_key", "branch", "config_value", "created_at", "updated_at"}).
		AddRow(1, testKey, DEFAULT_BRANCH, []byte(`{"key":"value"}`), testTime, testTime)

	s.mock.ExpectQuery(`SELECT \* FROM "configurations"`).
		WithArgs(testKey, DEFAULT_BRANCH).
		WillReturnRows(rows)

	config, err = s.db.GetConfiguration(context.Background(), testKey, "another-branch")
	s.NoError(err)
	s.Equal(testKey, config.ConfigKey)
	s.Equal(DEFAULT_BRANCH, config.Branch)
}

func (s *ConfigurationSuite) TestSetConfiguration() {
	testKey := "test-key"
	testBranch := "test-branch"
	testData := map[string]interface{}{
		"key": "value",
	}

	// Expect the transaction
	s.mock.ExpectBegin()
	s.mock.ExpectExec(`INSERT INTO "configurations" \("created_at","updated_at","config_key","config_value","branch"\) VALUES \(\$1,\$2,\$3,\$4,\$5\)`).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), testKey, []byte(`{"key":"value"}`), testBranch).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.mock.ExpectCommit()

	err := s.db.SetConfiguration(context.Background(), testKey, testBranch, testData)
	s.NoError(err)
}
