package database

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

func (DB *Database) GetConfigurationByID(ctx context.Context, configID uuid.UUID) (*AddonsConfiguration, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	cf := &AddonsConfiguration{}

	err := db.First(cf, configID).Error()

	return cf, err
}

func (DB *Database) CreateAddonsConfiguration(ctx context.Context, newConfig *AddonsConfiguration) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	return db.Create(newConfig).Error()
}

func (DB *Database) UpdateAddonsConfigurationValue(ctx context.Context, configID uuid.UUID, cv string) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	return db.Model(&AddonsConfiguration{}).Where("id = ?", configID).Update("config_value", cv).Error()
}

func (DB *Database) DeleteAddonsConfiguration(ctx context.Context, configID uuid.UUID) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	return db.Delete(&AddonsConfiguration{}, configID).Error()
}

func (DB *Database) ListAddonsConfigurations(ctx context.Context, installedAddonsID uuid.UUID, configName string) ([]AddonsConfiguration, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var cfs []AddonsConfiguration

	err := db.Where("addons_installed_id = ? and config_name = ?", installedAddonsID, configName).Order("created_at").Find(&cfs).Error()

	return cfs, err
}
