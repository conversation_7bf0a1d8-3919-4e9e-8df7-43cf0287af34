package database

import (
	"context"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
	"gorm.io/gorm"
)

func (DB *Database) AddOnboardingTask(ctx context.Context, task *OnboardingTask) (uuid.UUID, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return task.ID, db.FirstOrCreate(&task,
		OnboardingTask{TaskName: task.TaskName, Description: task.Description, Credits: task.Credits}).Error()
}

// Get all onboarding tasks.
func (DB *Database) GetOnboardingTasks(ctx context.Context) (tasks []OnboardingTask, err error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return tasks, db.Order("index").Find(&tasks).Error()
}

func (DB *Database) GetOnboardingTask(ctx context.Context, taskID uuid.UUID) (task OnboardingTask, err error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return task, db.Where("id = ?", taskID).First(&task).Error()
}

// Get entry by task id and user id from table CompletedTasks.
func (DB *Database) GetCompletedTask(ctx context.Context, taskID uuid.UUID, orgID string) (completedTask CompletedTask, err error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return completedTask, db.Where("onboarding_task_id = ? AND org_id = ?", taskID, orgID).First(&completedTask).Error()
}

type OnboardingTaskWithStatus struct {
	OnboardingTask
	Completed bool
}

// Returns all tasks along with their completed status for the given userId.
func (DB *Database) GetTasksWithCompletedStatus(ctx context.Context, orgID string) ([]OnboardingTaskWithStatus, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	var tasks []OnboardingTaskWithStatus

	err := db.Table("onboarding_tasks").
		Select("onboarding_tasks.*, CASE WHEN completed_tasks.onboarding_task_id IS NOT NULL THEN true ELSE false END AS completed").
		Joins("LEFT JOIN completed_tasks ON onboarding_tasks.id = completed_tasks.onboarding_task_id AND completed_tasks.org_id = ?", orgID).
		Order("onboarding_tasks.index ASC").
		Find(&tasks).Error()
	if err != nil {
		return nil, err
	}

	return tasks, nil
}

func (DB *Database) AddCompletedTask(ctx context.Context, task *CompletedTask, afterComplete func() error) (err error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return db.Transaction(func(tx tggorm.IGorm) error {
		if err := tx.Create(task).Error(); err != nil {
			return err
		}

		return afterComplete()
	})
}
