package database

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestSetupdb(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"normal",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, dbInfo, err := CreateCtrlTestDb()
			if (err != nil) != tt.wantErr {
				t.Errorf("Setupdb() error = %v, wantErr %v", err, tt.wantErr)
			}
			require.NotNil(t, dbInfo)
			require.NotEmpty(t, dbInfo.DockerName)

			if err := StopDB(dbInfo); (err != nil) != tt.wantErr {
				t.Errorf("StopDB() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
