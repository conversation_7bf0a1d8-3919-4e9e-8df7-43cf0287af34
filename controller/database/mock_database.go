package database

import (
	"context"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
)

type MockDatabase struct {
	Database
	mock.Mock
}

func (m *MockDatabase) GetOnboardingTask(ctx context.Context, taskID uuid.UUID) (OnboardingTask, error) {
	args := m.Called(ctx, taskID)
	return args.Get(0).(OnboardingTask), args.Error(1)
}

func (m *MockDatabase) GetCompletedTask(ctx context.Context, taskID uuid.UUID, orgID string) (CompletedTask, error) {
	args := m.Called(ctx, taskID, orgID)
	return args.Get(0).(CompletedTask), args.Error(1)
}

func (m *MockDatabase) GetTasksWithCompletedStatus(ctx context.Context, orgID string) ([]OnboardingTaskWithStatus, error) {
	args := m.Called(ctx, orgID)
	return args.Get(0).([]OnboardingTaskWithStatus), args.Error(1)
}

func (m *MockDatabase) AddCompletedTask(ctx context.Context, task *CompletedTask, afterComplete func() error) error {
	args := m.Called(ctx, task, afterComplete)
	return args.Error(0)
}
