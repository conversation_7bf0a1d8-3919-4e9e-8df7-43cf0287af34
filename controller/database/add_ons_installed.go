package database

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

func (DB *Database) InstallAddons(ctx context.Context, orgID string, addonsID uuid.UUID, enable bool) (*AddonsInstalled, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	installedAddons := &AddonsInstalled{}
	r := db.Where(AddonsInstalled{
		OrgID:    orgID,
		AddonsID: addonsID,
	}).Attrs(&AddonsInstalled{
		Enabled: enable,
	}).FirstOrCreate(installedAddons)

	// created
	if r.RowsAffected() > 0 {
		return installedAddons, r.Error()
	}

	if installedAddons.Enabled == enable {
		return installedAddons, nil
	}

	// updated if different
	installedAddons.Enabled = enable

	return installedAddons, db.Save(installedAddons).Error()
}

func (DB *Database) ListInstalledAddons(ctx context.Context, orgID string) ([]AddonsInstalled, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var installedAddonsList []AddonsInstalled
	err := db.Where("org_id = ? and enabled = ?", orgID, true).Find(&installedAddonsList).Error()

	return installedAddonsList, err
}

func (DB *Database) GetAddOnsInstalledByID(ctx context.Context, installedAddonsID uuid.UUID) (*AddonsInstalled, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	installedAddons := &AddonsInstalled{}
	if err := db.First(installedAddons, installedAddonsID).Error(); err != nil {
		return nil, err
	}

	return installedAddons, nil
}

func (DB *Database) UpdateInstalledAddons(ctx context.Context, installedAddonsID uuid.UUID, enable bool) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	return db.Model(&AddonsInstalled{}).Where("id = ?", installedAddonsID).Update("enabled", enable).Error()
}
