package database

import (
	"fmt"

	fileStore "github.com/tigergraph/cloud-universe/file_store/database"
	tgLogger "github.com/tigergraph/cloud-universe/utils/logger"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/prometheus"
)

// Database is gorm.DB.
// Allows for native receivers.
type Database struct {
	db tggorm.IGorm
}

// OpenDB uses or creates a new database under the path.
func OpenDB(dbType, uri string, logSql bool) (*Database, error) {
	var db *gorm.DB
	var err error
	log := tgLogger.L().GormWrapper()
	gormLogger := log.LogMode(logger.Silent)
	if logSql {
		gormLogger = gormLogger.LogMode(logger.Info)
	}

	switch dbType {
	case "postgres":
		if db, err = gorm.Open(postgres.Open(uri),
			&gorm.Config{Logger: gormLogger, AllowGlobalUpdate: true}); err == nil {
			db.Session(&gorm.Session{Logger: logger.Discard}).
				Use(prometheus.New(prometheus.Config{
					DBName: "controller",
					MetricsCollector: []prometheus.MetricsCollector{
						&prometheus.Postgres{},
					},
				}))
		}
	default:
		return nil, fmt.Errorf("dbType:%s not supported yet", dbType)
	}

	if err != nil {
		return nil, err
	}

	gormiface := tggorm.NewDB(db)

	if dbType == "postgres" {
		gormiface.Exec("create extension if not exists hstore;")

		gormiface.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")

		gormiface.Exec(`
		DO $$ BEGIN
			CREATE TYPE enum_file_target_type AS ENUM ('org', 'user');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;`)

		gormiface.Exec(`
		DO $$ BEGIN
			CREATE TYPE enum_file_perm AS ENUM ('view', 'edit');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;`)
	}

	gormiface.AutoMigrate(
		&User{},
		&DeletedUser{},
		&Log{},
		&OnboardingTask{},
		&CompletedTask{},
		&fileStore.FileStore{},
		&fileStore.FileSharePerm{},
		&AddonsInstalled{},
		&AddonsConfiguration{},
		&Configuration{},
	)

	return &Database{db: gormiface}, nil
}

// GormDB returns GormDB instance.
// Should try to avoid calling it as it violates the abstraction,
// but sometimes it's provides the flexibility to customize some business
// logic from upper layer that db layer should not know.
func (db *Database) GormDB() tggorm.IGorm {
	return db.db
}

func (db *Database) WithGormDB(gormdb *gorm.DB) *Database {
	db.db = tggorm.NewDB(gormdb)
	return db
}
