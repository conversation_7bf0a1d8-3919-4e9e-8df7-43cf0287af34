package database

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/cloud-universe/utils/lock"
	"github.com/tigergraph/cloud-universe/utils/tggorm"
	"gorm.io/gorm"
)

type DB interface {
	AddCompletedTask(ctx context.Context, task *CompletedTask, afterComplete func() error) (err error)
	AddLog(level string, issueType string, subject string, details interface{}) error
	AddOnboardingTask(ctx context.Context, task *OnboardingTask) (uuid.UUID, error)
	AddUser(ctx context.Context, u *User) (uuid.UUID, error)
	CreateAddonsConfiguration(ctx context.Context, newConfig *AddonsConfiguration) error
	DeleteAddonsConfiguration(ctx context.Context, configID uuid.UUID) error
	DeleteUser(ctx context.Context, userID uuid.UUID) error
	GetAddOnsInstalledByID(ctx context.Context, installedAddonsID uuid.UUID) (*AddonsInstalled, error)
	GetAllLogs() ([]Log, error)
	GetAllUserIDs(ctx context.Context) ([]uuid.UUID, error)
	GetAllUsers(ctx context.Context) ([]User, error)
	GetCompletedTask(ctx context.Context, taskID uuid.UUID, orgID string) (completedTask CompletedTask, err error)
	GetConfiguration(ctx context.Context, configKey string, branch string) (*Configuration, error)
	GetConfigurationByID(ctx context.Context, configID uuid.UUID) (*AddonsConfiguration, error)
	GetLegacyUserFromEmail(ctx context.Context, email string) (*User, error)
	GetNewUserStats(ctx context.Context, days int) (*NewUserStats, error)
	GetOnboardingTask(ctx context.Context, taskID uuid.UUID) (task OnboardingTask, err error)
	GetOnboardingTasks(ctx context.Context) (tasks []OnboardingTask, err error)
	GetTasksWithCompletedStatus(ctx context.Context, orgID string) ([]OnboardingTaskWithStatus, error)
	GetUser(ctx context.Context, userID uuid.UUID) (*User, error)
	GetUserFromEmail(ctx context.Context, email string) (*User, error)
	GetUserFromOrgID(ctx context.Context, orgID string) (*User, error)
	GetUserFromOrgIDOrEmail(ctx context.Context, orgID string, orgOwnerEmail string) (*User, error)
	GetUserID(ctx context.Context, email string) (uuid.UUID, error)
	GormDB() tggorm.IGorm
	InsertOnboardingTasks() error
	InstallAddons(ctx context.Context, orgID string, addonsID uuid.UUID, enable bool) (*AddonsInstalled, error)
	IsUserDeleted(ctx context.Context, userID uuid.UUID) (bool, error)
	ListAddonsConfigurations(ctx context.Context, installedAddonsID uuid.UUID, configName string) ([]AddonsConfiguration, error)
	ListInstalledAddons(ctx context.Context, orgID string) ([]AddonsInstalled, error)
	MarkReceivedFreeTierWarningEmail(ctx context.Context, userID uuid.UUID) error
	MarkReceivedWarningEmail(ctx context.Context, userID uuid.UUID) error
	MarkRequiresCardSince(ctx context.Context, userID uuid.UUID) (*time.Time, error)
	MigrateIndividualUser(ctx context.Context, orgID string, orgOwnerEmail string) error
	SetInternal(ctx context.Context, userOrgID string, internal bool) error
	SetUserBeingDeleted(ctx context.Context, userID uuid.UUID, isBeingDeleted bool) error
	UnmarkRequiresCardSince(ctx context.Context, userID uuid.UUID) error
	UpdateAddonsConfigurationValue(ctx context.Context, configID uuid.UUID, cv string) error
	UpdateDB(lockSvc lock.DLockService)
	UpdateInstalledAddons(ctx context.Context, installedAddonsID uuid.UUID, enable bool) error
	UpdateUser(ctx context.Context, orgOwnerEmail string, orgName string, orgID string, isGlobalChallengeUser bool) error
	UpdateUserLastBilled(ctx context.Context, userID uuid.UUID, lastBilled *time.Time) error
	WithGormDB(gormdb *gorm.DB) *Database
}
