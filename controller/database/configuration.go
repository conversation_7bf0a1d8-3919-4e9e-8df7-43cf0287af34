package database

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm/dialects/postgres"
	"gorm.io/gorm"
)

const (
	DEFAULT_BRANCH = "main"
)

func (DB *Database) GetConfiguration(ctx context.Context, configKey string, branch string) (*Configuration, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	cf := &Configuration{}

	err := db.Where("config_key = ? and branch = ?", configKey, branch).Order("updated_at DESC").First(cf).Error()
	if err == gorm.ErrRecordNotFound && branch != DEFAULT_BRANCH {
		cf = &Configuration{} // Reset cf for the next query
		err = db.Where("config_key = ? and branch = ?", configKey, DEFAULT_BRANCH).Order("updated_at DESC").First(cf).Error()
	}

	return cf, err
}

func (DB *Database) SetConfiguration(ctx context.Context, configKey string, branch string, configData any) error {
	value := postgres.Jsonb{}
	bs, err := json.Marshal(configData)
	if err != nil {
		return err
	}
	if err := value.Scan(bs); err != nil {
		return err
	}

	db := DB.db.Session(&gorm.Session{Context: ctx})
	cf := &Configuration{
		ConfigKey:   configKey,
		Branch:      branch,
		ConfigValue: value,
	}

	return db.Create(cf).Error()
}

func (cf *Configuration) GetConfigResult(result any) error {
	jsonbData, err := cf.ConfigValue.MarshalJSON()
	if err != nil {
		return fmt.Errorf("error marshaling JSONB: %v", err)
	}

	if err := json.Unmarshal(jsonbData, &result); err != nil {
		return fmt.Errorf("error unmarshaling JSON: %v", err)
	}

	return nil
}
