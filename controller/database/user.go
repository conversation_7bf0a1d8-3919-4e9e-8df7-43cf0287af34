package database

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	_ "github.com/jinzhu/gorm/dialects/postgres" // Initialze postgres dialect.
	"github.com/tigergraph/cloud-universe/utils/typeconv"
	"gorm.io/gorm"
)

// UserDBInfo is used exclusively by GetAllUsersInfo.
type UserDBInfo struct {
	OrgID                   *string // The org_id claim in auth0 token.
	CreatedAt               time.Time
	Email                   string
	Name                    string
	Balance                 float64
	HasCard                 bool
	Internal                bool
	CurrentMonthUsage       float64
	CurrentMonthUnpaidUsage float64
	AWSCredits              int64
	AzureCredits            int64
	GCPCredits              int64
	UniversalCredits        int64
	AWSCreditLimit          int64
	AzureCreditLimit        int64
	GCPCreditLimit          int64
	UniversalCreditLimit    int64
}

// UserInfo is used exclusively by GetAllUsersInfo.
// RealtimeBalance is not part of the userDBInfo since it is not scanned by the database
// and cannot be set manually (not updatable).
type UserInfo struct {
	UserDBInfo
	RealtimeBalance float64
}

// NewUserStats contains data about new users' activity
type NewUserStats struct {
	Registered      int
	CreatedSolution int
}

// AddUser adds a user to the database and returns his ID.
func (DB *Database) AddUser(ctx context.Context, u *User) (uuid.UUID, error) {
	if u.Email == "" {
		return uuid.Nil, fmt.Errorf("cannot add a user without an email")
	}
	if u.OrgID == nil {
		return uuid.Nil, fmt.Errorf("cannot add a user without an org id")
	}
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return u.ID, db.FirstOrCreate(u, User{OrgID: u.OrgID}).Error()
}

// GetUser returns the user matching the id.
func (DB *Database) GetUser(ctx context.Context, userID uuid.UUID) (*User, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return nil, fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserFromEmail returns the user matching the email.
// TODO to be deprecated since controller will rely solely on User.OrgID or User.ID as the unique identifier.
func (DB *Database) GetUserFromEmail(ctx context.Context, email string) (*User, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, User{Email: email}).Error()
	if gorm.ErrRecordNotFound == err {
		return nil, fmt.Errorf("the user [email=%v] does not exist: %w", email, err)
	} else if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetLegacyUserFromEmail returns the legacy user in db with org_id == nil and a matching email.
func (DB *Database) GetLegacyUserFromEmail(ctx context.Context, email string) (*User, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "email = ? AND org_id IS NULL", email).Error()
	if gorm.ErrRecordNotFound == err {
		return nil, fmt.Errorf("the user [email=%v] does not exist: %w", email, err)
	} else if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserFromOrgID returns the user matching the OrgID.
func (DB *Database) GetUserFromOrgID(ctx context.Context, orgID string) (*User, error) {
	// WARNING: should not return a user when orgID is empty.
	if orgID == "" {
		return nil, fmt.Errorf("OrgID is empty")
	}

	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, User{OrgID: typeconv.String(orgID)}).Error()
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserFromOrgIDOrEmail returns a user with a matching orgID or orgOWnerEmail.
// If orgID is provided, orgOwnerEmail will be ignored in matching.
// If orgID is not provided, a legacy user with a matching email will be returned.
func (DB *Database) GetUserFromOrgIDOrEmail(ctx context.Context, orgID, orgOwnerEmail string) (*User, error) {
	if orgID == "" && orgOwnerEmail == "" {
		return nil, fmt.Errorf("orgID and orgOwnerEmail are empty")
	}

	if orgID != "" {
		return DB.GetUserFromOrgID(ctx, orgID)
	} else {
		return DB.GetLegacyUserFromEmail(ctx, orgOwnerEmail)
	}
}

// MigrateIndividualUser checks if there is a matching existing account and fill the OrgID column.
func (DB *Database) MigrateIndividualUser(ctx context.Context, orgID string, orgOwnerEmail string) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return db.Model(&User{}).Where("email = ? AND org_id IS NULL", orgOwnerEmail).Update("org_id", orgID).Error()
}

// UpdateUser updates the user with the matching orgID (for newly pulled org meta info updates).
func (DB *Database) UpdateUser(ctx context.Context, orgOwnerEmail, orgName, orgID string, isGlobalChallengeUser bool) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, User{OrgID: typeconv.String(orgID)}).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [OrgID=%v] does not exist: %w", orgID, err)
	} else if err != nil {
		return err
	}

	user.Name = orgName
	user.Email = orgOwnerEmail
	user.IsGlobalChallengeUser = isGlobalChallengeUser
	return db.Save(&user).Error()
}

// DeleteUser removes the user from the users table (to the deleted_users table).
// If the user is not found, it is assumed to be deleted and nil error is returned.
func (DB *Database) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return nil
	} else if err != nil {
		return err
	}

	// Check email to make sure we have a proper where clause in the delete query.
	if user.Email == "" {
		return fmt.Errorf("cannot delete a user with no email")
	}

	deletedUser := DeletedUser{
		StripeCustomerID: user.StripeCustomerID,
		AccountType:      user.AccountType,
		LastBilled:       user.LastBilled,
	}
	err = db.Create(&deletedUser).Error()
	if err != nil {
		return err
	}

	// Other ways to set the same id (creating with id, gorm update method)
	// result in deleted user having a new id.
	err = db.Exec("UPDATE deleted_users SET id = ? WHERE id = ?", userID, deletedUser.ID).Error()
	if err != nil {
		return err
	}

	// Using db.Delete will only mark deletedAt (gorm specified behavior for tables with deleted_at col).
	return db.Exec("DELETE FROM users WHERE id = ?", user.ID).Error()
}

// IsUserDeleted checks if user ID is found in the deleted_users table.
func (DB *Database) IsUserDeleted(ctx context.Context, userID uuid.UUID) (bool, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var deletedUser DeletedUser
	err := db.First(&deletedUser, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return false, nil
	} else if err != nil {
		return false, err
	}
	return true, nil
}

// SetUserBeingDeleted sets IsBeingDeleted field on the user.
func (DB *Database) SetUserBeingDeleted(ctx context.Context, userID uuid.UUID, isBeingDeleted bool) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	return db.Exec("UPDATE users SET is_being_deleted = ? WHERE id = ?", isBeingDeleted, userID).Error()
}

// GetUserID returns the ID of the corresponding user.
func (DB *Database) GetUserID(ctx context.Context, email string) (uuid.UUID, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, User{Email: email}).Error()
	if gorm.ErrRecordNotFound == err {
		return user.ID, fmt.Errorf("the user [email=%v] does not exist: %w", email, err)
	}
	return user.ID, err
}

// GetAllUserIDs returns the IDs of all existing users.
func (DB *Database) GetAllUserIDs(ctx context.Context) ([]uuid.UUID, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var users []User
	if err := db.Select("id").Find(&users).Error(); err != nil {
		return nil, fmt.Errorf("the users' IDs cannot be retrieved: %w", err)
	}

	var ids = make([]uuid.UUID, 0)
	for _, user := range users {
		ids = append(ids, user.ID)
	}
	return ids, nil
}

// GetAllUsers returns all existing users.
func (DB *Database) GetAllUsers(ctx context.Context) ([]User, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var users []User
	if err := db.Find(&users).Error(); err != nil {
		return nil, fmt.Errorf("the users cannot be retrieved: %w", err)
	}
	return users, nil
}

// UpdateUserLastBilled updates the LastBilled column of the user.
func (DB *Database) UpdateUserLastBilled(ctx context.Context, userID uuid.UUID, lastBilled *time.Time) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return err
	}

	user.LastBilled = lastBilled
	return db.Save(&user).Error()
}

// MarkRequiresCardSince marks the RequiresCardSince field with the current time.
// If RequiresCardSince is not nil, it keeps the previous value.
func (DB *Database) MarkRequiresCardSince(ctx context.Context, userID uuid.UUID) (*time.Time, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return nil, fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return nil, err
	}

	if user.RequiresCardSince != nil {
		return user.RequiresCardSince, nil // The card is already required. Keep the older value.
	}

	now := time.Now()
	user.RequiresCardSince = &now
	return &now, db.Save(&user).Error()
}

// UnmarkRequiresCardSince unmarks the RequiresCardSince field (sets to nil).
func (DB *Database) UnmarkRequiresCardSince(ctx context.Context, userID uuid.UUID) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return err
	}

	return db.Model(&user).Update("requires_card_since", gorm.Expr("NULL")).Error()
}

// MarkReceivedWarningEmail marks the ReceivedWarningEmail field with time.Now().
func (DB *Database) MarkReceivedWarningEmail(ctx context.Context, userID uuid.UUID) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return err
	}

	now := time.Now()
	user.ReceivedWarningEmail = &now
	return db.Save(&user).Error()
}

// MarkReceivedFreeTierWarningEmail marks the ReceivedFreeTierWarningEmail field with time.Now().
func (DB *Database) MarkReceivedFreeTierWarningEmail(ctx context.Context, userID uuid.UUID) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "id = ?", userID).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [id=%v] does not exist: %w", userID, err)
	} else if err != nil {
		return err
	}

	now := time.Now()
	user.ReceivedFreeTierWarningEmail = &now
	return db.Save(&user).Error()
}

// SetInternal sets field Internal on the user to the value
func (DB *Database) SetInternal(ctx context.Context, userOrgID string, internal bool) error {
	db := DB.db.Session(&gorm.Session{Context: ctx})
	var user User
	err := db.First(&user, "org_id = ?", userOrgID).Error()
	if gorm.ErrRecordNotFound == err {
		return fmt.Errorf("the user [OrgID=%v] does not exist: %w", userOrgID, err)
	} else if err != nil {
		return err
	}

	return db.Model(&user).Updates(map[string]interface{}{"internal": internal}).Error()
}

// GetNewUserStats returns extra info for all existing users.
func (DB *Database) GetNewUserStats(ctx context.Context, days int) (*NewUserStats, error) {
	db := DB.db.Session(&gorm.Session{Context: ctx})

	threshold := time.Now().AddDate(0, 0, -days)
	stats := &NewUserStats{}

	err := db.Raw(`
	select
		count(t.email) as registered,
		count(case when t.sol_cnt > 0 then 1 end) as created_solution
	from
		(
			select
				users.email as "email",
				count(user_solutions.id) as "sol_cnt"
			from
				users
				left join user_solutions on users.id = user_solutions.user_id
			where users.created_at > ?
			group by users.email
		) as t
	`, threshold.Format(time.RFC3339)).Scan(stats).Error()

	return stats, err
}
