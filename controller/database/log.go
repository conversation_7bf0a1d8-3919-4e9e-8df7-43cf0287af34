package database

import (
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm/dialects/postgres"
)

// AddLog adds a log.
func (DB *Database) AddLog(level string, issueType, subject string, details interface{}) error {
	db := DB.db
	dts, _ := json.Marshal(&details)
	return db.Create(&Log{
		Level:   level,
		Type:    issueType,
		Subject: subject,
		Details: postgres.Jsonb{
			RawMessage: dts,
		},
	}).Error()
}

// GetAllLogs returns all existing logs.
func (DB *Database) GetAllLogs() ([]Log, error) {
	db := DB.db
	var logs []Log
	if err := db.Find(&logs).Error(); err != nil {
		return nil, fmt.Errorf("the logs cannot be retrieved: %w", err)
	}
	return logs, nil
}
