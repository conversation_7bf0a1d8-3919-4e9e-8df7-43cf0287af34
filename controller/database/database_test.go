package database_test

// Basic imports
import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"

	"github.com/tigergraph/cloud-universe/controller/database"
	"github.com/tigergraph/cloud-universe/utils/typeconv"
)

type DatabaseTestSuite struct {
	suite.Suite
	dbInfo *database.DBInfo
	db     *database.Database
}

func (suite *DatabaseTestSuite) SetupTest() {
	require := suite.Require()
	db, dbInfo, err := database.CreateCtrlTestDb()
	require.NoError(err)
	require.NotNil(db)
	require.NotNil(db.GormDB())
	require.NotNil(dbInfo)

	suite.db = db
	suite.dbInfo = dbInfo
}

func (suite *DatabaseTestSuite) TearDownTest() {
	suite.NoError(database.StopDB(suite.dbInfo))
}

func TestDatabaseSuite(t *testing.T) {
	dbSuite := &DatabaseTestSuite{}
	suite.Run(t, dbSuite)
}

func (suite *DatabaseTestSuite) TestUserAPIs() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)
	// Add user
	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org1"),
		Email:       "email",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)
	// Add user
	user2ID, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org2"),
		Email:       "email2",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)

	// Test: get existing user
	user, err := db.GetUser(context.TODO(), userID)
	require.Equal(userID, user.ID)
	require.NoError(err)
	require.NotNil(user)
	now := time.Now()
	require.Truef(
		now.Before(user.CreatedAt.Add(time.Second*10)),
		"the user record should have been created within 10 seconds, now: %v, createdAt: %v",
		now, user.CreatedAt,
	)

	// Test: Delete existing user
	deleted, err := db.IsUserDeleted(context.TODO(), userID)
	require.False(deleted)
	require.NoError(err)
	require.NoError(db.DeleteUser(context.TODO(), userID))
	deleted, err = db.IsUserDeleted(context.TODO(), userID)
	require.True(deleted)
	require.NoError(err)

	// Test: get non-existing user
	user, err = db.GetUser(context.TODO(), userID)
	require.Error(err)
	require.Nil(user)

	// Test: get exisiting user after deletion of another user
	user2, err := db.GetUser(context.TODO(), user2ID)
	require.NoError(err)
	require.NotNil(user2)
	require.NoError(db.DeleteUser(context.TODO(), user2ID))

	// Test: delete non-existing user
	require.NoError(db.DeleteUser(context.TODO(), userID))

	// Test: check deleted user record
	deletedUser := &database.DeletedUser{}
	require.NoError(db.GormDB().Find(deletedUser, "id = ?", userID).Error())
	require.NotNil(deletedUser)
	require.Equal(userID, deletedUser.ID)
}

func (suite *DatabaseTestSuite) TestUserRequiresCardSince() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org_1"),
		Email:       "email",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)

	user, err := db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Nil(user.RequiresCardSince)

	db.MarkRequiresCardSince(context.TODO(), userID)
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user.RequiresCardSince)

	// Test: RequiresCard field timestamp
	require.True(user.RequiresCardSince.Before(time.Now()))
	require.True(time.Since(*user.RequiresCardSince) < time.Second)

	// Test: Mark RequiresCard again, field should not be updated
	oldValue := user.RequiresCardSince
	db.MarkRequiresCardSince(context.TODO(), userID)
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Equal(oldValue, user.RequiresCardSince)

	// Test: MarkReceivedWarningEmail
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Nil(user.ReceivedWarningEmail)
	db.MarkReceivedWarningEmail(context.TODO(), userID)
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user.ReceivedWarningEmail)
	require.True(user.ReceivedWarningEmail.Before(time.Now()))
	require.True(time.Since(*user.ReceivedWarningEmail) < time.Second)

	// Test: MarkReceivedWarningEmail again, field should be updated
	oldValue = user.ReceivedWarningEmail
	db.MarkReceivedWarningEmail(context.TODO(), userID)
	user, err = db.GetUser(context.TODO(), userID)
	require.NotNil(user)
	require.NoError(err)
	require.NotEqual(oldValue, user.ReceivedWarningEmail)

	// Test: Handling unmark requirescard
	require.NoError(db.UnmarkRequiresCardSince(context.TODO(), userID))
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Nil(user.RequiresCardSince)
	require.NoError(db.DeleteUser(context.TODO(), userID))

	// Test: Handling non-existing user
	require.Error(db.UnmarkRequiresCardSince(context.TODO(), userID))
	t, err := db.MarkRequiresCardSince(context.TODO(), userID)
	require.Error(err)
	require.Nil(t)
	require.Error(db.MarkReceivedWarningEmail(context.TODO(), userID))
}

func (suite *DatabaseTestSuite) TestMarkUserForLegacyUsers() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	// Have to provide an org id otherwise AddUser will return error.
	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org_1"),
		Email:       "email",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)

	// Remove the org id to mock legacy user.
	err = db.GormDB().Exec("update users set org_id = null where id = ?", userID).Error()
	require.NoError(err)

	// Try to mark the user with RequiresCardSince to now.
	t, err := db.MarkRequiresCardSince(context.TODO(), userID)
	require.NotNil(t)
	require.NoError(err)

	// Re-select the user from db and see if org id is still null.
	user, err := db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Equal(userID, user.ID)
	require.Equal("email", user.Email)
	require.Nil(user.OrgID) // The org id shall remain as null value.

	// Try to mark the user with ReceivedWarningEmail to now.
	err = db.MarkReceivedWarningEmail(context.TODO(), userID)
	require.NoError(err)

	// Re-select the user from db and see if org id is still null.
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Equal(userID, user.ID)
	require.Equal("email", user.Email)
	require.Nil(user.OrgID) // The org id shall remain as null value.

	// Try to mark the user with ReceivedFreeTierWarningEmail to now.
	err = db.MarkReceivedFreeTierWarningEmail(context.TODO(), userID)
	require.NoError(err)

	// Re-select the user from db and see if org id is still null.
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.Equal(userID, user.ID)
	require.Equal("email", user.Email)
	require.Nil(user.OrgID) // The org id shall remain as null value.
}

func (suite *DatabaseTestSuite) TestGetLegacyUserFromEmail() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	// Have to provide an org id otherwise AddUser will return error.
	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org_1"),
		Email:       "email1",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)

	// Remove the org id to mock legacy user.
	err = db.GormDB().Exec("update users set org_id = null where id = ?", userID).Error()
	require.NoError(err)

	// Also create a user with same email but with an org_id.
	userID2, err := db.AddUser(context.TODO(), &database.User{
		OrgID:       typeconv.String("org_2"),
		Email:       "email1",
		AccountType: database.UserAccountTypeFree,
	})
	require.NoError(err)

	// Try to see if we can get the legacy user, not the migrated user (with an org_id).
	u, err := db.GetLegacyUserFromEmail(context.TODO(), "email1")
	require.NotNil(u)
	require.NoError(err)
	require.Equal(u.ID, userID)
	require.Equal(u.Email, "email1")
	require.NotEqual(u.ID, userID2)
}

func (suite *DatabaseTestSuite) TestSetInterval() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	// Add user
	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID: typeconv.String("org_1"),
		Email: "interval-tester",
	})
	require.NoError(err)

	// Test: get exisiting user
	user, err := db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user)
	require.False(user.Internal)

	require.NoError(db.SetInternal(context.TODO(), *user.OrgID, true))
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user)
	require.True(user.Internal)

	require.NoError(db.SetInternal(context.TODO(), *user.OrgID, false))
	user, err = db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user)
	require.False(user.Internal)

	require.NoError(db.DeleteUser(context.TODO(), userID))
}

func (suite *DatabaseTestSuite) TestUpdateUser() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	userID, err := db.AddUser(context.TODO(), &database.User{
		OrgID: typeconv.String("org_123456"),
		Email: "update-user-tester",
	})
	require.NoError(err)

	err = db.UpdateUser(context.TODO(), "<EMAIL>", "Big Org", "org_123456", false)
	require.NoError(err)

	user, err := db.GetUser(context.TODO(), userID)
	require.NoError(err)
	require.NotNil(user)
	require.Equal("Big Org", user.Name)
	require.Equal("<EMAIL>", user.Email)
	require.Equal("org_123456", *user.OrgID)

	// Test: Delete existing User
	require.NoError(db.DeleteUser(context.TODO(), userID))
}

func (suite *DatabaseTestSuite) TestGetTasksWithCompletedStatus() {
	require := suite.Require()
	db := suite.db
	require.NotNil(db)

	// Add onboarding task
	taskID, err := db.AddOnboardingTask(context.TODO(), &database.OnboardingTask{
		TaskName:    "task1",
		Description: "task1",
		Credits:     10,
	})
	require.NoError(err)

	// Test: get all tasks along with their completed status for the given userId
	tasks, err := db.GetTasksWithCompletedStatus(context.TODO(), orgID)
	require.NoError(err)
	require.Len(tasks, 1)
	require.Equal(taskID, tasks[0].ID)
	require.Equal("task1", tasks[0].TaskName)
	require.Equal("task1", tasks[0].Description)
	require.Equal(10, tasks[0].Credits)
	require.False(tasks[0].Completed)

	// Add completed task
	err = db.AddCompletedTask(context.TODO(), &database.CompletedTask{
		OnboardingTaskID: taskID,
		OrgID:            orgID,
		ReceivedCredits:  10,
	}, func() error {
		return nil
	})
	require.NoError(err)

	tasks, err = db.GetTasksWithCompletedStatus(context.TODO(), orgID)
	require.NoError(err)
	require.Len(tasks, 1)
	require.Equal(taskID, tasks[0].ID)
	require.True(tasks[0].Completed)
}
