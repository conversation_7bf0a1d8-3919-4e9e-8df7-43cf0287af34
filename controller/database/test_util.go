package database

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
	"github.com/docker/go-connections/nat"
	"github.com/google/uuid"
)

const (
	postgresImage   = "postgres:latest"
	postgresUser    = "test_user"
	postgrePassword = "test_passwd"
	postgreDbname   = "test_db"
)

type DBInfo struct {
	DockerName string
}

func getDBPort(ctx context.Context, cli *client.Client, containerID string) (string, error) {
	for {
		ctJson, err := cli.ContainerInspect(ctx, containerID)
		if err != nil {
			return "", fmt.Errorf("error when insspect container: %s, error: %v", containerID, err)
		}

		ports := ctJson.NetworkSettings.NetworkSettingsBase.Ports["5432/tcp"]

		if ctJson.State.Running && len(ports) > 0 {
			return ports[0].HostPort, nil
		}
		fmt.Println("checking port...")

		time.Sleep(100 * time.Millisecond)
	}
}

func CreateTestDb() (string, *DBInfo, error) {
	ctx := context.Background()
	os.Setenv("DOCKER_API_VERSION", "1.43")
	cli, err := client.NewClientWithOpts(client.FromEnv)
	if err != nil {
		return "", nil, fmt.Errorf("error when create docker client: %v", err)
	}

	defer cli.Close()
	pres, err := cli.ImagePull(ctx, postgresImage, types.ImagePullOptions{})
	defer func() {
		if pres != nil {
			pres.Close()
		}
	}()

	if err != nil {
		return "", nil, fmt.Errorf("error when pull image:%s error: %v", "postgres", err)
	}

	imgDownloaded := false
	for {
		imgs, err := cli.ImageList(ctx, types.ImageListOptions{})
		if err != nil {
			return "", nil, fmt.Errorf("error when list images: %v", err)
		}

		for _, image := range imgs {
			for _, repoTag := range image.RepoTags {
				if strings.Contains(repoTag, postgresImage) {
					imgDownloaded = true
					break
				}
			}
			if imgDownloaded {
				break
			}
		}

		if imgDownloaded {
			break
		}

		time.Sleep(5 * time.Second)
	}

	containerName := uuid.New().String()
	resp, err := cli.ContainerCreate(ctx, &container.Config{
		Image: postgresImage,
		Env: []string{
			"POSTGRES_PASSWORD=" + postgrePassword,
			"POSTGRES_USER=" + postgresUser,
			"POSTGRES_DB=" + postgreDbname,
		},
		ExposedPorts: nat.PortSet{
			"5432/tcp": struct{}{},
		},
	}, &container.HostConfig{
		PortBindings: nat.PortMap{
			"5432/tcp": []nat.PortBinding{
				{
					HostIP: "0.0.0.0",
				},
			},
		},
	}, nil, nil, containerName)
	if err != nil {
		return "", nil, fmt.Errorf("error when create container: %s, error: %v", containerName, err)
	}

	if err := cli.ContainerStart(ctx, resp.ID, container.StartOptions{}); err != nil {
		return "", nil, fmt.Errorf("error when start container: %s, error: %v", containerName, err)
	}

	fmt.Println("Container started:", resp.ID)

	dbPort, err := getDBPort(ctx, cli, resp.ID)
	if err != nil {
		return "", nil, err
	}

	dbInfo := &DBInfo{DockerName: containerName}
	dsn := fmt.Sprintf(
		"host=%v port=%v user=%v password=%v dbname=%v sslmode=%v",
		"localhost",
		dbPort,
		postgresUser,
		postgrePassword,
		postgreDbname,
		"disable",
	)

	return dsn, dbInfo, nil
}

func CreateCtrlTestDb() (*Database, *DBInfo, error) {
	dsn, dbInfo, err := CreateTestDb()
	if err != nil {
		return nil, nil, err
	}
	// wait for postgres to be ready
	retry := 0
	maxRetry := 3
	var database *Database
	for ; retry < maxRetry; retry++ {
		database, err = OpenDB("postgres", dsn, false)
		if err == nil {
			return database, dbInfo, nil
		}
		time.Sleep(5 * time.Second)
	}

	return nil, nil, fmt.Errorf("expect postgres to be ready within %v retries, tried: %v", maxRetry, retry)
}

func StopDB(db *DBInfo) error {
	if db == nil {
		return nil
	}

	ctx := context.Background()

	cli, err := client.NewClientWithOpts(client.FromEnv)
	if err != nil {
		return fmt.Errorf("error when create docker client: %v", err)
	}

	defer cli.Close()

	containers, err := cli.ContainerList(ctx, container.ListOptions{})
	if err != nil {
		return fmt.Errorf("error when list container: %v", err)
	}

	var containerID string
	for _, container := range containers {
		for _, name := range container.Names {
			if name == "/"+db.DockerName {
				containerID = container.ID
				break
			}
		}
	}

	if containerID == "" {
		return fmt.Errorf("container with name:%s not found", db.DockerName)
	}

	if err := cli.ContainerRemove(ctx, containerID, container.RemoveOptions{
		Force: true,
	}); err != nil {
		return fmt.Errorf("error when remove container with name:%s, containerId:%s", db.DockerName, containerID)
	}

	return nil
}
