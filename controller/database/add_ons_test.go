package database_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/tigergraph/cloud-universe/controller/database"
	"gorm.io/gorm"
)

type DatabaseCommonTestSuite struct {
	suite.Suite
	dbInfo *database.DBInfo
	db     *database.Database
}

type AddonsTestSuite struct {
	DatabaseCommonTestSuite
}

func (suite *DatabaseCommonTestSuite) SetupTest() {
	require := suite.Require()
	db, dbInfo, err := database.CreateCtrlTestDb()

	require.NoError(err)
	require.NotNil(db)
	require.NotNil(db.GormDB())
	require.NotNil(dbInfo)

	suite.db = db
	suite.dbInfo = dbInfo
}

func (suite *DatabaseCommonTestSuite) TearDownTest() {
	suite.NoError(database.StopDB(suite.dbInfo))
}

const (
	orgID        = "test_org_id"
	addonID      = "e2484ed8-6dcb-4881-ba0f-148d138aa0b7"
	emptyAddONID = "00000000-0000-0000-0000-000000000000"
)

func (suite *AddonsTestSuite) TestAddonsCreateConfiguration() {
	r := suite.Require()
	// initialize installed addons
	adi, err := suite.db.InstallAddons(context.TODO(), orgID, uuid.MustParse(addonID), true)
	r.Nil(err)
	r.NotNil(adi)
	r.True(adi.Enabled)

	_, err = suite.db.InstallAddons(context.TODO(), orgID, uuid.MustParse(addonID), false)
	r.Nil(err)

	adi2, err := suite.db.GetAddOnsInstalledByID(context.TODO(), adi.ID)
	r.Nil(err)
	r.Equal(adi2.ID, adi.ID)
	r.False(adi2.Enabled)

	// creation
	cf := &database.AddonsConfiguration{
		AddonsInstalledID: adi.ID,
		ConfigName:        "testConfigName",
		ConfigValue:       "{\"test\": 123}",
	}
	err = suite.db.CreateAddonsConfiguration(context.TODO(), cf)
	r.Nil(err)
	r.NotEmpty(cf.ID)

	// get by id
	cf0, err0 := suite.db.GetConfigurationByID(context.TODO(), cf.ID)
	r.Nil(err0)
	r.NotNil(cf0)

	// get list
	cfl, err1 := suite.db.ListAddonsConfigurations(context.TODO(), adi.ID, cf.ConfigName)
	r.Nil(err1)
	r.GreaterOrEqual(len(cfl), 1)

	// update
	updatedValue := "{\"test\": 234}"
	err2 := suite.db.UpdateAddonsConfigurationValue(context.TODO(), cf.ID, updatedValue)
	r.Nil(err2)
	cf2, err2 := suite.db.GetConfigurationByID(context.TODO(), cf.ID)
	r.Nil(err2)
	r.Equal(cf2.ConfigValue, updatedValue)

	// delete
	err3 := suite.db.DeleteAddonsConfiguration(context.TODO(), cf.ID)
	r.Nil(err3)
	_, err3 = suite.db.GetConfigurationByID(context.TODO(), cf.ID)
	r.Equal(true, errors.Is(err3, gorm.ErrRecordNotFound))
}

func (suite *AddonsTestSuite) TestInstalledAddons() {
	r := suite.Require()
	// create
	newAddonsInstalled, err := suite.db.InstallAddons(context.TODO(), orgID, uuid.MustParse(addonID), true)
	r.Nil(err)
	r.NotEmpty(newAddonsInstalled)
	r.Equal(newAddonsInstalled.Enabled, true)

	// install again
	_, err = suite.db.InstallAddons(context.TODO(), orgID, uuid.MustParse(addonID), true)
	r.Nil(err)

	// get by id
	ai, err := suite.db.GetAddOnsInstalledByID(context.TODO(), newAddonsInstalled.ID)
	r.Nil(err)
	r.NotEmpty(ai)
	r.Equal(newAddonsInstalled.ID, ai.ID)

	// list
	al, err := suite.db.ListInstalledAddons(context.TODO(), orgID)
	r.Nil(err)
	r.GreaterOrEqual(len(al), 1)

	// update
	err = suite.db.UpdateInstalledAddons(context.TODO(), newAddonsInstalled.ID, false)
	r.Nil(err)

	// update check
	ai, err = suite.db.GetAddOnsInstalledByID(context.TODO(), newAddonsInstalled.ID)
	r.Nil(err)
	r.Equal(ai.Enabled, false)
}

func (suite *AddonsTestSuite) TestAddonsInstalledNotExist() {
	r := suite.Require()
	adi, err := suite.db.GetAddOnsInstalledByID(context.TODO(), uuid.MustParse(emptyAddONID))
	r.Nil(adi)
	r.Condition(func() bool {
		return errors.Is(err, gorm.ErrRecordNotFound)
	})
}

func TestDatabaseAddonsSuite(t *testing.T) {
	suite.Run(t, new(AddonsTestSuite))
}
