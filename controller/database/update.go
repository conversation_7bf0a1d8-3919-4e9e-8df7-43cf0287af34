package database

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
	_ "github.com/jinzhu/gorm/dialects/postgres" // Initialize postgres dialect.

	"github.com/tigergraph/cloud-universe/utils/lock"
	"github.com/tigergraph/cloud-universe/utils/lock/consul"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

// TODO: Updates are run again if controller is redeployed. See TCE-692.
// UpdateDB is meant for 1-time db updates.
// Ex: we add a new field to a table and need update all records with correct value for that field.
// This function has to be cleared after the controller is deployed (and db is updated).
func (DB *Database) UpdateDB(lockSvc lock.DLockService) {
	log := logger.L()
	lock, err := lockSvc.NewLock("db-update/2022/10/release/quota-polling", true)
	if err != nil {
		log.Panic("Failed to update the database: failed to create lock")
		return
	}

	err = lock.Lock()
	if err != nil {
		if err == consul.ErrResourceLocked {
			return // Resource already locked means database has been updated. Ignore the error.
		}

		// If error is not ErrResourceLocked, it means something really failed.
		log.Panic("Failed to update the database: failed to lock the resource")
		return
	}

	if err := DB.InsertOnboardingTasks(); err != nil {
		log.Panic("Failed to update the database", err)
	}

}

func (DB *Database) InsertOnboardingTasks() error {
	db := DB.db

	onboardingTasks := []OnboardingTask{
		{
			TaskName:    "Sign up for TigerGraph Savanna",
			Description: "Create an account with TigerGraph Savanna to get started with your free trial.",
			Credits:     50,
			Index:       0,
		},
		{
			TaskName:    "Create a workspace",
			Description: "Set up your workspace and database to start building your graph analytics solutions.",
			Credits:     5,
			Index:       10,
		},
		{
			TaskName:    "Load data",
			Description: "Load data into your TigerGraph database to start running queries and building applications.",
			Credits:     5,
			Index:       20,
		},
		{
			TaskName:    "Invite a team member",
			Description: "Collaborate with your team by inviting a team member to your workspace.",
			Credits:     5,
			Index:       30,
		},
		{
			TaskName:    "Add payment method",
			Description: "Add a payment method to your account to continue using TigerGraph Savanna after your free trial ends.",
			Credits:     5,
			Index:       40,
		},
		{
			TaskName:    "Give feedback",
			Description: "Help us improve TigerGraph Savanna by providing feedback on your experience.",
			Credits:     5,
			Index:       50,
		},
	}

	var insertQuerySB strings.Builder
	insertQuerySB.WriteString("DO $$\nBEGIN\nIF (SELECT COUNT(*) FROM ONBOARDING_TASKS) = 0 THEN\n")
	for _, task := range onboardingTasks {
		insertQuerySB.WriteString(fmt.Sprintf("INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('%s', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'%s', '%s', %d, %d);\n", uuid.New().String(), task.TaskName, task.Description, task.Credits, task.Index))
	}
	insertQuerySB.WriteString("END IF;\nEND;\n$$")
	return db.Exec(insertQuerySB.String()).Error()
}
