package database

import (
	"time"

	"github.com/google/uuid"
	"github.com/jinzhu/gorm/dialects/postgres"
	utils "github.com/tigergraph/cloud-universe/utils/database"
	"gorm.io/gorm"
)

type UUIDBase struct {
	utils.UUIDBase
}

// User is a table type.
// A user in this table represents a organization in tgcloud.
type User struct {
	UUIDBase
	OrgID            *string `gorm:"unique;index;"`   // The OrgID claim in the ID token. not null constraint shall be applied after migration.
	Email            string  `gorm:"index;not null;"` // The org owner's email (for receiving invoice and creating stripe account).
	StripeCustomerID string
	Name             string
	AccountType      string
	Internal         bool
	Balance          int64 // In cents. Positive means the user owes money.
	LastBilled       *time.Time
	// Marked when real time balance turns positive and there is no card or when the default card failed to be charged
	RequiresCardSince            *time.Time
	ReceivedWarningEmail         *time.Time // Keeps track of the last time email notifying of solution termination was sent
	ReceivedFreeTierWarningEmail *time.Time // Keeps track of the last time email notifying of solution termination was sent
	IsGlobalChallengeUser        bool       // Marks users participating in the global challenge (hackathon) whose solutions should be terminated after longer inactivity period.
	IsBeingDeleted               bool       // Set to true between the deletion request is made and deletion is complete. Prevents other APIs being called.
}

// DeletedUser is a table for archiving deleted users.
type DeletedUser struct {
	UUIDBase
	StripeCustomerID string
	AccountType      string
	LastBilled       *time.Time
}

// UserActivity is a table type.
type UserActivity struct {
	gorm.Model
	UserID    uuid.UUID `gorm:"type:uuid;index;"`
	RequestID string
	Operator  string
	Type      string
	Message   string
	Details   postgres.Jsonb `gorm:"type:jsonb"`
}

// Log is used to store logs
type Log struct {
	gorm.Model
	Level   string
	Type    string
	Subject string
	Details postgres.Jsonb `gorm:"type:jsonb"`
}

type OnboardingTask struct {
	UUIDBase
	TaskName    string `gorm:"type:varchar(100)"`
	Description string
	Credits     int
	Index       int
}

type CompletedTask struct {
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time `sql:"index"`
	OrgID            string     `gorm:"type:string;primary_key"`
	OnboardingTaskID uuid.UUID  `gorm:"type:uuid;primary_key"`
	ReceivedCredits  int
}

type AddonsInstalled struct {
	UUIDBase
	Enabled  bool
	OrgID    string    `gorm:"type:string;size:256;uniqueIndex:unique_addons_installed_org_id_addons_id,where:deleted_at IS NULL"`
	AddonsID uuid.UUID `gorm:"type:uuid;uniqueIndex:unique_addons_installed_org_id_addons_id,where:deleted_at IS NULL"`
}

type AddonsConfiguration struct {
	UUIDBase
	AddonsInstalledID uuid.UUID `gorm:"type:uuid;index:idx_addons_installed_config_name,priority:1"`
	ConfigName        string    `gorm:"type:string;size:256;index:idx_addons_installed_config_name,priority:2"`
	ConfigValue       string    `gorm:"type:text"`
}

// Configuration is a table type. Store the configuration we need to hot update.
type Configuration struct {
	CreatedAt   time.Time
	UpdatedAt   time.Time
	ConfigKey   string
	ConfigValue postgres.Jsonb `gorm:"type:jsonb;not null"`
	Branch      string         `gorm:"default:main"`
}
