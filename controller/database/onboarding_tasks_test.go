package database_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"gopkg.in/DATA-DOG/go-sqlmock.v1"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	. "github.com/tigergraph/cloud-universe/controller/database"
)

type OnboardingTasksSuite struct {
	suite.Suite
	mockDB *sql.DB
	mock   sqlmock.Sqlmock
	db     *Database
}

func (s *OnboardingTasksSuite) SetupSuite() {
	var (
		gormDB *gorm.DB
		err    error
	)

	s.mockDB, s.mock, err = sqlmock.New()
	s.Require().NoError(err)

	gormDB, err = gorm.Open(postgres.New(postgres.Config{Conn: s.mockDB}))
	s.Require().NoError(err)

	db := &Database{}
	db = db.WithGormDB(gormDB)
	s.db = db
}

func (s *OnboardingTasksSuite) TearDownSuite() {
	s.mockDB.Close()
}

func (s *OnboardingTasksSuite) AfterTest(_ string, _ string) {
	s.Require().NoError(s.mock.ExpectationsWereMet())
}

func TestOnboardingTasks(t *testing.T) {
	suite.Run(t, new(OnboardingTasksSuite))
}

func (s *OnboardingTasksSuite) TestGetOnboardingTasks() {
	var (
		task1 = OnboardingTask{TaskName: "task1"}
		task2 = OnboardingTask{TaskName: "task2"}
	)
	task1.ID = uuid.New()
	task2.ID = uuid.New()

	sqlRows := sqlmock.NewRows([]string{"id", "task_name"}).
		AddRow(task1.ID.String(), task1.TaskName).
		AddRow(task2.ID.String(), task2.TaskName)
	s.mock.
		ExpectQuery(`^SELECT \* FROM \"onboarding_tasks\"`).
		WillReturnRows(sqlRows)

	tasks, err := s.db.GetOnboardingTasks(context.TODO())
	s.Require().NoError(err)
	s.Require().Len(tasks, 2)
	s.Require().Equal(task1.TaskName, tasks[0].TaskName)
	s.Require().Equal(task2.TaskName, tasks[1].TaskName)
}

func (s *OnboardingTasksSuite) TestAddCompletedTask() {
	var (
		taskID = uuid.New()
		task   = CompletedTask{OnboardingTaskID: taskID, OrgID: orgID, ReceivedCredits: 10}
	)

	s.mock.
		ExpectBegin()
	s.mock.
		ExpectExec(`^INSERT INTO \"completed_tasks\"`).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), orgID, taskID, 10).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.mock.
		ExpectCommit()

	called := false
	err := s.db.AddCompletedTask(context.TODO(), &task, func() error {
		called = true
		return nil
	})
	s.Require().NoError(err)
	s.Require().True(called)
}

func (s *OnboardingTasksSuite) TestAddCompletedTaskFailed() {
	var (
		taskID = uuid.New()
		task   = CompletedTask{OnboardingTaskID: taskID, OrgID: orgID, ReceivedCredits: 10}
	)

	s.mock.
		ExpectBegin()
	s.mock.
		ExpectExec(`^INSERT INTO \"completed_tasks\"`).
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), orgID, taskID, 10).
		WillReturnError(sql.ErrTxDone)
	s.mock.
		ExpectRollback()

	called := false
	err := s.db.AddCompletedTask(context.TODO(), &task, func() error {
		called = true
		return nil
	})
	s.Require().Error(err)
	s.Require().False(called)
}
