package common_test

import (
	"testing"

	"github.com/stretchr/testify/suite"
	. "github.com/tigergraph/cloud-universe/controller/common"
)

type TestSuite struct {
	suite.Suite
}

func Test(t *testing.T) {
	suite.Run(t, new(TestSuite))
}

func (s *TestSuite) TestAccessMode() {
	r := s.Require()

	// Test access removal.
	m := AccessModeDefaultPointer()

	r.<PERSON>als<PERSON>(m.HasPrivateAccess())
	r.True(m.HasPublicAccess())
	m.RemovePublicAccess()
	r.False(m.HasPrivateAccess())
	r.False(m.HasPublicAccess())

	// Test whether it affected the original value.
	m = AccessModeDefaultPointer()

	r.False(m.HasPrivateAccess())
	r.True(m.HasPublicAccess())

	// Test different modes.
	m2 := AccessModePrivate

	r.True(m2.HasPrivateAccess())
	r.False(m2.HasPublicAccess())
	m2.RemovePrivateAccess()
	r.<PERSON>als<PERSON>(m2.HasPrivateAccess())
	r.False(m2.HasPublicAccess())

	m2 = AccessModePublic
	r.False(m2.HasPrivateAccess())
	r.True(m2.HasPublicAccess())

	m2 = AccessModeNone
	r.False(m2.HasPrivateAccess())
	r.False(m2.HasPublicAccess())
	m2.AddPrivateAccess()
	r.True(m2.HasPrivateAccess())
	m2.AddPublicAccess()
	r.True(m2.HasPublicAccess())
	m2 = AccessModeNone
	r.False(m2.HasPrivateAccess())
	r.False(m2.HasPublicAccess())

	m2 = AccessModeBoth
	r.True(m2.HasPrivateAccess())
	r.True(m2.HasPublicAccess())

	var m3 *AccessMode
	r.False(m3.HasPrivateAccess())

	m3 = nil
	r.True(m3.HasPublicAccess()) // nil is treated as the default (public access only).

	m3 = nil
	m3.RemovePrivateAccess()

	m3 = nil
	m3.RemovePublicAccess()

	m3 = nil
	m3.AddPrivateAccess()

	m3 = nil
	m3.AddPublicAccess()
}
