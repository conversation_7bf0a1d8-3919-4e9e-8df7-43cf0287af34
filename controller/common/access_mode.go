package common

// Last bit defines presence of public access.
// Second to last bit defines presence of private access.
// Tip for memorizing the private/public order:
// Just like file permissions are alphabetically sorted, r(100) / w(010) / x(001),
// access permissions are private(10) / public(01).
type AccessMode int

// Note: modes MUST be constants so a pointer cannot point to them directly (could affect the value of the mode).
const (
	AccessModeNone    AccessMode = 0
	AccessModePublic  AccessMode = 1
	AccessModePrivate AccessMode = 2
	AccessModeBoth    AccessMode = 3
	AccessModeDefault            = AccessModePublic
)

// AccessModeDefaultPointer returns a pointer to a value equal to AccessModeDefault.
func AccessModeDefaultPointer() *AccessMode {
	m := AccessModeDefault
	return &m
}

func (m *AccessMode) HasPrivateAccess() bool {
	if m == nil {
		m = AccessModeDefaultPointer()
	}
	return *m&AccessModePrivate == AccessModePrivate
}

func (m *AccessMode) HasPublicAccess() bool {
	if m == nil {
		m = AccessModeDefaultPointer()
	}
	return *m&AccessModePublic == AccessModePublic
}

func (m *AccessMode) RemovePrivateAccess() {
	if m == nil {
		m = AccessModeDefaultPointer()
	}

	if m.HasPrivateAccess() {
		*m -= AccessModePrivate
	}
}

func (m *AccessMode) RemovePublicAccess() {
	if m == nil {
		m = AccessModeDefaultPointer()
	}

	if m.HasPublicAccess() {
		*m -= AccessModePublic
	}
}

func (m *AccessMode) AddPrivateAccess() {
	if m == nil {
		m = AccessModeDefaultPointer()
	}

	if !m.HasPrivateAccess() {
		*m += AccessModePrivate
	}
}

func (m *AccessMode) AddPublicAccess() {
	if m == nil {
		m = AccessModeDefaultPointer()
	}

	if !m.HasPublicAccess() {
		*m += AccessModePublic
	}
}
