package addons

import (
	"github.com/gin-gonic/gin"
	mv "github.com/tigergraph/cloud-universe/controller/middleware"
	aos "github.com/tigergraph/cloud-universe/controller/services/add_ons"
)

type AddonsMetaResponse struct {
	AddonsList    []aos.AddOnsMeta
	RequestAddons []aos.AddOnsMeta
}

// TODO: AddonsMetaResponse will cause error: [addonsList]: cannot find type definition: aos.AddOnsMeta

// Meta returns the metadata for add-ons.
//
//	@Summary		Get add-ons meta
//	@Description	Get metadata for add-ons.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	middleware.Response{Result=nil}
//	@Failure		400	{object}	middleware.Response
//	@Failure		500	{object}	middleware.Response
//	@Router			/controller/v4/v2/addons/meta [get]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) Meta(c *gin.Context) {
	addMeta := h.service.GetAddOns(c)
	reqMeta := h.service.GetRequestAddOns(c)

	mv.Reply(c, "Successfully got addons meta", AddonsMetaResponse{
		addMeta,
		reqMeta,
	})
}
