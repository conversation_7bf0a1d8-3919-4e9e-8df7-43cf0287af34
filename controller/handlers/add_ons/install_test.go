package addons_test

import (
	"bytes"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

const (
	aid = "79b4ed00-fdf8-4c78-a46b-fb611f74f4b9"
	cid = "2a0ff923-f9de-4c43-86b7-03038a39059b"
)

func TestUpdateInstalledAddons(t *testing.T) {
	testCases := []struct {
		caseName   string
		Enabled    bool
		WantStatus int
	}{
		{"enable-addons", true, http.StatusOK},
		{"disable-addons", false, http.StatusOK},
	}
	for _, tc := range testCases {
		t.Run(tc.caseName, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/api/v2/add-ons/install/%s/%t", aid, tc.Enabled), bytes.NewBuffer([]byte(fmt.Sprintf(`{"enabled":%t}`, tc.Enabled))))
			req.Header.Set("Content-Type", "application/json")
			w := MockServer(req)

			assert.Equal(t, w.Code, tc.WantStatus)
		})
	}
}

func TestGetAddonsInstalledByID(t *testing.T) {
	req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/api/v2/add-ons/installed/%s", aid), nil)
	req.Header.Set("Content-Type", "application/json")
	w := MockServer(req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestListInstalledAddons(t *testing.T) {
	req, _ := http.NewRequest(http.MethodGet, "/api/v2/add-ons/installed", nil)
	req.Header.Set("Content-Type", "application/json")
	w := MockServer(req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestNewUpdateInstalledAddons(t *testing.T) {
	testCases := []struct {
		caseName   string
		Enabled    string
		WantStatus int
	}{
		{"enable-addons", "undefined", http.StatusBadRequest},
		{"enable-addons", "true", http.StatusOK},
		{"disable-addons", "false", http.StatusOK},
	}
	for _, tc := range testCases {
		t.Run(tc.caseName, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodPut, fmt.Sprintf("/api/v2/addons/%s/install?enable=%s", aid, tc.Enabled), nil)
			req.Header.Set("Content-Type", "application/json")
			w := MockServer(req)

			assert.Equal(t, w.Code, tc.WantStatus)
		})
	}
}
