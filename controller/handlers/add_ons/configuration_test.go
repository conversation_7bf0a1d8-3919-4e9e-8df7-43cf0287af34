package addons_test

import (
	"bytes"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/controller/services/add_ons/constants"
)

func TestGetConfigList(t *testing.T) {
	testCases := []struct {
		caseName string
		wantCode int
		url      string
	}{
		{"bad-input", http.StatusBadRequest, fmt.Sprintf("/api/v2/add-ons/%s/configs/%s", "123", "config_name")},
		{"get-config", http.StatusOK, fmt.Sprintf("/api/v2/add-ons/%s/configs/%s", aid, "config_name")},
	}

	for _, tc := range testCases {
		// validate query
		req, _ := http.NewRequest(http.MethodGet, tc.url, nil)

		w := MockServer(req)

		assert.Equal(t, w.Code, tc.wantCode)
	}
}

func TestDeleteConfig(t *testing.T) {
	req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v2/add-ons/%s/configs/%s", aid, cid), nil)
	w := MockServer(req)
	assert.Equal(t, w.Code, http.StatusOK)
}

func TestCreateConfig(t *testing.T) {
	cfv := `{\"Name\":\"test\",\"Provider\":\"openai\",\"APIKey\":\"api_key\"}`
	bd := bytes.NewBuffer([]byte(fmt.Sprintf(`{"ConfigName": "%s", "ConfigValue": "%s"}`, constants.COPILOT_LLM_PROVIDERS_CONFIG_NAME, cfv)))
	req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/api/v2/add-ons/%s/configs", aid), bd)
	req.Header.Set("content-type", "application/json")

	w := MockServer(req)

	assert.Equal(t, w.Code, http.StatusOK)
}

func TestUpdateConfig(t *testing.T) {
	cfv := `{\"Name\":\"test\",\"Provider\":\"openai\",\"APIKey\":\"api_key\"}`
	req, _ := http.NewRequest(http.MethodPut, fmt.Sprintf("/api/v2/add-ons/%s/configs/%s", aid, cid), bytes.NewBuffer([]byte(fmt.Sprintf(`{"ConfigName":"copilot_llm_providers","ConfigValue": "%s"}`, cfv))))
	req.Header.Set("content-type", "application/json")

	w := MockServer(req)

	assert.Equal(t, w.Code, http.StatusOK)
}
