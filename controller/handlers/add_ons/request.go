package addons

import (
	"github.com/gin-gonic/gin"
	mw "github.com/tigergraph/cloud-universe/controller/middleware"
	"github.com/tigergraph/cloud-universe/utils/spreadsheet"
)

type RequestAddonRequest struct {
	Message   string `json:"message"`
	AddonName string `json:"addon_name"`
} //@name RequestAddonRequest

// RequestAddon handles the request to add an add-on.
//
//	@Summary		Request an add-on
//	@Description	Request an add-on by providing the addon name and a message.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Param			request	body		RequestAddonRequest					true	"Request body"
//	@Success		200		{object}	middleware.Response{Result=string}	"Successfully request addon."
//	@Failure		400		{object}	middleware.Response{Result=string}	"Invalid request body."
//	@Failure		500		{object}	middleware.Response{Result=string}	"Failed to request addon."
//	@Router			/controller/v4/v2/addons/request [post]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) RequestAddon(c *gin.Context) {
	orgID := mw.GetOrgID(c)
	orgName := mw.GetOrgName(c)
	email := mw.GetEmail(c)

	var req RequestAddonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Invalid request body: %v", err)
		return
	}

	row := spreadsheet.AddonRequestRow{
		Message:   req.Message,
		AddonName: req.AddonName,
		OrgId:     orgID,
		OrgName:   orgName,
		Email:     email,
	}
	err := h.googleSheetSvc.SendToAddonRequestSheet(c, row)
	if err != nil {
		mw.Abort(c, mw.ErrInternal, "Failed to request addon: %v", err)
		return
	}

	mw.Reply(c, "Successfully request addon.", nil)
}
