package addons

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/tigergraph/cloud-universe/controller/database"
	addon_srv "github.com/tigergraph/cloud-universe/controller/services/add_ons"
	audit "github.com/tigergraph/cloud-universe/observability/service/auditlog"
)

func TestAddonsHandler_InstallAddonAudit(t *testing.T) {
	type fields struct {
		service addon_srv.Service
	}
	type args struct {
		addonId string
		enable  string
	}

	addonId := uuid.New()
	tests := []struct {
		name              string
		fields            fields
		args              args
		wantCode          int
		wantAuditService  string
		wantAuditStatus   string
		wantAuditAction   string
		wantAuditTempalte string
		wantAuditValues   []interface{}
	}{
		{
			"invalid addonid param",
			fields{
				service: &mockAddonService{},
			},
			args{
				addonId: "test-cfg-id",
				enable:  "true",
			},
			http.StatusBadRequest,
			"",
			"",
			"",
			"",
			nil,
		},
		{
			"invalid enable param",
			fields{
				service: &mockAddonService{},
			},
			args{
				addonId: addonId.String(),
				enable:  "aaa",
			},
			http.StatusBadRequest,
			"",
			"",
			"",
			"",
			nil,
		},
		{
			"invalid body",
			fields{
				service: &mockAddonService{},
			},
			args{
				addonId: addonId.String(),
				enable:  "true",
			},
			http.StatusBadRequest,
			"",
			"",
			"",
			"",
			nil,
		},
		{
			"valid exist failed",
			fields{
				service: &mockAddonService{
					validCfgErr: errors.New("valid belong error"),
				},
			},
			args{
				addonId: addonId.String(),
				enable:  "true",
			},
			http.StatusBadRequest,
			"",
			"",
			"",
			"",
			nil,
		},
		{
			"enable failed",
			fields{
				service: &mockAddonService{
					installAddonErr: errors.New("error when install addons"),
					addonExist:      true,
				},
			},
			args{
				addonId: addonId.String(),
				enable:  "true",
			},
			http.StatusInternalServerError,
			"AddOn Operation",
			"Failed",
			"EnableAddOn",
			"Failed to enable add-on [%s].",
			[]interface{}{
				addonId.String(),
			},
		},
		{
			"enable success",
			fields{
				service: &mockAddonService{
					addonExist: true,
				},
			},
			args{
				addonId: addonId.String(),
				enable:  "true",
			},
			http.StatusOK,
			"AddOn Operation",
			"Success",
			"EnableAddOn",
			"Successfully enabled add-on [%s].",
			[]interface{}{
				addonId.String(),
			},
		},
		{
			"disable failed",
			fields{
				service: &mockAddonService{
					installAddonErr: errors.New("error when disable addons"),
					addonExist:      true,
				},
			},
			args{
				addonId: addonId.String(),
				enable:  "false",
			},
			http.StatusInternalServerError,
			"AddOn Operation",
			"Failed",
			"DisableAddOn",
			"Failed to disable add-on [%s].",
			[]interface{}{
				addonId.String(),
			},
		},
		{
			"disable success",
			fields{
				service: &mockAddonService{
					addonExist: true,
				},
			},
			args{
				addonId: addonId.String(),
				enable:  "false",
			},
			http.StatusOK,
			"AddOn Operation",
			"Success",
			"DisableAddOn",
			"Successfully disabled add-on [%s].",
			[]interface{}{
				addonId.String(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mal := &mockAuditLogger{}
			audit.ReplaceGlobal(mal)
			rr := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(rr)
			u, _ := url.Parse("http://localhost/v1/createAddonCfg?enable=" + tt.args.enable)

			c.Request = &http.Request{
				Method: "POST",
				Host:   "localhost",
				URL:    u,
			}

			handler := &AddonsHandler{
				service: tt.fields.service,
			}
			c.AddParam("addonsID", tt.args.addonId)
			handler.InstallAddons(c)
			assert.Equal(t, rr.Result().StatusCode, tt.wantCode)
			assert.Equal(t, mal.action, tt.wantAuditAction)
			assert.Equal(t, mal.service, tt.wantAuditService)
			assert.Equal(t, mal.status, tt.wantAuditStatus)
			assert.Equal(t, mal.template, tt.wantAuditTempalte)
			assert.Equal(t, mal.values, tt.wantAuditValues)
		})
	}
}

func (mas *mockAddonService) ValidateExistAddons(_ context.Context, addonsID string) bool {
	return mas.addonExist
}

func (mas *mockAddonService) InstallAddons(ctx context.Context, orgID string, addonsID uuid.UUID, enable bool) (*database.AddonsInstalled, error) {
	return &database.AddonsInstalled{}, mas.installAddonErr
}
