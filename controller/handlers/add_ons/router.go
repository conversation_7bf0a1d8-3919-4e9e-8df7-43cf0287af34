package addons

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cloud-universe/common/pb"
	ph "github.com/tigergraph/cloud-universe/controller/handlers/permission"
	addons "github.com/tigergraph/cloud-universe/controller/services/add_ons"
	"github.com/tigergraph/cloud-universe/tgIAM"
	"github.com/tigergraph/cloud-universe/utils/spreadsheet"
)

type AddonsHandler struct {
	service            addons.Service
	permissionHandler  ph.PermissionHandler
	googleSheetSvc     *spreadsheet.GoogleSheetService
	resourceManagerSvc pb.ResourceManager
}

func NewAddonsHandler(service addons.Service, permissionHandler ph.PermissionHandler, googleSheetSvc *spreadsheet.GoogleSheetService, resourceManager pb.ResourceManager) *AddonsHandler {
	return &AddonsHandler{
		service:            service,
		permissionHandler:  permissionHandler,
		googleSheetSvc:     googleSheetSvc,
		resourceManagerSvc: resourceManager,
	}
}

func (h *AddonsHandler) SetupRouter(r *gin.RouterGroup) {
	// for TCE-4297, TCE-4298
	// TODO: old api group for compatible, will be removed in the future
	apiAddOns := r.Group("/add-ons")
	{
		apiAddOns.GET("meta", h.Meta)

		apiAddOns.POST("request", h.RequestAddon)

		// will move `:enable` path param to request body
		apiAddOns.POST("install/:addonsID/:enable",
			h.permissionHandler.RequirePermsOnResource(tgIAM.IAMResourceTypeAddon,
				tgIAM.IAMResourceUpdate),
			h.InstallAddons)

		// TODO: will move this group under `/installed-addons`
		{
			apiAddOns.GET("installed", h.ListInstalledAddons)
			apiAddOns.GET("installed/:installedAddonsID", h.GetAddOnsInstalledByID)
			apiAddOns.GET(":installedAddonsID/configs/:configName", h.ListConfigurations)
			apiAddOns.POST(":installedAddonsID/configs",
				h.permissionHandler.RequirePermsOnResource(tgIAM.IAMResourceTypeAddon,
					tgIAM.IAMResourceCreate),
				h.CreateConfig)
			apiAddOns.DELETE(":installedAddonsID/configs/:configID",
				h.permissionHandler.RequirePermsOnResource(tgIAM.IAMResourceTypeAddon,
					tgIAM.IAMResourceDelete),
				h.DeleteConfig)
			apiAddOns.PUT(":installedAddonsID/configs/:configID",
				h.permissionHandler.RequirePermsOnResource(tgIAM.IAMResourceTypeAddon,
					tgIAM.IAMResourceUpdate),
				h.UpdateConfig)
		}
	}

	newAddonsAPI := r.Group("/addons")
	{
		newAddonsAPI.GET("meta", h.Meta)

		// for TCE-4297
		// Change POST /controller/v2/add-ons/install/:addonsID/:enable to be PUT /controller/v2/add-ons/install/:addonsID?enable=true|false
		// To enhance semantic clarity, it's better to use `PUT` method instead of `POST`
		newAddonsAPI.PUT(":addonsID/install",
			h.permissionHandler.RequirePermsOnResource(tgIAM.IAMResourceTypeAddon,
				tgIAM.IAMResourceUpdate),
			h.InstallAddons)

		newAddonsAPI.POST("request", h.RequestAddon)

		newAddonsAPI.GET(":addonsID/workspaces", h.ListWorkspacesByAddonsID)
	}

	newInstalledAddonsAPI := r.Group("/installed-addons")
	{
		newInstalledAddonsAPI.GET("", h.ListInstalledAddons)
		newInstalledAddonsAPI.GET(":installedAddonsID", h.GetAddOnsInstalledByID)
		newInstalledAddonsAPI.GET(":installedAddonsID/configs/:configName", h.ListConfigurations)
		// TODO: TCE-4298 for config, will need new API for
	}
}
