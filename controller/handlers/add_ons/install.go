package addons

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	mw "github.com/tigergraph/cloud-universe/controller/middleware"
	audit "github.com/tigergraph/cloud-universe/observability/service/auditlog"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

// TODO: database.AddonsInstalled will cause error: database.AddonsInstalled: UUIDBase: cannot find type definition: utils.UUIDBase

// InstallAddons installs an add-on.
//
//	@Summary		Installs an add-on
//	@Description	Installs an add-on by ID.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Param			addonsID	path		string	true	"Add-on ID"
//	@Param			enable		query		string	true	"Enable or disable add-on"
//	@Success		200			{object}	middleware.Response{Result=nil}
//	@Failure		400			{object}	middleware.Response
//	@Failure		500			{object}	middleware.Response
//	@Router			/controller/v4/v2/addons/{addonsID}/install [post]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) InstallAddons(c *gin.Context) {
	log := logger.L().WithContext(c)
	id := c.Param("addonsID")

	enableParam := c.Param("enable")
	if enableParam == "" {
		enableParam = c.Query("enable")
	}
	enable, err := strconv.ParseBool(enableParam)
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Failed to parse enable query params.")
		return
	}

	addonsID, err := uuid.Parse(id)
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Failed to parse addons_id")
		return
	}

	if !h.service.ValidateExistAddons(c, id) {
		mw.Abort(c, mw.ErrBadRequest, "Non exist addons.")
		return
	}

	action := actionEnableAddOn
	successAuditTemplate := "Successfully enabled add-on [%s]."
	failAuditTemplate := "Failed to enable add-on [%s]."
	if !enable {
		action = actionDisableAddOn
		successAuditTemplate = "Successfully disabled add-on [%s]."
		failAuditTemplate = "Failed to disable add-on [%s]."
	}

	orgID := mw.GetOrgID(c)
	al := audit.L().WithContext(c).WithService(auditServiceName)
	ado, err := h.service.InstallAddons(c, orgID, addonsID, enable)
	if err != nil {
		log.Errorf("install addons error: %s", err)
		al.Failf(action, failAuditTemplate, id)
		mw.Abort(c, mw.ErrInternal, "Failed to get install addons.")
		return
	}

	al.Successf(action, successAuditTemplate, id)
	if enable {
		mw.Reply(c, "Successfully enabled add-on.", ado)
	} else {
		mw.Reply(c, "Successfully disabled add-on.", ado)
	}
}

// ListInstalledAddons list all installed add-ons.
//
//	@Summary		List all installed add-ons
//	@Description	List all installed add-ons for the organization.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	middleware.Response{Result=nil}
//	@Failure		500	{object}	middleware.Response
//	@Router			/controller/v4/v2/installed-addons [get]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) ListInstalledAddons(c *gin.Context) {
	log := logger.L().WithContext(c)
	orgID := mw.GetOrgID(c)

	ia, err := h.service.ListInstalledAddons(c, orgID)
	if err != nil {
		log.Errorf("get installed addons error: %s", err)
		mw.Abort(c, mw.ErrInternal, "Failed to get all installed addons.")
		return
	}

	mw.Reply(c, "Successfully get installed addons.", ia)
}

// GetAddOnsInstalledByID gets an installed add-on.
//
//	@Summary		Get installed add-on by ID
//	@Description	Get details of an installed add-on by its ID.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Param			installedAddonsID	path		string	true	"Installed Add-on ID"
//	@Success		200					{object}	middleware.Response{Result=nil}
//	@Failure		400					{object}	middleware.Response
//	@Failure		500					{object}	middleware.Response
//	@Router			/controller/v4/v2/installed-addons/{installedAddonsID} [get]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) GetAddOnsInstalledByID(c *gin.Context) {
	log := logger.L().WithContext(c)
	installedAddonsID, err := uuid.Parse(c.Param("installedAddonsID"))
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Failed to parse id")
		return
	}

	orgID := mw.GetOrgID(c)
	_, err = h.service.ValidateInstalledAddonsBelongsTo(c, installedAddonsID, orgID)
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Failed to update installed addons, addons do not belong to this org.")
		return
	}

	ado, err := h.service.GetAddOnsInstalledByID(c, installedAddonsID)
	if err != nil {
		log.Errorf("failed to get addons by id error: %s", err)
		mw.Abort(c, mw.ErrInternal, "Failed to update installed addons, addons do not belong to this org.")
		return
	}

	mw.Reply(c, "Successfully get installed addons by id.", ado)
}
