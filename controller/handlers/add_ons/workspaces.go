package addons

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	mw "github.com/tigergraph/cloud-universe/controller/middleware"
	"github.com/tigergraph/cloud-universe/utils/logger"
)

// ListWorkspacesByAddonsID godoc
//
//	@Summary		List Workspaces by add-ons ID
//	@Description	Retrieves a list of workspaces associated with a specific addons ID.
//	@Tags			Add-ons
//	@Accept			json
//	@Produce		json
//	@Param			addonsID	path		string	true	"Addons ID"
//	@Success		200			{object}	middleware.Response{Result=[]pb.Workspace}
//	@Failure		400			{object}	middleware.Response{}
//	@Failure		500			{object}	middleware.Response{}
//	@Router			/controller/v4/v2/addons/{addonsID}/workspaces [get]
//	@Security		ApiKeyAuth
func (h *AddonsHandler) ListWorkspacesByAddonsID(c *gin.Context) {
	log := logger.L().WithContext(c)
	addonsID, err := uuid.Parse(c.Param("addonsID"))
	if err != nil {
		mw.Abort(c, mw.ErrBadRequest, "Invalid addons id")
		return
	}

	result, err := h.resourceManagerSvc.ListWorkspacesByAddonsID(c, addonsID)
	if err != nil {
		log.Errorf("resourceManagerSvc.ListWorkspacesByAddonsID err: %s", err)
		mw.Abort(c, mw.ErrInternal, "Failed to list workspaces by addonsID.")
		return
	}

	mw.Reply(c, "Successfully list workspaces by addonsID.", result)
}
