package addons_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"testing"
)

func TestAddFeedback(t *testing.T) {
	// test rowData is not an array
	reqBody, _ := json.Marshal(map[string]interface{}{"message": 1})
	req, _ := http.NewRequest(http.MethodPost, "/api/v2/add-ons/request", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	w := MockServer(req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status code %d but got %d", http.StatusBadRequest, w.Code)
	}
}
