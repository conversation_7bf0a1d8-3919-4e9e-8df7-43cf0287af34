package configurationresponse

import (
	"encoding/json"

	"github.com/tigergraph/cloud-universe/controller/database"
	"github.com/tigergraph/cloud-universe/controller/services/add_ons/constants"
)

type CopilotConfigResponseValue struct {
	Name     string
	Provider string
	APIKey   string
}

type ConfigMarshal interface {
	Unmarshal(string) error
	Marshal() (string, error)
}

func (co *CopilotConfigResponseValue) Unmarshal(data string) error {
	return json.Unmarshal([]byte(data), co)
}

func (co *CopilotConfigResponseValue) Marshal() (string, error) {
	bs, err := json.Marshal(co)
	return string(bs), err
}

func SerilizeConfig(configName string, configValue string) (string, error) {
	var cs ConfigMarshal
	switch configName {
	case constants.COPILOT_LLM_PROVIDERS_CONFIG_NAME:
		cs = &CopilotConfigResponseValue{}
		if err := cs.Unmarshal(configValue); err != nil {
			return "", err
		}
		return cs.Marshal()
	}

	return configValue, nil
}

func SerilizeConfigList(configName string, configList []database.AddonsConfiguration) error {
	for i, c := range configList {
		newConfigValue, err := SerilizeConfig(c.ConfigName, c.ConfigValue)
		if err != nil {
			return err
		}
		configList[i].ConfigValue = newConfigValue
	}

	return nil
}
