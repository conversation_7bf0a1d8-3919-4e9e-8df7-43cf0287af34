package addons_test

import (
	"net/http"
	"net/http/httptest"

	"github.com/gin-gonic/gin"
	addons "github.com/tigergraph/cloud-universe/controller/handlers/add_ons"
	permission_handler "github.com/tigergraph/cloud-universe/controller/handlers/permission"
	"github.com/tigergraph/cloud-universe/controller/middleware"
	"github.com/tigergraph/cloud-universe/controller/services/add_ons/mock"
	"github.com/tigergraph/cloud-universe/tgIAM"
)

func setMockAuthContextMiddleware(c *gin.Context) {
	c.Set(middleware.CtxKeyAuthContext, middleware.AuthContext{
		OrgID: "test_org_id",
	})
}

func MockServer(req *http.Request) *httptest.ResponseRecorder {
	gin.SetMode(gin.TestMode)
	service := mock.NewMockService()
	permissionHandler := permission_handler.MockPermission{}

	permissionHandler.On("RequirePermsOnResource", tgIAM.IAMResourceTypeAddon, []tgIAM.Action{tgIAM.IAMResourceRead}).Return(gin.HandlerFunc(func(c *gin.Context) {}))
	permissionHandler.On("RequirePermsOnResource", tgIAM.IAMResourceTypeAddon, []tgIAM.Action{tgIAM.IAMResourceUpdate}).Return(gin.HandlerFunc(func(c *gin.Context) {}))
	permissionHandler.On("RequirePermsOnResource", tgIAM.IAMResourceTypeAddon, []tgIAM.Action{tgIAM.IAMResourceCreate}).Return(gin.HandlerFunc(func(c *gin.Context) {}))
	permissionHandler.On("RequirePermsOnResource", tgIAM.IAMResourceTypeAddon, []tgIAM.Action{tgIAM.IAMResourceDelete}).Return(gin.HandlerFunc(func(c *gin.Context) {}))

	w := httptest.NewRecorder()
	h := addons.NewAddonsHandler(service, &permissionHandler, nil, nil)
	r := gin.Default()
	rg := r.Group("/api/v2")
	rg.Use(setMockAuthContextMiddleware)

	h.SetupRouter(rg)
	r.ServeHTTP(w, req)

	return w
}
